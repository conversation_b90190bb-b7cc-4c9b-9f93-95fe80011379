package com.cxrry.biz.sales.operation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cxrry.biz.common.mq.ViolationCustomerMatchesRecordsConstant;
import com.cxrry.biz.common.utils.DateUtils;
import com.cxrry.biz.common.utils.MqUtil;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.sales.operation.config.AccessCycleConfig;
import com.cxrry.biz.sales.operation.domain.CxrEmployee;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecord;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordEx;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordLog;
import com.cxrry.biz.sales.operation.domain.SalesDateCycle;
import com.cxrry.biz.sales.operation.domain.SalesLevelChangeRecord;
import com.cxrry.biz.sales.operation.domain.bo.AccessEditSettingBo;
import com.cxrry.biz.sales.operation.domain.bo.AccessSettingBo;
import com.cxrry.biz.sales.operation.domain.bo.AccessSettingMultipleBo;
import com.cxrry.biz.sales.operation.domain.bo.CxrEmployeeAccessCycleRecordBo;
import com.cxrry.biz.sales.operation.domain.dto.DelayCalculationResult;
import com.cxrry.biz.sales.operation.domain.dto.StageDTO;
import com.cxrry.biz.sales.operation.domain.dto.ViolationCustomerMatchesRecordsAddDTO;
import com.cxrry.biz.sales.operation.domain.vo.AccessEditSettingVo;
import com.cxrry.biz.sales.operation.enums.cycle.AccessCycleTypeEnum;
import com.cxrry.biz.sales.operation.enums.cycle.AchievementStatusEnum;
import com.cxrry.biz.sales.operation.enums.cycle.AdjustSourceTypeEnum;
import com.cxrry.biz.sales.operation.enums.cycle.AssStatusEndNoteEnum;
import com.cxrry.biz.sales.operation.enums.cycle.ChangeLevelSource;
import com.cxrry.biz.sales.operation.enums.cycle.ChangeTypeEnum;
import com.cxrry.biz.sales.operation.enums.cycle.EmployeeStageEnum;
import com.cxrry.biz.sales.operation.enums.cycle.ExecutionStatusEnum;
import com.cxrry.biz.sales.operation.enums.cycle.NewRuleEndFlag;
import com.cxrry.biz.sales.operation.enums.cycle.RunStatusEnum;
import com.cxrry.biz.sales.operation.handler.AccessStageHandler;
import com.cxrry.biz.sales.operation.manager.AccessCycleManager;
import com.cxrry.biz.sales.operation.manager.CycleDelayManager;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeAccessCycleRecordMapper;
import com.cxrry.biz.sales.operation.mapper.CxrSiteMapper;
import com.cxrry.biz.sales.operation.mapper.CxrStopMilkMapper;
import com.cxrry.biz.sales.operation.mapper.SysDeptMapper;
import com.cxrry.biz.sales.operation.service.AccessCycleCondition;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordExService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordLogService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeService;
import com.cxrry.biz.sales.operation.service.RuleConditionService;
import com.cxrry.biz.sales.operation.service.RuleSetDeptService;
import com.cxrry.biz.sales.operation.service.RuleSetService;
import com.cxrry.biz.sales.operation.service.SalesDateCycleService;
import com.cxrry.biz.sales.operation.service.SalesLevelChangeRecordService;
import com.cxrry.biz.sales.operation.utils.DateRangeCalculator;
import com.ruoyi.common.core.constant.UserConstants;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.satoken.utils.LoginUtil;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.model.LoginUser;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【cxr_employee_access_cycle_record】的数据库操作Service实现
* @createDate 2024-08-14 20:38:56
*/
@RefreshScope
@Slf4j
@Service
public class CxrEmployeeAccessCycleRecordServiceImpl extends ServiceImpl<CxrEmployeeAccessCycleRecordMapper, CxrEmployeeAccessCycleRecord>
    implements CxrEmployeeAccessCycleRecordService{
	@Value("${sales.divide.achievementRatio.ruleSetCode:DJSJ}")
	private String ruleSetCode;
	@Autowired
	private CxrEmployeeService cxrEmployeeService;
	@Autowired
	private CxrSiteMapper cxrSiteMapper;
	@Autowired
	private SysDeptMapper sysDeptMapper;
	@Autowired
	private AccessStageHandler accessStageHandler;
	@Autowired
	private RuleSetService ruleSetService;
	@Autowired
	private RuleSetDeptService ruleSetDeptService;
	@Autowired
	private RuleConditionService ruleConditionService;
	@Lazy
	@Autowired
	private SalesDateCycleService salesDateCycleService ;
	@Autowired
	private  AccessCycleConfig accessCycleConfig;

	@Lazy
	@Autowired
	private AccessCycleManager accessCycleManager;
	@Autowired
	private  CxrEmployeeAccessCycleRecordService accessCycleRecordService;
	@Autowired
	private  CxrEmployeeAccessCycleRecordExService cxrEmployeeAccessCycleRecordExService;
	@Autowired
	private CxrEmployeeAccessCycleRecordLogService accessCycleRecordLogService;
	@Autowired
	private SalesLevelChangeRecordService salesLevelChangeRecordService;
	@Autowired
	private  CxrStopMilkMapper stopMilkMapper;
	@Autowired
	private MqUtil mqUtil;
	@Autowired
	private CycleDelayManager cycleDelayManager;


	public static final long INVALID_SITE_ID = -1L; // 表示无效站点ID

	public static final Integer INVALID_MAX= 999999; // 表示无效站点ID

	@Override
	public List<CxrEmployeeAccessCycleRecord> getInProgress(Long employeeId){
		return this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.orderByDesc(CxrEmployeeAccessCycleRecord::getId)
		);
	}
	@Override
	public List<CxrEmployeeAccessCycleRecord> getInProgressAndNoStart(Long employeeId){
		return this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(),RunStatusEnum.NOT_STARTED.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.orderByDesc(CxrEmployeeAccessCycleRecord::getId)
		);
	}


	@Override
	public List<CxrEmployeeAccessCycleRecord> getInProgressBy(Long employeeId, Integer currentLevel) {
		if(currentLevel == null){
			CxrEmployee cxrEmployee = cxrEmployeeService.getById(employeeId);
			currentLevel = Objects.nonNull(cxrEmployee)?Integer.parseInt(cxrEmployee.getEmployeeLevelType()):null;
		}
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(Objects.nonNull(currentLevel), CxrEmployeeAccessCycleRecord::getCurrentLevel, currentLevel)
				.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.orderByDesc(CxrEmployeeAccessCycleRecord::getId)
		);
		log.info("\n查询销售代理[{}]当前等级为【L{}】正在进行中的考核周期数:{}个",employeeId,currentLevel,list.size());
		return list;
	}

	@Override
	@DSTransactional
	public Boolean levelChangeAction(Date actionDate,CxrEmployeeAccessCycleRecord record) {
		cxrEmployeeService.levelChange(record.getCxrEmployeeId(),actionDate,record.getCurrentLevel(),record.getTargetLevel(),
				record.getId(), ChangeLevelSource.AUTO_UP_DOWN);
		boolean update = this.update(new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getId, record.getId())
				.eq(CxrEmployeeAccessCycleRecord::getExecutionStatus, ExecutionStatusEnum.NOT_EXECUTED.getCode())
				.set(CxrEmployeeAccessCycleRecord::getExecutionStatus, ExecutionStatusEnum.EXECUTED.getCode())
				.set(CxrEmployeeAccessCycleRecord::getUpdateTime,new Date())
		);
		mqUtil.sendSyncMessage(ViolationCustomerMatchesRecordsConstant.VIOLATION_CUSTOMER_MATCHES_RECORDS_TOPIC,
				ViolationCustomerMatchesRecordsConstant.VIOLATION_CUSTOMER_MATCHES_RECORDS_TAG,
				JSONUtil.toJsonStr(
						ViolationCustomerMatchesRecordsAddDTO.builder().triggerType(2).time(new Date()).cxrEmployeeAccessCycleRecordId(record.getId()).build()));
		return update;
	}



	@Override
	@DSTransactional
	public void addAccessCycle(CxrEmployeeAccessCycleRecord record) {
		//如果阶段存在阶段&日期一样进行中的，则拒绝
		long count = this.count(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, record.getCxrEmployeeId())
						.eq(CxrEmployeeAccessCycleRecord::getStage, record.getStage())
						.eq(CxrEmployeeAccessCycleRecord::getRuleConditionGroupId, record.getRuleConditionGroupId())
						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(),
								RunStatusEnum.NOT_STARTED.getCode())
						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
		);
		if(count>0){
			log.info("当前阶段存在进行中的周期,拒绝插入,id{},阶段{}",record.getId(),record.getStage());
			return;
		}
		record.setCreateBy(1L);
		record.setCreateTime(new Date());
		record.setCreateByName(LoginUtil.getLoginUser().getUserName());
		record.setCreateByType(LoginUtil.getLoginUser().getUserType());
		//生成阶段前，判断是否有过调整记录或者请假记录
		this.autoDelayDays(record);
		this.baseMapper.insert(record);
		cxrEmployeeAccessCycleRecordExService.addEmployeeAccessCycleRecordEx(record.getCxrEmployeeId(),record.getId());
	}



	/**
	 *针对请假、调整记录补充天数和日期
	 * 只针对天类型的
	 */
	@Override
	public Integer autoDelayDays(CxrEmployeeAccessCycleRecord record){
		if(!record.getCycleType().equals(AccessCycleTypeEnum.BY_DAY.getCode())){
			return 0;
		}

		if (!record.getOriginalAssessmentEndDate().equals(record.getActualAssessmentEndDate()) ||
				!record.getOriginalAssessmentStartDate().equals(record.getActualAssessmentStartDate())){
			return 0;
		}
//		/**
//		 *按天的，按照调整的时间，每个周期生成的时候根据调整的时间断自动增补
//		 *    1. 判断生成的日期内是否涵盖了请假日期。（交集部分的天数，延续结束日期，如果是按月的延续的时间导致该月不足设置天数，自动延一个月，否则不改变）
//		 */
//		//先查询是否存在相同日期相同阶段的记录，有就直接原封不动覆盖
//		CxrEmployeeAccessCycleRecordLog existRecordLog = accessCycleRecordLogService.getOne(
//				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordLog>()
//						.eq(CxrEmployeeAccessCycleRecordLog::getEmployeeId, record.getCxrEmployeeId())
//						.eq(CxrEmployeeAccessCycleRecordLog::getStage, record.getStage())
//						.eq(CxrEmployeeAccessCycleRecordLog::getOriginalAssessmentStartDate, record.getActualAssessmentStartDate())
//						.eq(CxrEmployeeAccessCycleRecordLog::getOriginalAssessmentEndDate, record.getActualAssessmentEndDate())
//						.orderByDesc(CxrEmployeeAccessCycleRecordLog::getId)
//						.last("limit 1")
//		);
//		if(existRecordLog!=null){
//			record.setActualAssessmentStartDate(DateUtils.convertLocalDateToDate(existRecordLog.getEditLastAssessmentStartDate()));
//			record.setActualAssessmentEndDate(DateUtils.convertLocalDateToDate(existRecordLog.getEditLastAssessmentEndDate()));
//			record.setUpdateCycleReason(existRecordLog.getUpdateCycleReason());
////			return 0;  //允许同时请假和放假
//		}
//		List<CxrEmployeeAccessCycleRecordLog> adjustLogList = accessCycleRecordLogService.getAdjustLogList(
//				record.getCxrEmployeeId(), record.getActualAssessmentStartDate(),
//				record.getActualAssessmentEndDate(),record.getStage());
//		if (CollectionUtil.isEmpty(adjustLogList))return 0;
		int extendDays = 0;//要延长的天数，取日期的交集
//		String extendDaysStr = "";//延长原因&天数
//
//		for (CxrEmployeeAccessCycleRecordLog accessCycleRecordLog : adjustLogList) {
//			Date[] ranges = DateUtils.intersectRanges(DateUtils.convertLocalDateToDate(accessCycleRecordLog.getAdjustStartDate()),
//					DateUtils.convertLocalDateToDate(accessCycleRecordLog.getAdjustEndDate()),
//					record.getActualAssessmentStartDate(), record.getActualAssessmentEndDate());
//			if(ranges==null)continue;
//			Date rangeStartDate = ranges[0];
//			Date rangeEndDate = ranges[1];
//			extendDays += DateUtil.betweenDay(rangeStartDate, rangeEndDate,false)+1;
//			extendDaysStr =
//					extendDaysStr+StrUtil.format("{}至{}{},共{}天" ,DateUtils.format(rangeStartDate),DateUtils.format(rangeEndDate)
//							,accessCycleRecordLog.getUpdateCycleReason()
//							,extendDays);
//		}
		DelayCalculationResult delayResult = cycleDelayManager.autoProcessDelayWithResult(record);
		extendDays = delayResult.getDelayDays();
		if(extendDays==0)return 0;
		record.setActualAssessmentEndDate(delayResult.getNewEndDate());
		record.setUpdateCycleReason(delayResult.getDelayProcessDescription());
//		List<Long> logIds = adjustLogList.stream().map(CxrEmployeeAccessCycleRecordLog::getId)
//				.collect(Collectors.toList());
//		accessCycleRecordLogService.update(null, new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecordLog>()
//				.set(CxrEmployeeAccessCycleRecordLog::getAccessCycleId, record.getId())
//				.in(CxrEmployeeAccessCycleRecordLog::getId,logIds)
//		);
		return extendDays;
	}

	/**
	 * 自动延期一个月
	 * 只针对月类型的
	 */
	@Override
	public Integer autoDelayMonth(CxrEmployeeAccessCycleRecord record) {
		if(!record.getCycleType().equals(AccessCycleTypeEnum.BY_CONSECUTIVE_MONTHS.getCode())&&!record.getCycleType().equals(AccessCycleTypeEnum.BY_NATURAL_MONTH.getCode())){
			return 0;
		}
		AccessCycleCondition accessCycleCondition = ruleConditionService.getAccessCycleConditionByGroupId(
				record.getRuleConditionGroupId());
		if(accessCycleCondition==null)return 0;

		List<CxrEmployeeAccessCycleRecordLog> adjustLogList = accessCycleRecordLogService.getAdjustLogList(
				record.getCxrEmployeeId(), record.getActualAssessmentStartDate(),
				record.getActualAssessmentEndDate(),record.getStage());
		if (CollectionUtil.isEmpty(adjustLogList))return 0;

		DateRangeCalculator dateRangeCalculator = new DateRangeCalculator();
		for (CxrEmployeeAccessCycleRecordLog accessCycleRecordLog : adjustLogList) {
			Date[] ranges = DateUtils.intersectRanges(DateUtils.convertLocalDateToDate(accessCycleRecordLog.getAdjustStartDate()),
					DateUtils.convertLocalDateToDate(accessCycleRecordLog.getAdjustEndDate()),
					record.getActualAssessmentStartDate(), record.getActualAssessmentEndDate());
			if(ranges==null)continue;
			Date rangeStartDate = ranges[0];
			Date rangeEndDate = ranges[1];
			dateRangeCalculator.addDateRange(rangeStartDate,rangeEndDate);
		}
//		DelayCalculationResult delayCalculationResult = cycleDelayManager.autoProcessDelayWithResult(record);
//		int autoDelayOneMonth = 0;
		int autoDelayOneMonth = accessCycleCondition.autoDelayOneMonth(dateRangeCalculator.getDays());
		if(autoDelayOneMonth==0)return 0;
		//要补一个月
		record.setActualAssessmentEndDate( DateUtil.endOfMonth(DateUtil.offsetMonth(record.getOriginalAssessmentEndDate(),
				autoDelayOneMonth)).toSqlDate());
		record.setUpdateCycleReason(StrUtil.format("工作天数不足，自动延长{}个月;", autoDelayOneMonth));
		if(record.getId()!=null){
			boolean update = this.update(
					new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
							.eq(CxrEmployeeAccessCycleRecord::getId, record.getId())
							.set(CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate, record.getActualAssessmentEndDate())
							.set(CxrEmployeeAccessCycleRecord::getUpdateCycleReason, record.getUpdateCycleReason())
			);
//			List<Long> logIds = adjustLogList.stream().map(CxrEmployeeAccessCycleRecordLog::getId).collect(Collectors.toList());
//			accessCycleRecordLogService.update(null,new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecordLog>()
//							.set(CxrEmployeeAccessCycleRecordLog::getHandCycleRecordId, record.getId())
//							.in(CxrEmployeeAccessCycleRecordLog::getId, logIds)
//					);
		}
		return autoDelayOneMonth;
	}



	/**
	 * 结束进行中的
	 * @param employeeId
	 * @param nowDateTime
	 * @param cycleId
	 * @param srcStage
	 * @param assStatusEndNote
	 */
	@Override
	public void endInProgress(Long employeeId, Date nowDateTime, Long cycleId,Integer srcStage,
			AssStatusEndNoteEnum assStatusEndNote){
		String endNote = assStatusEndNote.getDescription();
		if(srcStage != null){
			endNote= EmployeeStageEnum.fromSequence(srcStage).getDescription()+endNote;
		}
		this.endInProgress(employeeId, nowDateTime, cycleId,srcStage,assStatusEndNote.getDescription());
	}

	@Override
	public void endInProgress(Long employeeId, Date nowDateTime, Long cycleId, Integer srcStage,
			String assStatusEndNote){
		this.endInProgress(employeeId, nowDateTime, cycleId,srcStage,assStatusEndNote,null,false);
	}

	@Override
	public void endInProgress(Long employeeId, Date nowDateTime, Long cycleId, Integer srcStage,
			String assStatusEndNote,Integer newRuleEndFlag,boolean deleteFlag){
		if(cycleId!=null){
			assStatusEndNote+="，触发的周期ID："+cycleId;
		}
		boolean delete = false;
		if(assStatusEndNote.equals(AssStatusEndNoteEnum.MANUAL_MODIFY.getDescription())||deleteFlag){
			delete = true;
		}
		this.update(
				new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(ObjectUtil.isNotNull(cycleId),CxrEmployeeAccessCycleRecord::getId,cycleId)
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId,employeeId)
						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(),
								RunStatusEnum.NOT_STARTED.getCode())
						.ne(ObjectUtil.isNotNull(srcStage),CxrEmployeeAccessCycleRecord::getAdvanceSrcStage,srcStage)  //不结束由自己阶段提前生成来的周期
						.set(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.ENDED.getCode())
						.set(CxrEmployeeAccessCycleRecord::getAssStatusEndNote,assStatusEndNote)
						.set(CxrEmployeeAccessCycleRecord::getAssStatusEndDate,new Date())
						.set(ObjectUtil.isNotNull(newRuleEndFlag),CxrEmployeeAccessCycleRecord::getNewRuleEndFlag,newRuleEndFlag)
						.set(delete,CxrEmployeeAccessCycleRecord::getDeleteStatus,DeleteStatus.DELETED.getValue())
						.set(delete,CxrEmployeeAccessCycleRecord::getDeleteByType,assStatusEndNote)
		);
	}

	@Override
	public Long countInStageProgress(Long employeeId, Integer stage,Date accessStartDate) {
		return this.count(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId,employeeId)
						.eq(CxrEmployeeAccessCycleRecord::getStage, stage)
						.eq(CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate, accessStartDate)
						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(),
								RunStatusEnum.NOT_STARTED.getCode())
						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
		);
	}

	/**
	 * 更改待开始的周期为进行中
	 */
	@Override
	public void startToBegin(CxrEmployeeAccessCycleRecord record, Date nowDateTime) {
		log.info("{}开始更改当前周期{}为进行中",record.getCxrEmployeeId(),record.getStage());
		this.baseMapper.update(null,
				new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(CxrEmployeeAccessCycleRecord::getId,record.getId())
						.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.NOT_STARTED.getCode())
						.set(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
						.set(CxrEmployeeAccessCycleRecord::getAssStatusStartDate,new Date())
		);
	}

	@Override
	public PageTableDataInfo<AccessEditSettingVo> accessSettingPage(AccessEditSettingBo bo) {
		PageQuery pageQuery = BeanUtil.copyProperties(bo, PageQuery.class);
		accessSettingPageParams(bo);


		IPage<AccessEditSettingVo> page =  cxrEmployeeService.accessSettingPage(bo,pageQuery.build());
		List<AccessEditSettingVo> records = page.getRecords();
		if (CollUtil.isNotEmpty(records)){
			List<Long> employeeIds = records.stream().map(AccessEditSettingVo::getEmployeeId).collect(Collectors.toList());
			List<CxrEmployeeAccessCycleRecord> accessCycleRecords = this.getBaseMapper().selectList(
					new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
							.select(CxrEmployeeAccessCycleRecord::getId,CxrEmployeeAccessCycleRecord::getCxrEmployeeId,
									CxrEmployeeAccessCycleRecord::getStage,
									CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate,
									CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate,
									CxrEmployeeAccessCycleRecord::getCycleType,
									CxrEmployeeAccessCycleRecord::getOriginalAssessmentStartDate,
									CxrEmployeeAccessCycleRecord::getOriginalAssessmentEndDate
							)
							.in(CxrEmployeeAccessCycleRecord::getCxrEmployeeId,employeeIds)
							.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
							.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
							.in(CxrEmployeeAccessCycleRecord::getChangeType, ChangeTypeEnum.UPGRADE.getCode(), ChangeTypeEnum.DOWNGRADE.getCode())
			);
			Map<Long, List<CxrEmployeeAccessCycleRecord>> cycleRecordMap =null;
			if (CollUtil.isNotEmpty(accessCycleRecords)){
				cycleRecordMap =accessCycleRecords.stream().collect(Collectors.groupingBy(CxrEmployeeAccessCycleRecord::getCxrEmployeeId));
			}

			List<Long> deptIds = records.stream().map(AccessEditSettingVo::getCurrentDeptId).collect(Collectors.toList());
			List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(deptIds);
			Map<Long, SysDept> sysDeptMap = sysDepts.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x));
			Set<Long> rootRegionIds =
					records.stream().map(AccessEditSettingVo::getCxrRootRegionId).collect(Collectors.toSet());

			List<SysDept> rootRegionDepts = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
							.select(SysDept::getDeptId, SysDept::getDeptName,SysDept::getParentId)
					.in(SysDept::getDeptId, rootRegionIds)
			);

			List<SysDept>  regionDepts =  sysDeptMapper.queryRegionNameAll(rootRegionIds);

			Map<Long, SysDept> rootRegionMap = regionDepts.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x,
					(v1, v2) -> v1));
			rootRegionDepts.stream().map(x->{
				x.setDeptName(rootRegionMap.get(x.getParentId()).getDeptName());
				return x;
			}).collect(Collectors.toList());

			Map<Long, SysDept> regionMap = rootRegionDepts.stream().collect(Collectors.toMap(x -> x.getDeptId(), x -> x,
					(v1, v2) -> v1));


			for (AccessEditSettingVo record : records) {
				SysDept dept = sysDeptMap.get(record.getCurrentDeptId());
				record.setCompanyName(ObjectUtil.isNotEmpty(dept)?dept.getDeptName():null);
				SysDept sysDept = regionMap.get(record.getCxrRootRegionId());
				record.setRegionName(sysDept.getDeptName());
				if (CollUtil.isNotEmpty(cycleRecordMap)&& CollUtil.isNotEmpty(cycleRecordMap.get(record.getEmployeeId()))){
					List<CxrEmployeeAccessCycleRecord> employeeAccessCycleRecords = cycleRecordMap.get(record.getEmployeeId());
					String stageDescription = employeeAccessCycleRecords.stream().map(x -> {
						return EmployeeStageEnum.fromSequence(x.getStage()).getDescription();
					}).collect(Collectors.joining(","));
					record.setStageStr(stageDescription);
					record.setStage(employeeAccessCycleRecords.get(0).getStage());
					List<StageDTO> dtoList = employeeAccessCycleRecords.stream().map(x -> {
						StageDTO dto = new StageDTO();
						dto.setStageStr(EmployeeStageEnum.fromSequence(x.getStage()).getDescription());
						dto.setStage(x.getStage());
						dto.setEmployeeId(x.getCxrEmployeeId());
						dto.setAccessCycleId(x.getId());
						dto.setActualAssessmentStartDate(x.getActualAssessmentStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
						dto.setCycleType(x.getCycleType());
						dto.setActualAssessmentEndDate(ObjectUtil.isNotEmpty(x.getActualAssessmentEndDate())?
								x.getActualAssessmentEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate():null);
						return dto;
					}).collect(Collectors.toList());
					record.setStageDTOs(dtoList);
				}
			}
		}
		return PageTableDataInfo.build(page);
	}

	@DSTransactional
	@Override
	public Boolean accessSingleSetting(AccessSettingBo bo) {

		LoginUser loginUser = LoginHelper.getLoginUser();
		List<CxrEmployeeAccessCycleRecordLog> cxrEmployeeAccessCycles = new ArrayList<>();
		CxrEmployee employee = cxrEmployeeService.getById(bo.getEmployeeId());
		if (CollUtil.isNotEmpty(bo.getStageEditDto())){
			List<Long> accessCycleIds = bo.getStageEditDto().stream().map(StageDTO::getAccessCycleId).collect(Collectors.toList());

			List<CxrEmployeeAccessCycleRecordEx> employeeAccessCycleRecordExes = cxrEmployeeAccessCycleRecordExService.getBaseMapper()
					.selectList(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordEx>()
							.in(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, accessCycleIds)
					);
			Map<Long, CxrEmployeeAccessCycleRecordEx> cycleRecordExMap = employeeAccessCycleRecordExes.stream().collect(Collectors.toMap(x -> x.getAccessCycleId(), x -> x, (v1, v2) -> v1));

			for (StageDTO dto : bo.getStageEditDto()) {

				if (ObjectUtil.isEmpty(dto.getEditLastAssessmentEndDate())){
					throw new ServiceException("周期时间不能为空！");
				}

				if (dto.getEditLastAssessmentEndDate().isBefore(dto.getEditLastAssessmentStartDate())){
					throw new ServiceException("周期结束时间不能小于周期开始时间");
				}
				Date submitTime = employee.getSubmitTime();
				LocalDate submitDate = submitTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				if (dto.getEditLastAssessmentStartDate().isBefore(submitDate)){
					throw new ServiceException("周期开始时间不能超过代理提交日期");
				}

				Long accessCycleId = dto.getAccessCycleId();
				Integer dtoStage = dto.getStage();

				CxrEmployeeAccessCycleRecord cycleRecord = this.getBaseMapper().selectById(accessCycleId);
				if (ObjectUtil.isEmpty(cycleRecord)){
					throw new ServiceException("未匹配到该代理考核周期");
				}

				CxrEmployeeAccessCycleRecordLog recordLog = new CxrEmployeeAccessCycleRecordLog();
				CxrEmployeeAccessCycleRecordEx accessCycleRecordEx = cycleRecordExMap.get(accessCycleId);
				if (ObjectUtil.isNotEmpty(accessCycleRecordEx)){
					BeanUtils.copyProperties(accessCycleRecordEx,recordLog);
				}
				recordLog.setId(null);
				recordLog.setStage(cycleRecord.getStage());
				recordLog.setEditLastStage(dtoStage);
				recordLog.setOriginalAssessmentStartDate(cycleRecord.getActualAssessmentStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				if (ObjectUtil.isNotEmpty(cycleRecord.getActualAssessmentEndDate())){
					recordLog.setOriginalAssessmentEndDate(cycleRecord.getActualAssessmentEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				}

				recordLog.setEditLastAssessmentStartDate(dto.getEditLastAssessmentStartDate());
				recordLog.setEditLastAssessmentEndDate(dto.getEditLastAssessmentEndDate());
				recordLog.setCurrentLevel(cycleRecord.getCurrentLevel());
				recordLog.setRemark(bo.getRemark());
				recordLog.setUpdateTime(new Date());
				recordLog.setUpdateByName(loginUser.getNickName());
				recordLog.setAccessCycleId(cycleRecord.getId());

				recordLog.setRerunFlag(bo.getRerunFlag());
				recordLog.setUpdateCycleReason(bo.getUpdateCycleReason());
				recordLog.setAdjustStartDate(bo.getAdjustStartDate());
				recordLog.setAdjustEndDate(bo.getAdjustEndDate());
				cxrEmployeeAccessCycles.add(recordLog);
			}


		}else {

			if (ObjectUtil.isEmpty(bo.getEditLastAssessmentEndDate())
					||ObjectUtil.isEmpty(bo.getEditLastAssessmentStartDate())){
				throw new ServiceException("周期结束时间不能为空");
			}

			if (bo.getEditLastAssessmentEndDate().isBefore(bo.getEditLastAssessmentStartDate())){
				throw new ServiceException("周期结束时间不能小于周期开始时间");
			}
			Date inductionTime = employee.getSubmitTime();
			LocalDate inductionDate = inductionTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			if (bo.getEditLastAssessmentStartDate().isBefore(inductionDate)){
				throw new ServiceException("周期开始时间不能超过代理提交日期");
			}

			List<CxrEmployeeAccessCycleRecord> accessCycleRecords = this.getBaseMapper().selectList(
					new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
							.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, bo.getEmployeeId())
							.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
							.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
							.eq(CxrEmployeeAccessCycleRecord::getChangeType, ChangeTypeEnum.UPGRADE.getCode())
			);

			if (CollUtil.isEmpty(accessCycleRecords)){
				throw new ServiceException("未匹配到该代理考核周期");
			}
			//周期记录表新增
			List<Long> accessCycleId = accessCycleRecords.stream().map(CxrEmployeeAccessCycleRecord::getId).collect(Collectors.toList());
			List<CxrEmployeeAccessCycleRecordEx> accessCycleRecordExes = cxrEmployeeAccessCycleRecordExService.getBaseMapper()
					.selectList(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordEx>()
							.in(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, accessCycleId)
					);
			Map<Long, CxrEmployeeAccessCycleRecordEx> cycleRecordExMap = accessCycleRecordExes.stream().collect(Collectors.toMap(x -> x.getAccessCycleId(), x -> x, (v1, v2) -> v1));


			for (CxrEmployeeAccessCycleRecord cycleRecord : accessCycleRecords) {
				CxrEmployeeAccessCycleRecordLog recordLog = new CxrEmployeeAccessCycleRecordLog();
				CxrEmployeeAccessCycleRecordEx accessCycleRecordEx = cycleRecordExMap.get(cycleRecord.getId());
				if (ObjectUtil.isNotEmpty(accessCycleRecordEx)){
					BeanUtils.copyProperties(accessCycleRecordEx,recordLog);
				}
				recordLog.setId(null);
				recordLog.setStage(cycleRecord.getStage());
				recordLog.setEditLastStage(bo.getStage());
				recordLog.setOriginalAssessmentStartDate(cycleRecord.getActualAssessmentStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				if (ObjectUtil.isNotEmpty(cycleRecord.getActualAssessmentEndDate())){
					recordLog.setOriginalAssessmentEndDate(cycleRecord.getActualAssessmentEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				}

				recordLog.setEditLastAssessmentStartDate(bo.getEditLastAssessmentStartDate());
				recordLog.setEditLastAssessmentEndDate(bo.getEditLastAssessmentEndDate());
				recordLog.setCurrentLevel(cycleRecord.getCurrentLevel());
				recordLog.setRemark(bo.getRemark());
				recordLog.setUpdateTime(new Date());
				recordLog.setUpdateByName(loginUser.getNickName());
				recordLog.setAccessCycleId(cycleRecord.getId());
				recordLog.setRerunFlag(bo.getRerunFlag());
				if(bo.getAdjustStartDate()!=null&& bo.getAdjustEndDate()!=null){
					recordLog.setUpdateCycleReason(bo.getAdjustStartDate().toString()+"-"+bo.getAdjustEndDate().toString()+
							"调整原因："+bo.getUpdateCycleReason());
				}else{
					recordLog.setUpdateCycleReason(bo.getUpdateCycleReason());
				}

				cxrEmployeeAccessCycles.add(recordLog);
			}
		}
		if (CollUtil.isNotEmpty(cxrEmployeeAccessCycles)){
			boolean saveBatch = accessCycleRecordLogService.saveBatch(cxrEmployeeAccessCycles);
			if (!saveBatch){
				throw new ServiceException("保存失败");
			}
			// 考核周期修改
			accessSettingEdit(cxrEmployeeAccessCycles);
		}
		return null;
	}


	@DSTransactional
	@Override
	public Object accessMultipleSetting(AccessSettingMultipleBo bo) {


		AccessEditSettingBo editSettingBo = BeanUtil.copyProperties(bo, AccessEditSettingBo.class);
		String ownerFlag = bo.getOwnerFlag();
		List<Long> employeeIds = bo.getCheckEmployeeIds();

		List<Long> accessSettingDeptIds= bo.getAccessSettingDTOS();// 与bigCompanyId 存在冲突
		Long dayOrMonthQuantity = bo.getDayOrMonthQuantity();// 修改天数
		Integer dayOrMonthType = bo.getDayOrMonthType();
		Integer advancedOrPostpone = bo.getAdvancedOrPostpone();

		// 提示
		StringBuilder failureMsg = new StringBuilder();
		AtomicInteger failureNum = new AtomicInteger();
		AtomicInteger successNum = new AtomicInteger();
		List<Long> employeesIds =null;

		if (ObjectUtil.equals(ownerFlag,UserConstants.DEPT_NORMAL)){
			if (CollUtil.isEmpty(employeeIds)){
				throw new ServiceException("没有勾选您要设置的数据！");
			}
			employeesIds=employeeIds;
		}else if (ObjectUtil.equals(ownerFlag,UserConstants.MENU_DISABLE)){
			PageQuery pageQuery =new PageQuery();
			pageQuery.setPageNum(1);
			pageQuery.setPageSize(INVALID_MAX);
			accessSettingPageParams(editSettingBo);
			editSettingBo.setOccupationStatus(OccupationStatus.INDUCTION.getValue());
			IPage<AccessEditSettingVo> page =  cxrEmployeeService.accessSettingPage(editSettingBo,pageQuery.build());
			List<AccessEditSettingVo> records = page.getRecords();
			if (CollUtil.isNotEmpty(records)){
				employeesIds = records.stream().map(AccessEditSettingVo::getEmployeeId).collect(Collectors.toList());
			}else{
				throw new ServiceException("该部门下没有可修改代理！");
			}
		}else{
			Set<Long> siteIds = queryDeptParamMultipleList(accessSettingDeptIds);
			if (CollUtil.isEmpty(siteIds)){
				throw new ServiceException("该部门下没有可修改代理！");
			}

			List<CxrEmployee> cxrEmployees = cxrEmployeeService.getBaseMapper()
					.selectList(new LambdaQueryWrapper<CxrEmployee>()
							.select(CxrEmployee::getId,CxrEmployee::getOccupationStatus,CxrEmployee::getCxrSiteId,CxrEmployee::getName)
							.in(CxrEmployee::getCxrSiteId,siteIds)
							.eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
							.eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
					);
			if (CollUtil.isEmpty(cxrEmployees)){
				throw new ServiceException("该部门下没有可修改代理!");
			}
			employeesIds = cxrEmployees.stream().map(CxrEmployee::getId).collect(Collectors.toList());
		}

		List<CxrEmployee> cxrEmployees = cxrEmployeeService.getBaseMapper()
				.selectList(new LambdaQueryWrapper<CxrEmployee>()
						.select(CxrEmployee::getId,CxrEmployee::getOccupationStatus,CxrEmployee::getCxrSiteId,CxrEmployee::getName)
						.in(CxrEmployee::getId,employeesIds)
						.eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
						.eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				);

		List<CxrEmployeeAccessCycleRecord> accessCycleRecords = this.getBaseMapper().selectList(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.in(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeesIds)
						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
						.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
		);
		if (CollUtil.isEmpty(accessCycleRecords)){
			throw new ServiceException("未匹配到代理考核周期");
		}
		// 拓展表
		List<Long> accessCycleId = accessCycleRecords.stream().map(CxrEmployeeAccessCycleRecord::getId).collect(Collectors.toList());
		List<CxrEmployeeAccessCycleRecordEx> accessCycleRecordExes = cxrEmployeeAccessCycleRecordExService.getBaseMapper()
				.selectList(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordEx>()
						.in(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, accessCycleId));

		Map<Long, CxrEmployeeAccessCycleRecord> accessCycleRecordMap = accessCycleRecords.stream().collect(Collectors.toMap(x -> x.getCxrEmployeeId(), x -> x, (v1, v2) -> v1));
		Map<Long, CxrEmployeeAccessCycleRecordEx> cycleRecordExMap = accessCycleRecordExes.stream().collect(Collectors.toMap(x -> x.getAccessCycleId(), x -> x, (v1, v2) -> v1));
		LoginUser loginUser = LoginHelper.getLoginUser();
		List<List<CxrEmployee>> split = CollUtil.split(cxrEmployees, 200);
		for (List<CxrEmployee> employees : split) {
			List<CxrEmployeeAccessCycleRecordLog> cxrEmployeeAccessCycles = new ArrayList<>();
			for (CxrEmployee cxrEmployee : employees) {

				if (ObjectUtil.equals(ownerFlag,UserConstants.MENU_DISABLE)&& CollUtil.isNotEmpty(bo.getFilterEmployeeIds())
				&&bo.getFilterEmployeeIds().contains(cxrEmployee.getId())
				){
					continue;
				}

				CxrEmployeeAccessCycleRecord cycleRecord = accessCycleRecordMap.get(cxrEmployee.getId());
				if (ObjectUtil.isEmpty(cycleRecord)){
					failureMsg.append(StrUtil.format("代理:{} 未匹配到考核周期",cxrEmployee.getName()));
					failureNum.getAndIncrement();
					continue;
				}
				CxrEmployeeAccessCycleRecordEx accessCycleRecordEx = cycleRecordExMap.get(cycleRecord.getId());
				// 起始日期
				if (ObjectUtil.isEmpty(cycleRecord.getActualAssessmentStartDate())){
					failureMsg.append(StrUtil.format("代理:{}考核周期无开始日期，自动跳过",cxrEmployee.getName()));
					failureNum.getAndIncrement();
					continue;
				}
				LocalDate assessmentStartDate = cycleRecord.getActualAssessmentStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
				if (ObjectUtil.isEmpty(cycleRecord.getActualAssessmentEndDate())){
					failureMsg.append(StrUtil.format("代理:{}考核周期无结束日期，自动跳过",cxrEmployee.getName()));
					failureNum.getAndIncrement();
					continue;
				}
				LocalDate assessmentEndDate =
						cycleRecord.getActualAssessmentEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();


				LocalDate editLastEndDate =null;
				if (NumberUtil.equals(advancedOrPostpone,1)){
					//提前
					editLastEndDate = processingSubtractDays(dayOrMonthType, dayOrMonthQuantity,
							cycleRecord.getActualAssessmentEndDate());
					//提前 且结束时间超过周期考核开始时间
					if (editLastEndDate.isBefore(assessmentStartDate)){
						long days = isMoreThan45Days(assessmentStartDate, editLastEndDate);
						failureMsg.append(StrUtil.format("{}的考核周期为 {}至{},修改后超过{}天 不能设置",cxrEmployee.getName(),
								assessmentStartDate,assessmentEndDate,days));
						failureNum.getAndIncrement();
						continue;
					}
				}else {
					//顺延
					editLastEndDate =processingAddDays(dayOrMonthType,dayOrMonthQuantity,cycleRecord.getActualAssessmentEndDate());
				}

				CxrEmployeeAccessCycleRecordLog recordLog = new CxrEmployeeAccessCycleRecordLog();
				if (ObjectUtil.isNotEmpty(accessCycleRecordEx)){
					BeanUtils.copyProperties(accessCycleRecordEx,recordLog);
				}
				recordLog.setStage(cycleRecord.getStage());
				recordLog.setEditLastStage(cycleRecord.getStage());
				recordLog.setOriginalAssessmentStartDate(cycleRecord.getActualAssessmentStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				if (ObjectUtil.isNotEmpty(cycleRecord.getActualAssessmentEndDate())){
					recordLog.setOriginalAssessmentEndDate(cycleRecord.getActualAssessmentEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
				}
				recordLog.setId(null);
				recordLog.setEditLastAssessmentStartDate(assessmentStartDate);
				recordLog.setEditLastAssessmentEndDate(editLastEndDate);
				recordLog.setCurrentLevel(cycleRecord.getCurrentLevel());
				recordLog.setRemark(bo.getRemark());
				recordLog.setUpdateTime(new Date());
				recordLog.setUpdateByName(loginUser.getNickName());
				recordLog.setAccessCycleId(cycleRecord.getId());
				recordLog.setRerunFlag(bo.getRerunFlag());
				if(bo.getAdjustStartDate()!=null&& bo.getAdjustEndDate()!=null){
					recordLog.setUpdateCycleReason(bo.getAdjustStartDate().toString()+"-"+bo.getAdjustEndDate().toString()+
							"调整原因："+bo.getUpdateCycleReason());
				}else{
					recordLog.setUpdateCycleReason(bo.getUpdateCycleReason());
				}
				recordLog.setAdjustStartDate(bo.getAdjustStartDate());
				recordLog.setAdjustEndDate(bo.getAdjustEndDate());
				recordLog.setCycleType(cycleRecord.getCycleType());
				cxrEmployeeAccessCycles.add(recordLog);
				successNum.getAndIncrement();
				//不是按天不改变结束日期
				if (!ObjectUtil.equals(cycleRecord.getCycleType(),AccessCycleTypeEnum.BY_DAY.getCode())){
					recordLog.setEditLastAssessmentEndDate(assessmentEndDate);
					recordLog.setUpdateCycleReason(recordLog.getUpdateCycleReason()+"按月的不改变即将过期时自动补偿");
				}
			}
			if (CollUtil.isNotEmpty(cxrEmployeeAccessCycles)){
				boolean saveBatch = accessCycleRecordLogService.saveBatch(cxrEmployeeAccessCycles);
				if (saveBatch){
					accessSettingEdit(cxrEmployeeAccessCycles);
				}
			}
		}
		return failureNum.get()>0?failureMsg.insert(0, StrUtil.format("操作成功，共有[{}]条,其中[{}]条数据修改失败",
				cxrEmployees.size(),failureNum.get())):true;
	}


	public  long isMoreThan45Days(LocalDate startDate, LocalDate endDate) {
		long daysBetween = ChronoUnit.DAYS.between(startDate, endDate);
		return daysBetween ;
	}

	private LocalDate processingAddDays(Integer dayOrMonthType,Long dayOrMonthQuantity,Date handlingDay) {
		LocalDate localDate = handlingDay.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		if (NumberUtil.equals(dayOrMonthType,1)){
			localDate = localDate.plusDays(dayOrMonthQuantity.intValue());
		}else{
			localDate = localDate.plusMonths(dayOrMonthQuantity.intValue());
		}
		return localDate;
	}

	private LocalDate processingSubtractDays(Integer dayOrMonthType,Long dayOrMonthQuantity,Date handlingDay) {
		LocalDate localDate = handlingDay.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		if (NumberUtil.equals(dayOrMonthType,1)){
			localDate = 	localDate.minusDays(dayOrMonthQuantity.intValue());
		}else{
			localDate = localDate.minusMonths(-dayOrMonthQuantity.intValue());
		}
		return localDate;
	}

	@Override
	public List<Integer> listDownStage(Long cxrEmployeeId) {
		List<CxrEmployeeAccessCycleRecord> list = this.baseMapper.selectList(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.select(CxrEmployeeAccessCycleRecord::getStage)
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, cxrEmployeeId)
						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
						.eq(CxrEmployeeAccessCycleRecord::getExecutionStatus, ExecutionStatusEnum.EXECUTED.getCode())
						.eq(CxrEmployeeAccessCycleRecord::getChangeType, ChangeTypeEnum.DOWNGRADE.getCode())
		);
		if (CollUtil.isEmpty(list))ListUtil.toList();;
		return list.stream().map(CxrEmployeeAccessCycleRecord::getStage).collect(Collectors.toList());
	}

	@DSTransactional
	@Override
	public Object assessStageEdit(StageDTO bo) {

		LoginUser loginUser = LoginHelper.getLoginUser();
		List<CxrEmployeeAccessCycleRecord> accessCycleRecords =
				accessCycleManager.generateAccessCycleByStage(bo.getEmployeeId(), new Date(), bo.getEditLastStage());
		if (CollUtil.isEmpty(accessCycleRecords)){
			throw new ServiceException("未匹配到代理考核周期");
		}
//		accessCycleRecords = accessCycleRecords.stream()
//				.filter(c -> c.getChangeType().equals(ChangeTypeEnum.UPGRADE.getCode())).collect(
//						Collectors.toList());
		// 取其中一条作为记录
		CxrEmployeeAccessCycleRecord cycleRecord =
				accessCycleRecords.get(0);
		Long accessCycleId = cycleRecord.getId();
		CxrEmployeeAccessCycleRecordEx accessCycleRecordEx = cxrEmployeeAccessCycleRecordExService.getBaseMapper()
				.selectOne(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordEx>()
						.in(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, accessCycleId).last("limit 1 "));
			CxrEmployeeAccessCycleRecordLog recordLog = new CxrEmployeeAccessCycleRecordLog();
			if (ObjectUtil.isNotEmpty(accessCycleRecordEx)){
				BeanUtils.copyProperties(accessCycleRecordEx,recordLog);
			}
			recordLog.setId(null);
			recordLog.setStage(bo.getStage());
			recordLog.setOriginalAssessmentStartDate(null);
				recordLog.setOriginalAssessmentEndDate(
						null);
			recordLog.setEditLastStage(ObjectUtil.isNotEmpty(bo.getEditLastStage())?bo.getEditLastStage():bo.getStage());
			// 放回时间
			recordLog.setEditLastAssessmentStartDate(null);
			recordLog.setEditLastAssessmentEndDate(null);
			//-----
			recordLog.setCurrentLevel(cycleRecord.getCurrentLevel());
			recordLog.setUpdateTime(new Date());
			recordLog.setUpdateByName(loginUser.getNickName());
			recordLog.setAccessCycleId(cycleRecord.getId());
			recordLog.setRerunFlag(bo.getRerunFlag());
			recordLog.setUpdateCycleReason(bo.getUpdateCycleReason());
			recordLog.setAdjustStartDate(bo.getAdjustStartDate());
			recordLog.setAdjustEndDate(bo.getAdjustEndDate());
			boolean save = accessCycleRecordLogService.save(recordLog);
			if (!save){
				throw new ServiceException("修改失败！");
			}

		//生成修改记录
		Map<String,Object> data =new HashMap<>();
		data.put("editLastStag", bo.getStage());
		data.put("currentLevel", accessCycleRecords.get(0).getCurrentLevel());


		data.put("editLastAssessmentStartDate",DateUtil.parse(DateUtil.format(accessCycleRecords.get(0).getActualAssessmentStartDate(),
				DatePattern.NORM_DATE_PATTERN)).toSqlDate().toLocalDate());
		data.put("editLastAssessmentEndDate",
				ObjectUtil.isNotEmpty(accessCycleRecords.get(0).getActualAssessmentEndDate())?
						DateUtil.parse(DateUtil.format(accessCycleRecords.get(0).getActualAssessmentEndDate(),
								DatePattern.NORM_DATE_PATTERN)).toSqlDate().toLocalDate():null);



		List<StageDTO> dtoList = accessCycleRecords.stream().map(x -> {
			StageDTO dto = new StageDTO();
			dto.setStageStr(EmployeeStageEnum.fromSequence(x.getStage()).getDescription());
			dto.setStage(x.getStage());
			dto.setEmployeeId(x.getCxrEmployeeId());
			dto.setCycleType(x.getCycleType());
			dto.setAccessCycleId(x.getId());
			dto.setActualAssessmentStartDate(DateUtil.parse(DateUtil.format(x.getActualAssessmentStartDate(),
					DatePattern.NORM_DATE_PATTERN)).toSqlDate().toLocalDate());



			dto.setActualAssessmentEndDate(ObjectUtil.isNotEmpty(x.getActualAssessmentEndDate())?
					DateUtil.parse(DateUtil.format(x.getActualAssessmentEndDate(),
							DatePattern.NORM_DATE_PATTERN)).toSqlDate().toLocalDate():null);

			return dto;
		}).collect(Collectors.toList());
		data.put("editAccessCycleRecords", dtoList);
		return data;
	}

	private void accessSettingEdit(List<CxrEmployeeAccessCycleRecordLog> cxrEmployeeAccessCycles){
		List<CxrEmployeeAccessCycleRecord> recordsToUpdate = new ArrayList<>();
		// 考核周期修改
		for (CxrEmployeeAccessCycleRecordLog employeeAccessCycle : cxrEmployeeAccessCycles) {
			//不是按天的不改
			if (employeeAccessCycle.getCycleType()!=null&&!ObjectUtil.equals(employeeAccessCycle.getCycleType(),
					AccessCycleTypeEnum.BY_DAY.getCode())){
				continue;
			}
			CxrEmployeeAccessCycleRecord record = new CxrEmployeeAccessCycleRecord();
			record.setId(employeeAccessCycle.getAccessCycleId());
			Date editLastAssessmentEndDate = Date.from(employeeAccessCycle.getEditLastAssessmentEndDate().atStartOfDay(ZoneId.systemDefault()).toInstant());
			Date editLastAssessmentStartDate =
					Date.from(employeeAccessCycle.getEditLastAssessmentStartDate().atStartOfDay(ZoneId.systemDefault()).toInstant());

			record.setActualAssessmentEndDate(editLastAssessmentEndDate);
			record.setActualAssessmentStartDate(editLastAssessmentStartDate);
			recordsToUpdate.add(record);
		}
		updateBatchById(recordsToUpdate);
	}


	private List<SysDept> queryDeptsParamSimpleList(Long companyId){
	return 	sysDeptMapper.selectList(
				new LambdaQueryWrapper<SysDept>()
						.select(SysDept::getDeptId, SysDept::getDeptName, SysDept::getParentId, SysDept::getOrderNum)
						.eq(SysDept::getStatus, UserConstants.DEPT_NORMAL)
						.eq(SysDept::getSiteFlag, UserConstants.DEPT_NORMAL)
						.apply("find_in_set({0}, ancestors) <> 0", companyId)
						.orderByAsc(SysDept::getParentId)
						.orderByAsc(SysDept::getOrderNum));
	}

	private Set<Long> queryDeptParamMultipleList(List<Long> accessSettingDeptIds){
		Set<Long> siteIds=new HashSet<>();
		// 站点
		List<SysDept> siteList = sysDeptMapper.selectList(new LambdaQueryWrapper<SysDept>()
				.select(SysDept::getDeptId,SysDept::getDeptName)
				.in(SysDept::getDeptId, accessSettingDeptIds)
				.eq(SysDept::getDelFlag,DeleteStatus.NOT_DELETED.getValue())
				.eq(SysDept::getSiteFlag, 0)
		);
		if (CollUtil.isNotEmpty(siteList)){
			List<Long> siteDeptIds = siteList.stream().map(x -> x.getDeptId()).collect(Collectors.toList());
			siteIds.addAll(siteDeptIds);
			Map<Long, String> stringMap = siteList.stream().collect(Collectors.toMap(SysDept::getDeptId, x -> x.getDeptName()));
			List<Long> toRemove = new ArrayList<>();
			//反向遍历
			for (Long deptId : accessSettingDeptIds) {
				if (ObjectUtil.isNotEmpty(stringMap.get(deptId))) {
					toRemove.add(deptId);
				}
			}
			accessSettingDeptIds.removeAll(toRemove);

		}
		if (CollUtil.isNotEmpty(accessSettingDeptIds)){
			//  公司 大区 区域
			for (Long x : accessSettingDeptIds) {
				List<Long> deptIds=sysDeptMapper.companyOrRegionAllSite(x);
				if (CollUtil.isNotEmpty(deptIds)){
					siteIds.addAll(deptIds);
				}
			}
		}
		return siteIds;
	}

	private void accessSettingPageParams(AccessEditSettingBo bo){
		if (ObjectUtil.isNotEmpty(bo.getStage())){
			List<CxrEmployeeAccessCycleRecord> accessCycleRecords = this.getBaseMapper().selectList(
					new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
							.select(CxrEmployeeAccessCycleRecord::getCxrEmployeeId)
							.eq(CxrEmployeeAccessCycleRecord::getStage, bo.getStage())
							.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
							.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode())
							.eq(CxrEmployeeAccessCycleRecord::getChangeType, ChangeTypeEnum.UPGRADE.getCode())
			);
			if (CollUtil.isNotEmpty(accessCycleRecords)){
				bo.setEmployeeIds(accessCycleRecords.stream().map(CxrEmployeeAccessCycleRecord::getCxrEmployeeId).collect(Collectors.toList()));
			}else{
				bo.setEmployeeIds(Arrays.asList(INVALID_SITE_ID));
			}
		}

		if (StrUtil.isNotEmpty(bo.getRegionName())){
			List<SysDept>  regionDepts = sysDeptMapper.queryRegionJuniorAll(bo.getRegionName());
			if (CollUtil.isNotEmpty(regionDepts)){
				bo.setRootRegionIds(regionDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList()));
			}else {
				bo.setEmployeeIds(Arrays.asList(INVALID_SITE_ID));
			}
		}

		if (CollUtil.isNotEmpty(bo.getBigCompanyId())){
			Set<Long> siteIds = queryDeptParamMultipleList(bo.getBigCompanyId());
			if (CollUtil.isEmpty(siteIds)){
				bo.setBigSiteIds(Collections.singleton(INVALID_SITE_ID));
			}else {
				bo.setBigSiteIds(siteIds);
			}
		}
	}
	@Override
	@DSTransactional
	public List<CxrEmployeeAccessCycleRecord> rerunActive(Long employeeId,Date rerunDate,String rerunReason){
		return this.rerunActive(employeeId,rerunDate,rerunReason,null);
	}

	//new
	@Override
	@DSTransactional
	public List<CxrEmployeeAccessCycleRecord> rerunActive(Long employeeId,Date rerunDate,String rerunReason,
			List<Long> rerunAccessIds){
		Date now = DateUtil.date();
		if(DateUtil.compare(rerunDate,now)>=0){
			log.info("重跑日期大于今日,不处理");
			return ListUtil.toList();
		}
		List<CxrEmployeeAccessCycleRecord> rerunDateAccessCycleRecordListAll = this.list(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
//						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(), RunStatusEnum.ENDED.getCode())
						.ge(CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate, DateUtil.offsetDay(rerunDate, -1).toSqlDate())//>=
				// 结束日期大于重跑日期的都要重来
//						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus,DeleteStatus.NOT_DELETED.getValue())//
		);

		if(rerunDateAccessCycleRecordListAll == null)rerunDateAccessCycleRecordListAll = new ArrayList<>();
		boolean isUpdate = false;
		isUpdate =  this.vaildRerunFlag(rerunDateAccessCycleRecordListAll);
		if(!isUpdate&& rerunDateAccessCycleRecordListAll.size()!=0){
			//再查下id
			CxrEmployeeAccessCycleRecord cycleRecord = rerunDateAccessCycleRecordListAll.stream()
					.min(Comparator.comparing(CxrEmployeeAccessCycleRecord::getId)).get();
			List<CxrEmployeeAccessCycleRecord> minRerunDateAccessCycleRecordListAll = this.list(
					new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
							.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
//						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(), RunStatusEnum.ENDED.getCode())
							.ge(CxrEmployeeAccessCycleRecord::getId, cycleRecord.getId())//>=
					// 结束日期大于重跑日期的都要重来
//						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus,DeleteStatus.NOT_DELETED.getValue())//
			);
			isUpdate =  this.vaildRerunFlag(minRerunDateAccessCycleRecordListAll);
		}
			//如果已达标并且日期不在内则不重跑
//			if(cycleRecord.getAchievementStatus().equals(AchievementStatusEnum.ACHIEVED.getCode()) && cycleRecord.getAchievementDate().before(rerunDate)){
//				log.info("重跑日期前{}已达标，不重跑",cycleRecord.getId());
//				rerunDateAccessCycleRecordListAll.remove(cycleRecord);
//			}

		//更改过使用当前周期
		List<Long> cycleIdList = null;
		if(isUpdate){
			cycleIdList =
					this.getInProgress(employeeId).stream().map(CxrEmployeeAccessCycleRecord::getId).collect(Collectors.toList());
		}else{
			List<SalesDateCycle> cycleIds = salesDateCycleService.list(
					new LambdaQueryWrapper<SalesDateCycle>().eq(SalesDateCycle::getRunDate, rerunDate).eq(SalesDateCycle::getCxrEmployeeId,
							employeeId));
			cycleIdList = cycleIds.stream().map(SalesDateCycle::getAccessCycleId).collect(Collectors.toList());
		}

		if (CollUtil.isEmpty(cycleIdList)){
			log.error("未找到历史关联的周期记录");
			return  ListUtil.toList();
		}
		if(CollectionUtil.isEmpty(rerunDateAccessCycleRecordListAll)){
			log.info("没有可重跑数据");
			return  ListUtil.toList();
		}
		List<CxrEmployeeAccessCycleRecord> accessCycleRecordOldList = this.list(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>().in(CxrEmployeeAccessCycleRecord::getId,
						cycleIdList));
		if(CollectionUtil.isEmpty(accessCycleRecordOldList)){
			log.error("未找到历史关联的周期记录");
			return  ListUtil.toList();
		}
		log.info("{},剔除前accessCycleRecordOldList:{}",employeeId,cycleIdList);
		//取最小等级的
		Integer minLevel = accessCycleRecordOldList.stream()
				.sorted(Comparator.comparingInt(CxrEmployeeAccessCycleRecord::getCurrentLevel)).collect(Collectors.toList()).get(0).getCurrentLevel();

		//剔除不同等级的
		accessCycleRecordOldList =
				accessCycleRecordOldList.stream()
						.filter(cycleRecord -> cycleRecord.getCurrentLevel().equals(minLevel))
						.filter(cycleRecord-> !cycleRecord.getActualAssessmentStartDate().after(rerunDate))
						.collect(Collectors.toList());
		log.info("{},accessCycleRecordOldList:{}",employeeId,accessCycleRecordOldList.size());
		if(CollUtil.isEmpty(accessCycleRecordOldList)){
			log.info("accessCycleRecordOldList没有可重跑数据");
			return  ListUtil.toList();
		}

		String rerunDateYYYYMMDD = DateUtil.format(rerunDate, DatePattern.NORM_DATE_PATTERN);
		CxrEmployee cxrEmployee = cxrEmployeeService.getById(employeeId);
		this.endInProgress(employeeId,
						now,null, null,DateUtil.format(rerunDate, DatePattern.NORM_DATE_PATTERN)+rerunReason,null,true);
		//把后面时间产生的全部逻辑删除
		CxrEmployeeAccessCycleRecord cycleRecordOldMin = accessCycleRecordOldList.stream()
				.min(Comparator.comparing(CxrEmployeeAccessCycleRecord::getId)).get();
		List<Long> allRecordIds =
				rerunDateAccessCycleRecordListAll.stream()
						.filter(n -> n.getDeleteStatus().equals(DeleteStatus.NOT_DELETED.getValue()))
						.filter(n -> n.getId().compareTo(cycleRecordOldMin.getId()) >= 0)
						.map(CxrEmployeeAccessCycleRecord::getId)
						.collect(Collectors.toList());
		List<Long> deleteIds = this.baseMapper.selectList(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.select(CxrEmployeeAccessCycleRecord::getId)
				.in(CxrEmployeeAccessCycleRecord::getId, allRecordIds)
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
		).stream().map(CxrEmployeeAccessCycleRecord::getId).collect(Collectors.toList());
		if(rerunAccessIds != null){
			rerunAccessIds.addAll(deleteIds);
		}
		if (!deleteIds.isEmpty()){
			this.update(
					new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
							.in(CxrEmployeeAccessCycleRecord::getId, deleteIds)
							.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
							.set(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
							.set(CxrEmployeeAccessCycleRecord::getDeleteByName,rerunDateYYYYMMDD+rerunReason+"重跑删除")
							.set(CxrEmployeeAccessCycleRecord::getDeleteTime,now)
							.set(CxrEmployeeAccessCycleRecord::getDeleteBy,1)
			);
		}

		CxrEmployeeAccessCycleRecord accessCycleRecord0 = accessCycleRecordOldList.get(0);
		for (CxrEmployeeAccessCycleRecord cycleRecord : accessCycleRecordOldList) {
			cycleRecord.init();
			cycleRecord.setActualAssessmentStartDate(cycleRecord.getOriginalAssessmentStartDate());
			cycleRecord.setActualAssessmentEndDate(cycleRecord.getOriginalAssessmentEndDate());
			accessCycleRecordService.addAccessCycle(cycleRecord);
		}
		Integer empLevel = Integer.valueOf(cxrEmployee.getEmployeeLevelType());
		if(	accessCycleRecord0.getCurrentLevel()<accessCycleConfig.getLimitUpdateLevel()
				&& !accessCycleRecord0.getCurrentLevel().equals(empLevel)){//2024 -11-6,
			// 为了降低影响，先不对L3以上进行等级变更，排查问题用
			//删除重跑日期后已经生成的等级记录（包括手动的）
			salesLevelChangeRecordService.update(
					new LambdaUpdateWrapper<SalesLevelChangeRecord>()
							.eq(SalesLevelChangeRecord::getCxrEmployeeId, employeeId)
							.ge(SalesLevelChangeRecord::getEndTime, rerunDate)
							.eq(SalesLevelChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
							.set(SalesLevelChangeRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
							.set(SalesLevelChangeRecord::getDeleteTime, now)
							.set(SalesLevelChangeRecord::getDeleteNote, rerunDateYYYYMMDD+rerunReason+"重跑删除")
			);
			//恢复销售代理的等级到第一条周期时的
//			cxrEmployeeService.levelUpdate(employeeId,  Integer.valueOf(cxrEmployee.getEmployeeLevelType()),
//					accessCycleRecord0.getCurrentLevel());
			cxrEmployeeService.levelChange(employeeId, accessCycleRecord0.getActualAssessmentStartDate(),empLevel
			,	accessCycleRecord0.getCurrentLevel(),null,
					ChangeLevelSource.RE_RUN);
		}
		return accessCycleRecordOldList;
	}

	private boolean vaildRerunFlag(List<CxrEmployeeAccessCycleRecord> rerunDateAccessCycleRecordListAll) {
		boolean isUpdate = false;
		//判断是否存在新规则结束的状态，如果有则忽略，剔除掉被达标流转的
		for (int i = rerunDateAccessCycleRecordListAll.size() - 1; i >= 0; i--) {
			CxrEmployeeAccessCycleRecord cycleRecord = rerunDateAccessCycleRecordListAll.get(i);
			if (cycleRecord.getNewRuleEndFlag().equals(NewRuleEndFlag.YES.getCode())) {
				log.info("{}存在新规则结束状态,不处理", cycleRecord.getId());
				isUpdate = true;
				break;
			}
			//存在手动修改等级，不重跑
			if (StrUtil.contains(cycleRecord.getAssStatusEndNote(), "手动修改")) {
				log.info("{}存在手动修改等级，不重跑", cycleRecord.getId());
				isUpdate = true;
				break;
			}
			//存在手动修改等级，不重跑
			if (StrUtil.contains(cycleRecord.getDeleteByType(), "手动修改")) {
				log.info("{}存在手动修改等级，不重跑", cycleRecord.getId());
				isUpdate = true;
				break;
			}
		}
		return isUpdate;
	}
	public static void main(String[] args) {
		System.out.println(!DateUtil.parse("2025-03-01","yyyy-MM-dd").after(DateUtil.parse("2025-03-01","yyyy-MM-dd")));
	}

//	@Override
//	@DSTransactional
//	public void rerunActive(Long employeeId,Date rerunDate,String rerunReason) {
//		//2.
//		List<CxrEmployeeAccessCycleRecord> rerunDateAccessCycleRecordListAll = this.list(
//				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
//						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
//						.in(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.IN_PROGRESS.getCode(), RunStatusEnum.ENDED.getCode())
//						.ge(CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate, rerunDate)//>= 结束日期大于重跑日期的都要重来
//						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus,DeleteStatus.NOT_DELETED.getValue())//
//		);
//
//		if(rerunDateAccessCycleRecordListAll == null)rerunDateAccessCycleRecordListAll = new ArrayList<>();
//		//判断是否存在新规则结束的状态，如果有则忽略，剔除掉被达标流转的
//		for (int i = rerunDateAccessCycleRecordListAll.size() - 1; i >= 0; i--) {
//			CxrEmployeeAccessCycleRecord cycleRecord = rerunDateAccessCycleRecordListAll.get(i);
//			if(cycleRecord.getNewRuleEndFlag().equals(NewRuleEndFlag.YES.getCode())){
//				log.info("{}存在新规则结束状态,不处理",cycleRecord.getId());
//				return;
//			}
//			//存在手动修改等级，不重跑
//			if(StrUtil.contains(cycleRecord.getAssStatusEndNote(),"手动修改等级")){
//				log.info("{}存在手动修改等级，不重跑",cycleRecord.getId());
//				return;
//			}
////			//剔除被达标流转的
////			if(StrUtil.contains(cycleRecord.getAssStatusEndNote(),"达标")&& cycleRecord.getAchievementStatus().equals(
////					AchievementStatusEnum.NOT_ACHIEVED.getCode())){
////				rerunDateAccessCycleRecordList.remove(i);
////			}
//		}
////		//判断是否已达标，剔除达标日期之外前的  因为有的是比重跑日期提前生效了,不包含在重跑日期内
////		rerunDateAccessCycleRecordList = rerunDateAccessCycleRecordList.stream()
////				.filter(item->{
////					return item.getAchievementStatus().equals(AchievementStatusEnum.NOT_ACHIEVED.getCode())
////							|| (DateUtil.compare(item.getAchievementDate(),dto.getRerunDate())>=0);
////				}).collect(Collectors.toList());
//		if(CollectionUtil.isEmpty(rerunDateAccessCycleRecordListAll)){
//			log.info("未找到重跑日期对应的考核周期记录");
//			return;
//		}
//
//		String rerunDateYYYYMMDD = DateUtil.format(rerunDate, DatePattern.NORM_DATE_PATTERN);
//		Date now = DateUtil.date();
//
//
//		//按周期起始日期正序
////		rerunDateAccessCycleRecordList.sort(Comparator.comparing(CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate));
//
//
////		//按照最早的一条阶段重新生成周期.优先选升级，无则默认第一条
////		CxrEmployeeAccessCycleRecord accessCycleRecord0 = rerunDateAccessCycleRecordList.get(0);
////		if(!accessCycleRecord0.getChangeType().equals(ChangeTypeEnum.UPGRADE.getCode())){
////			for (CxrEmployeeAccessCycleRecord cycleRecord : rerunDateAccessCycleRecordList) {
////				if(cycleRecord.getChangeType().equals(ChangeTypeEnum.UPGRADE.getCode())){
////					accessCycleRecord0 = cycleRecord;
////					break;
////				}
////			}
////		}
////		Long cxrEmployeeId = accessCycleRecord0.getCxrEmployeeId();
//
//		//取actual_assessment_start_date日期小于重跑日期的
//		List<CxrEmployeeAccessCycleRecord> accessCycleRecordFilterDateList = rerunDateAccessCycleRecordListAll.stream()
//				.filter(s -> DateUtil.compare(s.getActualAssessmentStartDate(), rerunDate) <= 0)
//				.filter(s-> s.getAchievementStatus().equals(AchievementStatusEnum.NOT_ACHIEVED.getCode())||DateUtil.compare(s.getAchievementDate(), rerunDate)>0)//在达标日期内
//				.collect(
//						Collectors.toList());
//		if(CollectionUtil.isEmpty(accessCycleRecordFilterDateList)){
//			log.info(employeeId+"过滤后未找到重跑日期对应的考核周期记录");
//			return;
//		}
//		CxrEmployee cxrEmployee = cxrEmployeeService.getById(employeeId);
//		//清洗数据-开始
//		//结束进行中的
//		for (CxrEmployeeAccessCycleRecord cycleRecord : accessCycleRecordFilterDateList) {
//			if(cycleRecord.getRunStatus().equals(RunStatusEnum.IN_PROGRESS.getCode())){
//				this.endInProgress(employeeId,
//						now,cycleRecord.getId(), null,DateUtil.format(rerunDate, DatePattern.NORM_DATE_PATTERN)+rerunReason);
//			}
//		}
//		List<Long> srcRecordIds = accessCycleRecordFilterDateList.stream().map(CxrEmployeeAccessCycleRecord::getId).collect(
//				Collectors.toList());
////		List<Long> recordIds = rerunDateAccessCycleRecordList.stream().map(CxrEmployeeAccessCycleRecord::getId).collect(Collectors.toList());
//		//把这段时间产生的全部逻辑删除
//		this.update(
//				new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecord>()
//						.in(CxrEmployeeAccessCycleRecord::getId, srcRecordIds)
//						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//						.set(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
//						.set(CxrEmployeeAccessCycleRecord::getDeleteByName,rerunDateYYYYMMDD+rerunReason+"重跑删除")
//						.set(CxrEmployeeAccessCycleRecord::getDeleteTime,now)
//						.set(CxrEmployeeAccessCycleRecord::getDeleteBy,1)
//		);
//
//
//		//清洗数据-结束
//		//获取最高等级
//		Integer maxCurrentLevel = accessCycleRecordFilterDateList.stream()
//				.max(Comparator.comparing(CxrEmployeeAccessCycleRecord::getCurrentLevel)).get().getCurrentLevel();
//		log.info("{}最高等级为{}",employeeId,maxCurrentLevel);
//		accessCycleRecordFilterDateList = accessCycleRecordFilterDateList.stream()
//				.filter(item -> item.getCurrentLevel().equals(maxCurrentLevel)).collect(
//						Collectors.toList());
//		//按照规则组ID分组，每个按照ActualAssessmentStartDate取最早的，复制原来的数据生成周期
//		Map<Long, List<CxrEmployeeAccessCycleRecord>> accessCycleRecordGroupByRuleGroupId = accessCycleRecordFilterDateList.stream().collect(
//				Collectors.groupingBy(CxrEmployeeAccessCycleRecord::getRuleConditionGroupId));
//		for (Map.Entry<Long, List<CxrEmployeeAccessCycleRecord>> entry : accessCycleRecordGroupByRuleGroupId.entrySet()) {
//			List<CxrEmployeeAccessCycleRecord> accessCycleRecordList = entry.getValue();
//			//取actual_assessment_start_dat最早的
//			CxrEmployeeAccessCycleRecord accessCycleRecordMin =
//					accessCycleRecordList.stream().min(Comparator.comparing(CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate)).get();
//			accessCycleRecordMin.init();
//			accessCycleRecordService.addAccessCycle(accessCycleRecordMin);
//		}
//		CxrEmployeeAccessCycleRecord accessCycleRecord0 = accessCycleRecordFilterDateList.get(0);
//		//把等级不一样的停掉
//		for (CxrEmployeeAccessCycleRecord cycleRecord : rerunDateAccessCycleRecordListAll) {
//			if(!cycleRecord.getCurrentLevel().equals(accessCycleRecord0.getCurrentLevel())){
//				this.endInProgress(employeeId,
//						now,cycleRecord.getId(), null,DateUtil.format(rerunDate, DatePattern.NORM_DATE_PATTERN)+rerunReason);
//			}
//		}
////		accessCycleManager.generateAccessCycleByStage(cxrEmployeeId,accessCycleRecord0.getActualAssessmentStartDate(),
////				accessCycleRecord0.getStage(),ChangeLevelSource.RE_RUN);
//		LoginInfo loginUser = LoginUtil.getLoginUser();
////		//如果没有正在进行的则新增一条当前周期
////		SalesLevelChangeRecord inProgressLevelChangeRecord = salesLevelChangeRecordService.getInProgress(employeeId);
////		if (inProgressLevelChangeRecord == null) {
////			SalesLevelChangeRecord salesLevelChangeRecord = new SalesLevelChangeRecord();
////			salesLevelChangeRecord.setCxrEmployeeId(employeeId);
////			salesLevelChangeRecord.setLevel(accessCycleRecord0.getCurrentLevel());
////			salesLevelChangeRecord.setStartTime(accessCycleRecord0.getActualAssessmentStartDate());
////			salesLevelChangeRecord.setSource(ChangeLevelSource.RE_RUN.getCode());
////			salesLevelChangeRecord.setSourceId(accessCycleRecord0.getId());
////			salesLevelChangeRecord.setCreateTime(now);
////			salesLevelChangeRecord.setCreateBy(loginUser.getUserId());
////			salesLevelChangeRecord.setCreateByName(loginUser.getUserName());
////			salesLevelChangeRecordService.save(salesLevelChangeRecord);
////		}
//		if(	accessCycleRecord0.getCurrentLevel()<3){//2024-11-6,为了降低影响，先不对L3以上进行等级变更，排查问题用
//			//删除重跑日期后已经生成的等级记录（包括手动的）
//			salesLevelChangeRecordService.update(
//					new LambdaUpdateWrapper<SalesLevelChangeRecord>()
//							.eq(SalesLevelChangeRecord::getCxrEmployeeId, employeeId)
//							.ge(SalesLevelChangeRecord::getEndTime, rerunDate)
//							.eq(SalesLevelChangeRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//							.set(SalesLevelChangeRecord::getDeleteStatus, DeleteStatus.DELETED.getValue())
//							.set(SalesLevelChangeRecord::getDeleteTime, now)
//							.set(SalesLevelChangeRecord::getDeleteNote, rerunDateYYYYMMDD+rerunReason+"重跑删除")
//			);
//			//恢复销售代理的等级到第一条周期时的
////			cxrEmployeeService.levelUpdate(employeeId,  Integer.valueOf(cxrEmployee.getEmployeeLevelType()),
////					accessCycleRecord0.getCurrentLevel());
//			cxrEmployeeService.levelChange(employeeId, accessCycleRecord0.getActualAssessmentStartDate(),
//					Integer.valueOf(cxrEmployee.getEmployeeLevelType()),	accessCycleRecord0.getCurrentLevel(),null,
//					ChangeLevelSource.RE_RUN);
//		}
//	}

	@Override
	public CxrEmployeeAccessCycleRecord getAssessCycleRecordBy(Long employeeId, Integer stage) {
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
//				.eq(CxrEmployeeAccessCycleRecord::getCurrentLevel, EmployeeLevelEnum.L1.getLevel())
				.eq(CxrEmployeeAccessCycleRecord::getStage, stage)
//				.eq(CxrEmployeeAccessCycleRecord::getAchievementStatus, AchievementStatusEnum.ACHIEVED.getCode())
//				.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.ENDED.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.orderByDesc(CxrEmployeeAccessCycleRecord::getId)
		);
		if(CollectionUtils.isEmpty(list)) return null;
		CxrEmployeeAccessCycleRecord cycleRecord = Collections.max(list, Comparator.comparing(CxrEmployeeAccessCycleRecord::getId));
		return cycleRecord;
	}

	@Override
	public CxrEmployeeAccessCycleRecord getAssessCycleRecordBy(Long employeeId, Collection<Integer> stages, Integer achievementStatus) {
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
						.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
						.in(CxrEmployeeAccessCycleRecord::getStage, stages)
						.eq(Objects.nonNull(achievementStatus),CxrEmployeeAccessCycleRecord::getAchievementStatus, achievementStatus)
						.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
						.orderByDesc(CxrEmployeeAccessCycleRecord::getId)
		);
		if(CollectionUtils.isEmpty(list)) return null;
		CxrEmployeeAccessCycleRecord cycleRecord = Collections.max(list, Comparator.comparing(CxrEmployeeAccessCycleRecord::getId));
		return cycleRecord;
	}

	@Override
	public List<CxrEmployeeAccessCycleRecord> getAssessCycleRecordBy(Long employeeId, Collection<Integer> stages, Integer achievementStatus,
																	 LocalDate startDate, LocalDate endDate) {
		List<Long> assessRecordIds = baseMapper.getEveryStageAssessRecordMaxId(employeeId, ruleSetCode,startDate, endDate);
		boolean dateNotNull = Objects.nonNull(startDate) && Objects.nonNull(endDate);
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(CxrEmployeeAccessCycleRecord::getRuleSetCode, ruleSetCode)
				.in(CollectionUtils.isNotEmpty(assessRecordIds),CxrEmployeeAccessCycleRecord::getId, assessRecordIds)
				.in(CollectionUtils.isNotEmpty(stages),CxrEmployeeAccessCycleRecord::getStage, stages)
				.eq(Objects.nonNull(achievementStatus),CxrEmployeeAccessCycleRecord::getAchievementStatus, achievementStatus)
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
//				.between(dateNotNull,CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate, startDate, endDate)
//				.between(dateNotNull,CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate, startDate, endDate)
		);
		return list;
	}

	@Override
	public Boolean judgeIsAchievedInDateRange(Long employeeId, Integer currentLevel, Integer targetLevel, LocalDate startDate, LocalDate endDate){
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(CxrEmployeeAccessCycleRecord::getCurrentLevel, currentLevel)
				.eq(CxrEmployeeAccessCycleRecord::getTargetLevel, targetLevel)
				.eq(CxrEmployeeAccessCycleRecord::getAchievementStatus, AchievementStatusEnum.ACHIEVED.getCode())
				.between(CxrEmployeeAccessCycleRecord::getAchievementDate, startDate, endDate)
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
		return CollectionUtils.isNotEmpty(list);
	}

	@Override
	public CxrEmployeeAccessCycleRecord getCycleRecordInDateRange(Long employeeId, Integer currentLevel, Integer targetLevel, Integer cycleType
			,  Integer achievementStatus, LocalDate startDate, LocalDate endDate){
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(CxrEmployeeAccessCycleRecord::getCurrentLevel, currentLevel)
				.eq(CxrEmployeeAccessCycleRecord::getTargetLevel, targetLevel)
				.eq(CxrEmployeeAccessCycleRecord::getCycleType, cycleType)
				.eq(achievementStatus != null, CxrEmployeeAccessCycleRecord::getAchievementStatus, achievementStatus)
				.and(qw -> qw.between(CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate, startDate, endDate)
							.or()
							.between(CxrEmployeeAccessCycleRecord::getActualAssessmentEndDate, startDate, endDate))
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.orderByDesc(CxrEmployeeAccessCycleRecord::getId));
		if(CollectionUtils.isNotEmpty(list)) return list.get(0);
		return null;
	}

	@Override
	public CxrEmployeeAccessCycleRecord getUpStagePassAssessCycleRecordBy(Long employeeId, Integer currentLevel, Integer targetLevel) {
		List<CxrEmployeeAccessCycleRecord> list = this.list(new LambdaQueryWrapper<CxrEmployeeAccessCycleRecord>()
				.eq(CxrEmployeeAccessCycleRecord::getCxrEmployeeId, employeeId)
				.eq(CxrEmployeeAccessCycleRecord::getChangeType, ChangeTypeEnum.UPGRADE.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getCurrentLevel, currentLevel-1)
				.eq(CxrEmployeeAccessCycleRecord::getTargetLevel, targetLevel-1)
				.eq(CxrEmployeeAccessCycleRecord::getRunStatus, RunStatusEnum.ENDED.getCode())
				.eq(CxrEmployeeAccessCycleRecord::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
		);
		if(CollectionUtils.isEmpty(list)) return null;
		CxrEmployeeAccessCycleRecord cycleRecord = Collections.max(list, Comparator.comparing(CxrEmployeeAccessCycleRecord::getId));
		return cycleRecord;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<CxrEmployeeAccessCycleRecordLog> adjustCycleByDays(AdjustSourceTypeEnum adjustSourceType,Long sourceId,
			List<CxrEmployeeAccessCycleRecord> records,Date adjustStartDate,Date adjustEndDate,
			String adjustReason) {
		if (CollUtil.isEmpty(records)) return  null;
		log.info("调整周期开始：{}，员工ID：{}，调整日期：{}，调整结束日期：{}，调整原因：{}",adjustSourceType.getDescription(),records.get(0).getCxrEmployeeId(),adjustStartDate,adjustEndDate,adjustReason);
		//提取ID,获取拓展表信息
		List<Long> cycleRecordIds = records.stream().map(CxrEmployeeAccessCycleRecord::getId)
				.collect(Collectors.toList());
		List<CxrEmployeeAccessCycleRecordEx> cycleRecordExList = cxrEmployeeAccessCycleRecordExService.list(
				new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordEx>()
						.in(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, cycleRecordIds));
		//转map
		Map<Long, CxrEmployeeAccessCycleRecordEx> recordExMap = cycleRecordExList.stream().collect(Collectors.toMap(CxrEmployeeAccessCycleRecordEx::getAccessCycleId, Function.identity()));
		//调整逻辑:1.按月的只生成记录,不做周期修改 2.按天的同时修改.
		List<CxrEmployeeAccessCycleRecord> recordUpdateList =  new ArrayList<>();
		List<CxrEmployeeAccessCycleRecordLog> recordLogAddList =  new ArrayList<>();
		for (CxrEmployeeAccessCycleRecord record : records) {
			// //如果当前周期结束日期小于调整起始日期或者起始日期大于调整的结束日期，则不增加
			// if (record.getActualAssessmentEndDate().before(adjustStartDate) || record.getActualAssessmentStartDate().after(adjustEndDate)){
			// 	log.info("调整日期范围外，不增加调整记录:{}",record.getId());
			// 	continue;
			// }

			CxrEmployeeAccessCycleRecordEx cycleRecordEx = recordExMap.get(record.getId());
			Date actualAssessmentStartDateSrc = record.getActualAssessmentStartDate();
			Date actualAssessmentEndDateSrc = record.getActualAssessmentEndDate();
			Date originalAssessmentStartDate = record.getOriginalAssessmentStartDate();
			Date originalAssessmentEndDate = record.getOriginalAssessmentEndDate();

			CxrEmployeeAccessCycleRecordLog cxrEmployeeAccessCycleRecordLog = new CxrEmployeeAccessCycleRecordLog();
			cxrEmployeeAccessCycleRecordLog.setCompanyName(cycleRecordEx.getCompanyName());
			cxrEmployeeAccessCycleRecordLog.setRegionName(cycleRecordEx.getRegionName());
			cxrEmployeeAccessCycleRecordLog.setArea(cycleRecordEx.getArea());
			cxrEmployeeAccessCycleRecordLog.setProvince(cycleRecordEx.getProvince());
			cxrEmployeeAccessCycleRecordLog.setCity(cycleRecordEx.getCity());
			cxrEmployeeAccessCycleRecordLog.setDistrict(cycleRecordEx.getDistrict());
			cxrEmployeeAccessCycleRecordLog.setSiteName(cycleRecordEx.getSiteName());
			cxrEmployeeAccessCycleRecordLog.setEmployeeId(record.getCxrEmployeeId());
			cxrEmployeeAccessCycleRecordLog.setEmployeeName(cycleRecordEx.getEmployeeName());
			cxrEmployeeAccessCycleRecordLog.setCompanyId(cycleRecordEx.getCompanyId());
			cxrEmployeeAccessCycleRecordLog.setEmployeeNumber(cycleRecordEx.getEmployeeNumber());
			cxrEmployeeAccessCycleRecordLog.setCurrentLevel(record.getCurrentLevel());
			cxrEmployeeAccessCycleRecordLog.setStage(record.getStage());
			cxrEmployeeAccessCycleRecordLog.setEditLastStage(record.getStage());
			cxrEmployeeAccessCycleRecordLog.setOriginalAssessmentStartDate(DateUtils.getLocalDateFromDate(actualAssessmentStartDateSrc));
			cxrEmployeeAccessCycleRecordLog.setOriginalAssessmentEndDate(DateUtils.getLocalDateFromDate(actualAssessmentEndDateSrc));
			cxrEmployeeAccessCycleRecordLog.setEditLastAssessmentStartDate(DateUtils.getLocalDateFromDate(record.getActualAssessmentStartDate()));
			cxrEmployeeAccessCycleRecordLog.setEditLastAssessmentEndDate(DateUtils.getLocalDateFromDate(record.getActualAssessmentEndDate()));
			cxrEmployeeAccessCycleRecordLog.setAccessCycleId(record.getId());
			cxrEmployeeAccessCycleRecordLog.setUpdateCycleReason(adjustReason);
			cxrEmployeeAccessCycleRecordLog.setRerunFlag(1);
			cxrEmployeeAccessCycleRecordLog.setUpdateTime(new Date());
			cxrEmployeeAccessCycleRecordLog.setUpdateByName("admin");
			cxrEmployeeAccessCycleRecordLog.setAdjustStartDate(DateUtils.getLocalDateFromDate(adjustStartDate));
			cxrEmployeeAccessCycleRecordLog.setAdjustEndDate(DateUtils.getLocalDateFromDate(adjustEndDate));
			cxrEmployeeAccessCycleRecordLog.setSourceType(adjustSourceType.getCode());
			cxrEmployeeAccessCycleRecordLog.setSourceId(sourceId);
			accessCycleRecordLogService.save(cxrEmployeeAccessCycleRecordLog);

			// if(record.getOriginalAssessmentStartDate().after(adjustStartDate)){
			// 	adjustDays = (int) DateUtil.between(record.getOriginalAssessmentStartDate(),adjustEndDate, DateUnit.DAY,false)+1;
			// }else{
			// 	adjustDays = (int) DateUtil.between(adjustStartDate,adjustEndDate, DateUnit.DAY,false)+1;
			// }

			if (record.getCycleType().equals(AccessCycleTypeEnum.BY_DAY.getCode())){
				// 计算延期（包含当前调整）
				DelayCalculationResult delayResult = cycleDelayManager.autoProcessDelayWithResult(record, cxrEmployeeAccessCycleRecordLog);
				log.info("调整周期结束：{}，员工ID：{}，调整日期：{}，调整结束日期：{}，调整原因：{}",adjustSourceType.getDescription(),records.get(0).getCxrEmployeeId(),adjustStartDate,adjustEndDate,adjustReason);
				//截断下getDelayProcessDescription，避免超限
				record.setUpdateCycleReason(delayResult.getDelayProcessDescription().substring(0,
						Math.min(delayResult.getDelayProcessDescription().length(), 500)));
				//按原日期修改
				CxrEmployeeAccessCycleRecord updateCycleRecord = new CxrEmployeeAccessCycleRecord();
				updateCycleRecord.setId(record.getId());
				if (delayResult.getHasDelay()){
					updateCycleRecord.setActualAssessmentEndDate(delayResult.getNewEndDate());
					updateCycleRecord.setUpdateCycleReason(delayResult.getDelayProcessDescription());
					cxrEmployeeAccessCycleRecordLog.setEditLastAssessmentEndDate(DateUtils.getLocalDateFromDate(delayResult.getNewEndDate()));
					cxrEmployeeAccessCycleRecordLog.setUpdateCycleReason(delayResult.getDelayProcessDescription());

				}else{
					cxrEmployeeAccessCycleRecordLog.setEditLastAssessmentEndDate(DateUtils.getLocalDateFromDate(originalAssessmentEndDate));
					cxrEmployeeAccessCycleRecordLog.setEditLastAssessmentStartDate(DateUtils.getLocalDateFromDate(originalAssessmentStartDate));
					updateCycleRecord.setActualAssessmentStartDate(originalAssessmentStartDate);
					updateCycleRecord.setActualAssessmentEndDate(originalAssessmentEndDate);
					updateCycleRecord.setUpdateCycleReason("无实际延期天数");
				}
				accessCycleRecordLogService.updateById(cxrEmployeeAccessCycleRecordLog);
				recordLogAddList.add(cxrEmployeeAccessCycleRecordLog);
				recordUpdateList.add(updateCycleRecord);
			}else{
				log.info("非按日周期，不直接延期处理");
			}
		}
		this.updateBatchById(recordUpdateList);

		return recordLogAddList;
	}



	@Override
	public void addAccessCycleVByUser(CxrEmployeeAccessCycleRecordBo bo) {
		EmployeeStageEnum stageEnum = EmployeeStageEnum.fromSequence(bo.getStage());
		LoginInfo loginUser = LoginUtil.getLoginUser();
		CxrEmployeeAccessCycleRecord record = new CxrEmployeeAccessCycleRecord();
		record.setRuleId(0L);
		record.setRuleSetId(0L);
		record.setRuleSetCode(ruleSetCode);
		record.setRuleConditionGroupId(0L);
		record.setCycleType(0);
		record.setStage(bo.getStage());
		record.setCxrEmployeeId(bo.getCxrEmployeeId());
		record.setCurrentLevel(stageEnum.getSrcLevel().getLevel());
		record.setTargetLevel(stageEnum.getTargetLevel().getLevel());
		record.setChangeType(stageEnum.getChangeType().getCode());
		record.setOriginalAssessmentStartDate(bo.getOriginalAssessmentStartDate());
		record.setOriginalAssessmentEndDate(bo.getOriginalAssessmentEndDate());
		record.setActualAssessmentStartDate(bo.getOriginalAssessmentStartDate());
		record.setActualAssessmentEndDate(bo.getOriginalAssessmentEndDate());
		record.setAssStatusEndNote(AssStatusEndNoteEnum.MANUAL_MODIFY.getDescription());
		record.setRunStatus(RunStatusEnum.ENDED.getCode());
		record.setAchievementStatus(Objects.nonNull(bo.getAchievementDate())?AchievementStatusEnum.ACHIEVED.getCode():AchievementStatusEnum.NOT_ACHIEVED.getCode());
		record.setAchievementDate(bo.getAchievementDate());
		if(Objects.nonNull(bo.getAchievementDate())&&Objects.isNull(bo.getEffectiveDate())){
			throw new ServiceException("达标日期不为空时，生效日期不能为空");
		}
		record.setEffectiveDate(bo.getEffectiveDate());
		record.setExecutionStatus(bo.getEffectiveDate()!=null?ExecutionStatusEnum.EXECUTED.getCode():ExecutionStatusEnum.NOT_EXECUTED.getCode());
		record.setEditNote(bo.getEditNote());
		record.setCreateBy(loginUser.getUserId());
		record.setCreateByType("sys");
		record.setCreateTime(new Date());
		record.setCreateByName(loginUser.getUserName());
		record.setUpdateByName(loginUser.getUserName());
		record.setUpdateBy(loginUser.getUserId());
		record.setUpdateTime(new Date());
		this.save(record);
		cxrEmployeeAccessCycleRecordExService.addEmployeeAccessCycleRecordEx(bo.getCxrEmployeeId(),record.getId());
	}	

	@Override
	public void updateByUser(CxrEmployeeAccessCycleRecordBo bo) {
		LoginInfo loginUser = LoginUtil.getLoginUser();
		EmployeeStageEnum stageEnum = EmployeeStageEnum.fromSequence(bo.getStage());
		CxrEmployeeAccessCycleRecord record = new CxrEmployeeAccessCycleRecord();
		record.setId(bo.getId());
		record.setStage(bo.getStage());
		record.setCxrEmployeeId(bo.getCxrEmployeeId());
		record.setCurrentLevel(stageEnum.getSrcLevel().getLevel());
		record.setTargetLevel(stageEnum.getTargetLevel().getLevel());
		record.setChangeType(stageEnum.getChangeType().getCode());
		record.setOriginalAssessmentStartDate(bo.getOriginalAssessmentStartDate());
		record.setOriginalAssessmentEndDate(bo.getOriginalAssessmentEndDate());
		record.setActualAssessmentStartDate(bo.getOriginalAssessmentStartDate());
		record.setActualAssessmentEndDate(bo.getOriginalAssessmentEndDate());
		record.setAssStatusEndNote(AssStatusEndNoteEnum.MANUAL_MODIFY.getDescription());
		record.setRunStatus(RunStatusEnum.ENDED.getCode());
		if(Objects.nonNull(bo.getAchievementDate())&&Objects.isNull(bo.getEffectiveDate())){
			throw new ServiceException("达标日期不为空时，生效日期不能为空");
		}
		record.setAchievementStatus(Objects.nonNull(bo.getAchievementDate())?AchievementStatusEnum.ACHIEVED.getCode():AchievementStatusEnum.NOT_ACHIEVED.getCode());
		record.setAchievementDate(bo.getAchievementDate());
		record.setEffectiveDate(bo.getEffectiveDate());
		record.setExecutionStatus(bo.getEffectiveDate()!=null?ExecutionStatusEnum.EXECUTED.getCode():ExecutionStatusEnum.NOT_EXECUTED.getCode());
		record.setEditNote(bo.getEditNote());
		record.setUpdateBy(loginUser.getUserId());
		record.setUpdateTime(new Date());
		record.setUpdateByName(loginUser.getUserName());
		this.updateById(record);
	}

	@Override
	public void removeByUser(Long id) {
		LoginInfo loginUser = LoginUtil.getLoginUser();
		CxrEmployeeAccessCycleRecord record = new CxrEmployeeAccessCycleRecord();
		record.setId(id);
		record.setDeleteBy(loginUser.getUserId());
		record.setDeleteTime(new Date());
		record.setDeleteByName(loginUser.getUserName());
		record.setDeleteStatus(DeleteStatus.DELETED.getValue());
		record.setEditNote("手动删除历史周期");
		this.updateById(record);
	}	
	
	



}




