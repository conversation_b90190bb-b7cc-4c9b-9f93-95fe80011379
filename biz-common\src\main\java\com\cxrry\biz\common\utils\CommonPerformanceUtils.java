package com.cxrry.biz.common.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Sets;
import com.ruoyi.calculate.api.domain.vo.PerformanceVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CommonPerformanceUtils {

    /**
     * 根据员工ID和实际评估日期范围，填充员工每月的绩效信息
     * 如果在指定的日期范围内有月份的绩效数据缺失，则为这些月份创建绩效记录并设置为0
     *
     * @param employeeId 员工ID
     * @param actualAssessmentStartDate 实际评估开始日期
     * @param actualAssessmentEndDate 实际评估结束日期
     * @param employeePerformance 员工绩效列表，将被填充缺失月份的绩效数据
     */
    public static void fillMonthPerformance(Long employeeId, Date actualAssessmentStartDate, Date actualAssessmentEndDate, List<PerformanceVo> employeePerformance){
        // 计算实际评估开始日期和结束日期之间的月数，包括开始和结束月份
        long monthCount = DateUtil.betweenMonth(actualAssessmentStartDate, actualAssessmentEndDate, Boolean.TRUE);
        // 日志记录月份数量，同时将monthCount增加1，以包含起始月份
        log.info("monthCount:{}",monthCount++);

        // 如果计算出的月份数量大于员工绩效列表的大小，则进行数据填充
        if(monthCount > employeePerformance.size()){
            // 格式化实际评估开始日期为YYYY-MM格式
            String startMonth = DateUtils.format(actualAssessmentStartDate, DateUtils.DATE_YYYY_MM);
            // 创建一个HashSet存储从实际评估开始日期到结束日期的所有月份
            Set<String> monthsSet = Sets.newHashSet(startMonth);

            // 循环遍历，生成所有月份并添加到monthsSet中
            for (int i = 1; i < monthCount; i++) {
                DateTime dateTime = DateUtil.beginOfMonth(DateUtil.offsetMonth(actualAssessmentStartDate, i));
                monthsSet.add(DateUtils.format(dateTime, DateUtils.DATE_YYYY_MM));
            }

            // 从employeePerformance中提取所有已有的月份，并转换为Set集合
            Set<String> fecthMonth = employeePerformance.stream().map(item -> item.getMonths()).collect(Collectors.toSet());
            // 从所有月份中移除已存在的月份，剩下需要添加的月份
            monthsSet.removeAll(fecthMonth);

            // 遍历需要添加的月份，创建PerformanceVo对象并添加到employeePerformance列表中
            monthsSet.forEach(item->{
                PerformanceVo performanceVo = new PerformanceVo();
                performanceVo.setEmployeeId(employeeId);
                performanceVo.setMonths(item);
                performanceVo.setPerformance(BigDecimal.ZERO);
                employeePerformance.add(performanceVo);
            });
        }
    }

    public static void main(String[] args) {
        List<PerformanceVo> employeePerformance = new ArrayList<>();
        PerformanceVo performanceVo = new PerformanceVo();
        performanceVo.setPerformance(new BigDecimal(17320));
        performanceVo.setMonths("2024-10");
        employeePerformance.add(performanceVo);
        fillMonthPerformance(null,DateUtil.parse("2024-10-06"),DateUtil.parse("2024-11-04"),employeePerformance);
        System.out.println();
    }
}
