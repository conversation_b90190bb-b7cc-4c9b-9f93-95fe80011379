package com.cxrry.biz.common.rule;

import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 *
 * @TableName cxr_employee_access_cycle_record
 */
@Data
public class CxrEmployeeAccessCycleRecordDTO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 规则ID
     */
    private Long ruleId;
    /**
     * 规则组ID
     */
    private Long ruleConditionGroupId;


    /**
     *  规则集编码
     */
    private String ruleSetCode;

    /**
     * 规则集ID
     */
    private Long ruleSetId;

    /**
     * 阶段编码
     */
    private Integer stage;

    /**
     * 销售代理ID
     */
    private Long cxrEmployeeId;

    /**
     * 当前等级
     */
    private Integer currentLevel;

    /**
     * 变更类型：0无操作，1升级，2降级
     */
    private Integer changeType;

    /**
     * 目标等级
     */
    private Integer targetLevel;


    /**
     * 周期类型 1.按天 2.按自然月  3.按当月剩余天数 4.按连续月份
     */
    private Integer cycleType;

    /**
     * 提前生成的来源阶段
     */
    private Integer advanceSrcStage;

    /**
     * 原考核起始日期
     */
    private Date originalAssessmentStartDate;

    /**
     * 原考核结束日期
     */
    private Date originalAssessmentEndDate;

    /**
     * 实际考核起始日期
     */
    private Date actualAssessmentStartDate;

    /**
     * 实际考核结束日期
     */
    private Date actualAssessmentEndDate;

    /**
     * 考核状态：0未开始，1进行中，2已结束
     */
    private Integer runStatus;

    /**
     * 考核状态修改起始日期
     */
    private Date assStatusStartDate;
    /**
     * 考核状态修改结束日期
     */
    private Date assStatusEndDate;

    /**
     * 达标日期
     */
    private Date achievementDate;

    /**
     * 达标状态：0未达标，1已达标
     */
    private Integer achievementStatus;

    /**
     * 达标执行的生效日期
     */
    private Date effectiveDate;

    /**
     * 执行状态：0未执行，1已执行
     */
    private Integer executionStatus;


    @TableField(exist = false)
    private Date runDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 实际考核起始日期
     */
    private Date startDate;
    /**
     * 实际考核结束日期
     */
    private Date endDate;
}