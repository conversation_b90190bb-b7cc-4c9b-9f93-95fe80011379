package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 工单受理节点表
 * @TableName work_order_priority_node
 */
@TableName(value ="work_order_priority_node")
@Data
public class WorkOrderPriorityNode implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;


    /**
     * 工单表ID
     */
    private Long workOrderId;

    /**
     * 工单流程配置表ID
     */
    private Long workOrderFlowConfigId ;

    /**
     * 工单流程配置节点表ID
     */
    private Long workOrderFlowNodeConfigId ;


    /**
     * 受理类型：0或签 1会签
     */
    private Integer priorityType;

    /**
     * 受理状态：0待受理 1受理中 2已完结 3.已逾期
     * @see WorkOrderProcessStatusEnums
     */
    private Integer priorityStatus;

    /**
     * 受理人限定范围 1指定人 2指定职位
     */
    private Integer priorityUserType;

    /**
     * 节点开始时间
     */
    private Date startTime;

    /**
     * 节点要求处理时长（浮点类型，小时数）
     */
    private Double limitFinishHours;

    /**
     * 节点要求完成时间
     */
    private Date limitFinishTime;

    /**
     * 节点实际完成时间
     */
    private Date actFinishTime;

    /**
     * 节点实际处理时长（浮点类型，小时数）
     */
    private Double actFinishHours;

    /**
     * 受理节点名称
     */
    private String acceptedNodeName;

    /**
     * open_sort 重启序号
     */
    private Integer openSort;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人类型(详情见字典)
     */
    private String createByType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    private String updateByType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除人id
     */
    private Long deleteBy;

    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    private String deleteByType;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;


    /**
     * 节点等级
     */
    private Integer nodeLevel;


    /**
     * 流程类型 1真实节点 2虚拟节点
     */
    private Integer processType;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



}