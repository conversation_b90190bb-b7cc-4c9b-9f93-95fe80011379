package com.cxrry.biz.common.serializer;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Set;
import com.fasterxml.jackson.databind.JsonSerializer;

public class CustomStringSetSerializer extends JsonSerializer<Set<String>>{

  @Override
  public void serialize(Set<String> value, JsonGenerator jsonGenerator, SerializerProvider serializers) throws IOException {
    jsonGenerator.writeStartArray();
    for (String item : value) {
      item = item.replaceAll("[【】]", "");
      String[] parts = item.split(",");
      StringBuilder formattedItem = new StringBuilder();
      for (int i = 0; i < parts.length; i++) {
        if (i == parts.length - 1) { // 最后一个字段是数字
          try {
            BigDecimal bigDecimal = new BigDecimal(parts[i].trim());
            String plainString = bigDecimal.stripTrailingZeros().toPlainString();
            formattedItem.append(plainString);
          } catch (NumberFormatException e) {
            // 如果解析失败，写入默认值 "0"
            formattedItem.append(BigDecimal.ZERO.toPlainString());
          }
        } else {
          formattedItem.append(parts[i].trim());
        }
        if (i < parts.length - 1) {
          formattedItem.append(","); // 保留分隔符
        }
      }
      jsonGenerator.writeString(formattedItem.toString());
    }
    jsonGenerator.writeEndArray();
  }
}