package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.List;

import com.cxrry.biz.common.json.JudgmentValue;
import com.cxrry.biz.common.typeHandler.TypeHandlerConstant;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 
 * @TableName work_order_trigger_active
 */

@TableName(value ="work_order_trigger_active", autoResultMap = true)
@Data
public class WorkOrderTriggerActive implements Serializable {

    @TableId
    private Long id;
    /**
     * 
     */

    private Long triggerId;

    /**
     * 执行动作类型 1.给受理方发送短信消息 2.给受理方发送公众号消息 3.更改工单受理人 4.添加关注人
     * @see com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerActiveTypeEnums
     */
    private Integer activeType;

    /**
     * 
     */
    @TableField(typeHandler = TypeHandlerConstant.judgmentValueJson.class)
    private List<JudgmentValue> targetIdList;

    /**
     * 公众号模板类型  1.投诉 2.退款
     */
    private Integer wxMpTmpType;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    /**
     * 类型 1用户ID、2职位ID、3角色ID
     */
    private Integer followerType;

    private String username;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}