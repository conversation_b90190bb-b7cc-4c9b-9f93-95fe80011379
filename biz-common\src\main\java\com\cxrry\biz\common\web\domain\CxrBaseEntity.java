package com.cxrry.biz.common.web.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.BaseColumn;
import com.ruoyi.common.core.web.domain.base.Entity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * Entity基类
 *
 * <AUTHOR> Li
 */
@Data
public class CxrBaseEntity implements Entity, Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 搜索值
     */
    @ApiModelProperty(value = "搜索值")
    @TableField(exist = false)
    private String searchValue;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Long createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty(value = "更新者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @BaseColumn
    private Long updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @BaseColumn
    private Date updateTime;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();

    /**
     * 乐观锁
     */
    @ApiModelProperty(value = "乐观锁")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Long revision;

    /**
     * 创建者名称
     */
    @ApiModelProperty(value = "创建者名称")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private String createByName;

    /**
     * 创建者类型(详情见字典)
     */
    @ApiModelProperty(value = "创建者类型(详情见字典)")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private String createByType;

    /**
     * 更新者名称
     */
    @ApiModelProperty(value = "更新人名称")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @BaseColumn
    private String updateByName;

    /**
     * 更新者类型(详情见字典)
     */
    @ApiModelProperty(value = "更新者类型(详情见字典)")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @BaseColumn
    private String updateByType;

    /**
     * 删除者
     */
    @ApiModelProperty(value = "删除者")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Long deleteBy;

    /**
     * 删除者名称
     */
    @ApiModelProperty(value = "删除者名称")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String deleteByName;

    /**
     * 删除者类型(详情见字典)
     */
    @ApiModelProperty(value = "删除者类型(详情见字典)")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String deleteByType;

    /**
     * 删除时间
     */
    @ApiModelProperty(value = "删除时间")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    @ApiModelProperty(value = "删除状态(详情见字典)")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    // @TableLogic(value = DeleteStatus.not_deleted,delval = DeleteStatus.deleted)
    // 由于使用上面的逻辑删除注解导致查询时会自动限制只能查询删除状态为未删除的数据，且无法使用修改语句修改删除状态，操作不灵活，所以暂时放弃使用该注解
    private String deleteStatus;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Long sortNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @BaseColumn
    private String remark;

    /**
     * 部门id(公司id)
     */
    @ApiModelProperty(value = "部门id(公司id)")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Long sysDeptId;

    /**
     * 备用id
     */
    @ApiModelProperty(value = "备用id")
    @BaseColumn
    private Long spareId;

    @TableField(exist = false)
    private List<Long> qylSiteIds;
}
