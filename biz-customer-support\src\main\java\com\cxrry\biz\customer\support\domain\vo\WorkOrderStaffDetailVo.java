package com.cxrry.biz.customer.support.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.dto.WorkOrderOperationDto;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 配送端-工单详情视图
 *
 */
@Data
public class WorkOrderStaffDetailVo  extends WorkOrderBaseEntity{


	private Long id;
	@ApiModelProperty(value = "工单编号")
	private String code;

	@ApiModelProperty(value = "工单创建时间")
	private Date createTime;

	@ApiModelProperty(value = "投诉类型名称")
	private String orderMenuRemark;

	@ApiModelProperty(value = "优先级 1低 2一般 3紧急 4非常紧急")
	private Integer priority;

	@ApiModelProperty(value = "逾期次数")
	private Integer overdueNum;

	@ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
	private Integer processStatus;

	@ApiModelProperty(value = "客户名称")
	private String customerName;

	@ApiModelProperty(value = "客户主手机号")
	private String customerMainPhone;

	@ApiModelProperty(value = "客户地址")
	private String customerAddress;

	@ApiModelProperty(value = "本单要求完成时间")
	private Date limitFinishTime;

	@ApiModelProperty(value = "最新节点要求结束时间")
	private Date lastNodeLimitFinishTime;

	@ApiModelProperty(value = "受理人")
	private String acceptorStr;

	@ApiModelProperty(value = "当前受理人id")
	private Long  acceptorId;


	@ApiModelProperty(value = "工单内容")
	private String describetion;

	@ApiModelProperty(value = "工单服务记录")
	private List<WorkOrderOperationDto> operationDtoList;

	@ApiModelProperty(value = "投诉对象")
	private String complaintObjectName;
	@ApiModelProperty(value = "配送员")
	private String employeeName;

	/**
	 * Url相对路径，逗号隔开
	 */
	@TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
	private List<FileUrlConfig> fileUrl;

	@TableField(typeHandler = JsonTypeHandlerConstant.OperatorMessageHandler.class)
	private List<OperatorMessageConfig> operatorMessage;


	private String deleteStatus;
}
