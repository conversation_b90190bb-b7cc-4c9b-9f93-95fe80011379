package com.cxrry.biz.customer.support.controller;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderProcessingTotalBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderProcessingTotalVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderTotalNumberVo;
import com.cxrry.biz.customer.support.service.WorkOrderProcessingTotalService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
    value = "工单排名-受理工单统计",
    tags = {"工单排名-受理工单统计"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderProcessingTotal")
public class WorkOrderProcessingTotalController {
    private final WorkOrderProcessingTotalService workOrderProcessingTotalService;


  @ApiOperation("工单排名-受理工单统计")
  @SaCheckPermission("support:workOrder:workOrderProcessingTotalPage")
  @PostMapping("/page")
  public R<PageTableDataInfo<WorkOrderProcessingTotalVo>> workOrderProcessingTotalPage(@RequestBody WorkOrderProcessingTotalBo bo) {
    return R.ok(workOrderProcessingTotalService.workOrderProcessingTotalPage(bo));
  }

  @ApiOperation("工单排名-受理工单底部合计")
  @PostMapping("/total")
  public R<WorkOrderTotalNumberVo> workOrderProcessingTotal(@RequestBody WorkOrderProcessingTotalBo bo) {
    return R.ok(workOrderProcessingTotalService.workOrderProcessingTotal(bo));
  }

  @ApiOperation("工单排名-受理工单导出")
  @PostMapping("/export")
  public void  export(@RequestBody WorkOrderProcessingTotalBo bo, HttpServletResponse response) throws IOException {
    this.workOrderProcessingTotalService.export(bo,response);
  }






}
