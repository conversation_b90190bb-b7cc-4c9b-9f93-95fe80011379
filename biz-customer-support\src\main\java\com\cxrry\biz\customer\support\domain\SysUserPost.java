package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户和岗位关联 sys_user_post
 *
 * <AUTHOR> <PERSON>
 */
@Data
@NoArgsConstructor
@TableName("sys_user_post")
@ApiModel("用户和岗位关联")
public class SysUserPost {

    /**
     * 用户ID
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 用户ID
     */
//    @TableId(type = IdType.INPUT)
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 岗位ID
     */
    @ApiModelProperty(value = "岗位ID")
    private Long postId;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;

    /**
     * 部门岗位别名
     */
    @ApiModelProperty(value = "部门岗位别名")
    private String postAliasName;

    /**
     * 修改版本号
     */
    @ApiModelProperty(value = "修改版本号")
    private Integer version;

    /**
     * 是否删除：0未删除 1已删除
     */
    @ApiModelProperty(value = "是否删除：0未删除 1已删除")
    private Integer deleteStatus;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createBy;

    /**
     * 删除人
     */
    @ApiModelProperty(value = "删除人")
    private Long deleteBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 删除时间
     */
    @ApiModelProperty(value = "删除时间")
    private Date deleteTime;

}