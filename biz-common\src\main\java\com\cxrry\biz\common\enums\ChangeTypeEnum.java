package com.cxrry.biz.common.enums;

public enum ChangeTypeEnum {
    NONE(0, "无操作"),
    UPGRADE(1, "升级"),
    DOWNGRADE(2, "降级");

    private int code;
    private String description;

    // 构造函数
    ChangeTypeEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // getCode方法
    public int getCode() {
        return code;
    }

    // getDescription方法
    public String getDescription() {
        return description;
    }

    // 可以根据code值来获取枚举项
    public static ChangeTypeEnum fromCode(int code) {
        for (ChangeTypeEnum type : ChangeTypeEnum.values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }
}