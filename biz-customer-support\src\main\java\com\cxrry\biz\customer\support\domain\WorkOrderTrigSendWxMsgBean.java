package com.cxrry.biz.customer.support.domain;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 触发器发送微信公众号消息包装类
 */
@Data
public class WorkOrderTrigSendWxMsgBean {
	@ApiModelProperty(value = "微信的openid")
	private String wxOpenid;

	@ApiModelProperty(value = "工单类型")
	private String workOrderType;

	@ApiModelProperty(value = "模板id")
	private String tenantId;

	@ApiModelProperty(value = "门店")
	private String shop;

	@ApiModelProperty(value = "内容")
	private String  contents;

	private Long workOrderId;


}
