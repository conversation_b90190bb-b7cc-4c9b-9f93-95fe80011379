package com.cxrry.biz.customer.support.cmp;

import cn.hutool.json.JSONUtil;
import com.cxrry.common.trace.TraceUtils;
import com.cxrry.liteflow.LiteFlowUtil;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <p>测试组件，利用方法注解定义不同类型的组件</p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Slf4j
@RequiredArgsConstructor
@Component
//@LiteflowComponent
//@LiteflowCmpDefine
public class TestCmp {

    private final FlowExecutor flowExecutor;
    
    public void test(String param) {
        log.info("test param:{}", param);
        TestDTO testDTO = new TestDTO(param);
        log.info(JSONUtil.toJsonStr(testDTO));
        // 第二个入参为 testDTO, 从requestData 获取
        // 第二个入参为 testDTO, 从 组件上下文 获取
        LiteflowResponse liteflowResponse = flowExecutor.execute2RespWithRid("testChain", testDTO, "工单1100", testDTO);
        String stepTimeOrCause = LiteFlowUtil.getStepTimeOrCause(liteflowResponse);
        log.warn("stepTimeOrCause:{}", stepTimeOrCause);
    }

    // 重载方法
//    @LiteflowMethod(value = LiteFlowMethodEnum.IS_ACCESS)
//    public boolean isAccess(NodeComponent bindCmp) {
//        log.info(" isAccess");
//        return true;
//    }

    //  定义a组件
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "a", nodeName = "a组件")
    public void a(NodeComponent bindCmp) {
        log.info("param={}", JSONUtil.toJsonStr(LiteFlowUtil.getParam(bindCmp, TestDTO.class)));
        TestDTO testDTO = bindCmp.getContextBean(TestDTO.class);
        testDTO.addName(" a");
        log.info(JSONUtil.toJsonStr(testDTO));
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "b",  nodeName = "b组件")
    public void b(NodeComponent bindCmp) {
        TestDTO testDTO = bindCmp.getContextBean(TestDTO.class);
        testDTO.addName(" b");
        log.info(JSONUtil.toJsonStr(testDTO));
    }

    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_BOOLEAN, nodeId = "c", nodeName = "C组件", nodeType = NodeTypeEnum.BOOLEAN)
    public boolean ifcmp(NodeComponent bindCmp) {
        log.info("ifcmp");
        return true;
    }


    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "d", nodeName = "d组件", nodeType = NodeTypeEnum.COMMON)
    public void dcmp(NodeComponent bindCmp) {
        log.info("dcmp");
    }

    @Data
    class TestDTO {
        private String tid= TraceUtils.get();

        private String name;

        public TestDTO(String name) {
            this.name = name;
        }

        public void addName(String name) {
            this.name = this.name + name;
        }
    }
}
