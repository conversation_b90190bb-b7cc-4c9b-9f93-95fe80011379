package com.cxrry.biz.customer.support.domain.bo;

import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderUpdateBo {
  private Long id;

  @ApiModelProperty("1低 2一般 3紧急 4非常紧急")
  private Integer priority;

  @ApiModelProperty("工单描述内容")
  private String describetion;

  @ApiModelProperty("工单附件相对路径url，多个用逗号隔开")
  private List<FileUrlConfig> fileUrl;
  @ApiModelProperty(value = "关注人")
  List<WorkOrderFollower> workOrderFollowerList;

  @ApiModelProperty(value = "投诉对象id")
  private Long complaintObjectId;
  @ApiModelProperty(value = "投诉对象名称")
  private String complaintObjectName;

  private String complaintObjectNumber; // 投诉对象的编号

  private String complaintObjectSiteName; // 投诉对象站点名称

  private String complaintObjectSiteMark; // 投诉对象站点编号

  private Long complaintObjectSiteId; // 投诉对象站点id

  private String complaintObjectDeptName; // 投诉对象公司名

  private Long complaintObjectDeptId; // 投诉对象公司id

  private Long complaintObjectRegionId; // 投诉对象区域id

  private String complaintObjectRegionName; // 投诉对象区域名称
  private Long complaintObjectRootRegionId; // 投诉对象大区id

  private String complaintObjectRootRegionName; // 投诉对象大区名称

  private String complaintObjectProvince; // 投诉对象省

  private String complaintObjectCity; // 投诉对象市
  private String complaintObjectArea; // 投诉对象区

  @ApiModelProperty(value = "开单销售代理")
  private List<OperatorMessageConfig> operatorMessage;

}
