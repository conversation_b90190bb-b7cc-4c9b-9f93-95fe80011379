package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/9/21 14:37
 **/
@ApiModel
@Data
public class CustomerAddressMilkDistributionInfo implements Serializable {

    /**
     * 星期一
     */
    private List<MilkDistributionInfo> monday;

    /**
     * 星期二
     */
    private List<MilkDistributionInfo> tuesday;

    /**
     * 星期三
     */
    private List<MilkDistributionInfo> wednesday;

    /**
     * 星期四
     */
    private List<MilkDistributionInfo> thursday;

    /**
     * 星期五
     */
    private List<MilkDistributionInfo> friday;

    /**
     * 星期六
     */
    private List<MilkDistributionInfo> saturday;


}
