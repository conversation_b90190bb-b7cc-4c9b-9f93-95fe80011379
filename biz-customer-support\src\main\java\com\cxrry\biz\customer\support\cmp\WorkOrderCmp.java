package com.cxrry.biz.customer.support.cmp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cxrry.biz.common.json.JudgmentValue;
import com.cxrry.biz.common.mq.MqBizConst;
import com.cxrry.biz.common.utils.MqUtil;
import com.cxrry.biz.customer.support.config.WxMpTmpProperties;
import com.cxrry.biz.customer.support.domain.CxrEmployee;
import com.cxrry.biz.customer.support.domain.CxrEmployeePost;
import com.cxrry.biz.customer.support.domain.SysUserPost;
import com.cxrry.biz.customer.support.domain.WorkOrder;
import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.biz.customer.support.domain.WorkOrderOperation;
import com.cxrry.biz.customer.support.domain.WorkOrderTrigSendWxMsgBean;
import com.cxrry.biz.customer.support.domain.WorkOrderTrigger;
import com.cxrry.biz.customer.support.domain.WorkOrderTriggerActive;
import com.cxrry.biz.customer.support.domain.WorkOrderTriggerCondition;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderCmpInParamObject;
import com.cxrry.biz.customer.support.domain.vo.UserInfoServiceVo;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderClassificationEnums;
import com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerActiveSendUserTypeEnums;
import com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerActiveTypeEnums;
import com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerConditionJudgmentType;
import com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerConditionalGroup;
import com.cxrry.biz.customer.support.mapper.erp.CxrEmployeeMapper;
import com.cxrry.biz.customer.support.mapper.erp.CxrEmployeePostMapper;
import com.cxrry.biz.customer.support.mapper.erp.SysDeptMapper;
import com.cxrry.biz.customer.support.mapper.erp.SysUserPostMapper;
import com.cxrry.biz.customer.support.service.WorkOrderFollowerService;
import com.cxrry.biz.customer.support.service.WorkOrderOperationService;
import com.cxrry.biz.customer.support.service.WorkOrderService;
import com.cxrry.biz.customer.support.service.WorkOrderStrategy;
import com.cxrry.biz.customer.support.service.WorkOrderTriggerActiveService;
import com.cxrry.biz.customer.support.utils.WorkOrderRoleCmpUtil;
import com.ruoyi.common.core.enums.ChargeStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.PostType;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.system.api.RemoteUserService;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.model.LoginUser;
import com.yomahub.liteflow.annotation.LiteflowMethod;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.enums.LiteFlowMethodEnum;
import com.yomahub.liteflow.enums.NodeTypeEnum;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <p>工单组件，利用方法注解定义不同类型的组件</p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Slf4j
@RequiredArgsConstructor
@Component
//@LiteflowComponent
//@LiteflowCmpDefine
public class WorkOrderCmp {

    private final FlowExecutor flowExecutor;
    private final WorkOrderRoleCmpUtil workOrderRoleCmpUtil;

    private final WorkOrderTriggerActiveService workOrderTriggerActiveService;

    private final WorkOrderFollowerService workflowFollowerService;

    private final WorkOrderService workOrderService;
    private final WorkOrderOperationService workOrderOperationService;
    private final CxrEmployeePostMapper cxrEmployeePostMapper;
    private final SysUserPostMapper sysUserPostMapper;
    private final SysDeptMapper sysDeptMapper;
    private final CxrEmployeeMapper cxrEmployeeMapper;
    private final MqUtil mQUtil;
    private final WxMpTmpProperties wxMpTmpProperties;


    @DubboReference
    private RemoteUserService remoteUserService;

//    @PostConstruct
    public void test() {
//        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
//        stringObjectHashMap.put("type", "1");
//        workOrderRoleCmpUtil.startAddRole(stringObjectHashMap);
    }

    /**
     * 新增or编辑决策组件
     */
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_SWITCH, nodeId = "addOrEditChoose", nodeName = "新增编辑决策组件", nodeType = NodeTypeEnum.SWITCH)
    public String addOrEditChoose(NodeComponent bindCmp) {
        log.info("addOrEditChoose");
        String methodName = bindCmp.getRequestData();
        log.info("addOrEditChoose requestData:{}", methodName);
        return "tag:"+bindCmp.getRequestData();
    }

    /**
     * 前置条件判断组件
     */
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_BOOLEAN, nodeId = "preconditionJudge", nodeName = "前置判断条件组件", nodeType = NodeTypeEnum.BOOLEAN)
    public Boolean preconditionJudge(NodeComponent bindCmp) {
        log.info("preconditionJudge");
        WorkOrderTrigger workOrderTrigger = bindCmp.getContextBean(WorkOrderTrigger.class);
        WorkOrderCmpInParamObject workOrderCmpInParamObject = bindCmp.getContextBean(WorkOrderCmpInParamObject.class);
        WorkOrderStrategy oldWorkOrderObj = workOrderCmpInParamObject.getOldWorkOrderObj();
        List<WorkOrderTriggerCondition> triggerConditions = workOrderTrigger.getTriggerConditions();
        //获取前置条件
        List<WorkOrderTriggerCondition> preconditionList =
            triggerConditions.stream().filter(x -> x.getJudgmentType().equals(
                    WorkOrderTriggerConditionJudgmentType.PRECONDITION.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(preconditionList)){
            log.info("preconditionList is empty");
            return true;
        }
        //获取满足任一条件的集合
        List<WorkOrderTriggerCondition> anyConditionList = preconditionList.stream().filter(x ->
            x.getConditionalGroup().equals(WorkOrderTriggerConditionalGroup.ANY_SATISFIED.getCode()))
            .collect(Collectors.toList());
        for (WorkOrderTriggerCondition anyCondition : anyConditionList) {
            if(workOrderRoleCmpUtil.comparePrecondition(oldWorkOrderObj, anyCondition)){//只要其中一个满足就返回true
                return true;
            }
        }
        //获取满足全部条件的集合
        List<WorkOrderTriggerCondition> allConditionList = preconditionList.stream().filter(x ->
            x.getConditionalGroup().equals(WorkOrderTriggerConditionalGroup.ALL_SATISFIED.getCode()))
            .collect(Collectors.toList());
        for (WorkOrderTriggerCondition allCondition : allConditionList) {
            if(!workOrderRoleCmpUtil.comparePrecondition(oldWorkOrderObj, allCondition)){//只要其中一个不满足就返回false
                return false;
            }
        }
        return true;
    }

    /**
     * 变更条件判断组件
     */
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS_BOOLEAN, nodeId = "fieldChangeJudge", nodeName = "前置判断条件组件", nodeType = NodeTypeEnum.BOOLEAN)
    public Boolean fieldChangeJudge(NodeComponent bindCmp) {
        log.info("fieldChangeJudge");
        WorkOrderTrigger workOrderTrigger = bindCmp.getContextBean(WorkOrderTrigger.class);
        WorkOrderCmpInParamObject workOrderCmpInParamObject = bindCmp.getContextBean(WorkOrderCmpInParamObject.class);
        WorkOrderStrategy oldWorkOrderObj = workOrderCmpInParamObject.getOldWorkOrderObj();
        WorkOrderStrategy newWorkOrderObj = workOrderCmpInParamObject.getNewWorkOrderObj();
        List<WorkOrderTriggerCondition> triggerConditions = workOrderTrigger.getTriggerConditions();
        //获取字段变更条件
        List<WorkOrderTriggerCondition> fieldChangeList =
            triggerConditions.stream().filter(x -> x.getJudgmentType().equals(
                    WorkOrderTriggerConditionJudgmentType.FIELD_CHANGE_CONDITION.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(fieldChangeList)){
            log.info("fieldChangeJudge is empty");
            return true;
        }
        Boolean resultAny = false;
        //获取满足任一条件的集合
        List<WorkOrderTriggerCondition> anyConditionList = fieldChangeList.stream().filter(x ->
                x.getConditionalGroup().equals(WorkOrderTriggerConditionalGroup.ANY_SATISFIED.getCode()))
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(anyConditionList)){
            resultAny = true;
        }else{
            for (WorkOrderTriggerCondition anyCondition : anyConditionList) {
                if(workOrderRoleCmpUtil.compareFieldChangeCondition(oldWorkOrderObj,newWorkOrderObj, anyCondition)){//只要其中一个满足就返回true
                    resultAny =  true;
                    break;
                }
            }
        }
        Boolean resultAll = true;
        //获取满足全部条件的集合
        List<WorkOrderTriggerCondition> allConditionList = fieldChangeList.stream().filter(x ->
                x.getConditionalGroup().equals(WorkOrderTriggerConditionalGroup.ALL_SATISFIED.getCode()))
            .collect(Collectors.toList());
        for (WorkOrderTriggerCondition allCondition : allConditionList) {
            if (!workOrderRoleCmpUtil.compareFieldChangeCondition(oldWorkOrderObj, newWorkOrderObj,
                allCondition)) {//只要其中一个不满足就返回false
                resultAll = false;
            }
        }
        log.info("resultAny:{},resultAll:{}",resultAny,resultAll);
        return resultAny&resultAll;
    }

    /**
     * 执行动作迭代器组件
     */
    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "activeIterateor", nodeName = "执行动作迭代器组件", nodeType = NodeTypeEnum.COMMON)
    public void activeIterateor(NodeComponent bindCmp) {
        log.info("activeIterateor");
        WorkOrderTrigger workOrderTrigger = bindCmp.getContextBean(WorkOrderTrigger.class);
        WorkOrderCmpInParamObject contextBean = bindCmp.getContextBean(WorkOrderCmpInParamObject.class);
        WorkOrderStrategy oldWorkOrderObj = contextBean.getOldWorkOrderObj();
        for (WorkOrderTriggerActive triggerActive : workOrderTrigger.getTriggerActives()) {
            if (triggerActive.getActiveType().equals(WorkOrderTriggerActiveTypeEnums.SEND_RECEIVER_SMS.getCode())) {
                this.sendSmsToAssignee(workOrderTrigger, oldWorkOrderObj, triggerActive);
            }else if (triggerActive.getActiveType().equals(WorkOrderTriggerActiveTypeEnums.SEND_RECEIVER_WX.getCode())) {
                log.info("sendMpMsgToAssignee");
                this.sendMpMsgToAssignee(workOrderTrigger, oldWorkOrderObj, triggerActive);
            }else if (triggerActive.getActiveType().equals(WorkOrderTriggerActiveTypeEnums.UP_RECEIVER.getCode())) {
                log.info("changeAssignee");
                this.changeAcceptor(workOrderTrigger, oldWorkOrderObj, triggerActive);
            }else if (triggerActive.getActiveType().equals(WorkOrderTriggerActiveTypeEnums.ADD_FOLLOWER.getCode())) {
                log.info("addFollower");
                this.addFollower(workOrderTrigger, oldWorkOrderObj);
            }
        }
    }

    /**
     * 通知受理方短信（已取消）
     */
    public void sendSmsToAssignee(WorkOrderTrigger workOrderTrigger ,  WorkOrderStrategy workOrderStrategy,
        WorkOrderTriggerActive triggerActive) {
        log.info("sendSmsToAssignee");
    }

    /**
     * 通知受理方公众号消息
     */
    public void sendMpMsgToAssignee(WorkOrderTrigger workOrderTrigger ,  WorkOrderStrategy workOrderStrategy,
        WorkOrderTriggerActive triggerActive) {
        WorkOrder workOrder = workOrderService.getById(workOrderStrategy.getWorkOrderId());
        String wxwptmpId = "";
        if(triggerActive.getWxMpTmpType()==1){
             wxwptmpId = wxMpTmpProperties.getComplaintTemplateId();
        }else if(triggerActive.getWxMpTmpType()==2){
             wxwptmpId = wxMpTmpProperties.getRefundTemplateId();
        }
        for (JudgmentValue judgmentValue : triggerActive.getTargetIdList()) {
            List<Long> empIdList = new ArrayList<>();//销售代理ID集合
            String shopName = workOrder.getSiteName();//受理人、站点主管
            if (judgmentValue.getValue().equals(WorkOrderTriggerActiveSendUserTypeEnums.ACCEPTOR.getCode().toString())) {
                log.info("通知受理人");
                List<WorkOrderOperation> acceptingOperationByWorkOrderId = workOrderOperationService.getAcceptingOperationByWorkOrderId(
                    workOrderStrategy.getWorkOrderId());
                if (CollectionUtil.isEmpty(acceptingOperationByWorkOrderId)) continue;
                empIdList.addAll(acceptingOperationByWorkOrderId.stream()
                    .map(WorkOrderOperation::getPriorityerId).collect(Collectors.toList()));
//                shopName = workOrder.getSiteName();
            } else if (judgmentValue.getValue().equals(WorkOrderTriggerActiveSendUserTypeEnums.SITE_MANAGER.getCode().toString())) {
                log.info("通知站点主管");
                CxrEmployeePost cxrEmployeePost =
                    cxrEmployeePostMapper.selectOne(new LambdaQueryWrapper<CxrEmployeePost>()
                    .eq(CxrEmployeePost::getCxrSiteId, workOrder.getSiteId())
                    .eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
                    .eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
                    .apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})", PostType.DIRECTOR.getValue()));
                if (cxrEmployeePost == null) continue;
                    empIdList.add(cxrEmployeePost.getCxrEmployeeId());
//                shopName=workOrder.getSiteName();
            } else if (judgmentValue.getValue().equals(WorkOrderTriggerActiveSendUserTypeEnums.REGION_MANAGER.getCode().toString())) {
                log.info("通知区域经理");
                List<SysUserPost> cxrEmployeePost = sysUserPostMapper.selectList(new LambdaQueryWrapper<SysUserPost>()
                    .eq(SysUserPost::getDeptId, workOrder.getRegionId())
                    .in(SysUserPost::getPostId, PostType.CELLULAR_MANAGER.getValue(),PostType.REGIONAL_MANAGER.getValue())
                    .eq(SysUserPost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
                if  (CollectionUtil.isEmpty(cxrEmployeePost)) continue;
                empIdList.addAll(cxrEmployeePost.stream().map(SysUserPost::getUserId).collect(Collectors.toList()));
//                shopName = workOrder.getRegionName();
            } else if (judgmentValue.getValue().equals(WorkOrderTriggerActiveSendUserTypeEnums.REGION_DIRECTOR.getCode().toString())) {
                log.info("通知大区总监");
                SysDept sysDept = sysDeptMapper.selectById(workOrder.getRegionId());
                if (sysDept == null) continue;
                List<SysUserPost> sysUserPostList = sysUserPostMapper.selectList(new LambdaQueryWrapper<SysUserPost>()
                    .eq(SysUserPost::getDeptId, sysDept.getParentId())
                    .eq(SysUserPost::getPostId, PostType.MAJORDOMO.getValue())
                    .eq(SysUserPost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
                if (CollectionUtil.isEmpty(sysUserPostList)) continue;
                empIdList.addAll(sysUserPostList.stream().map(SysUserPost::getUserId).collect(Collectors.toList()));
//                shopName = workOrder.getRegionName();
            }
            if (CollectionUtil.isEmpty(empIdList)) continue;
            //查找其OpenId
            List<CxrEmployee> cxrEmployees = cxrEmployeeMapper.selectList(new LambdaQueryWrapper<CxrEmployee>()
                .in(CxrEmployee::getId, empIdList)
                .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue()));
            if (CollectionUtil.isEmpty(cxrEmployees)) continue;
            List<String> openIdList = cxrEmployees.stream().map(CxrEmployee::getWxOpenid).distinct().collect(Collectors.toList());
            List<WorkOrderTrigSendWxMsgBean> workOrderTrigSendMsgBeanList = new ArrayList<WorkOrderTrigSendWxMsgBean>();
            for (String openId : openIdList) {
                WorkOrderTrigSendWxMsgBean workOrderTrigSendMsgBean = new WorkOrderTrigSendWxMsgBean();
                workOrderTrigSendMsgBean.setWxOpenid(openId);
                workOrderTrigSendMsgBean.setWorkOrderType(WorkOrderClassificationEnums.getByCode(workOrder.getType()).getDescription());
                workOrderTrigSendMsgBean.setShop(shopName);
                workOrderTrigSendMsgBean.setContents(workOrder.getDescribetion());
                workOrderTrigSendMsgBean.setTenantId(wxwptmpId);
                workOrderTrigSendMsgBean.setWorkOrderId(workOrder.getId());
                workOrderTrigSendMsgBeanList.add(workOrderTrigSendMsgBean);
            }
            for (WorkOrderTrigSendWxMsgBean workOrderTrigSendWxMsgBean : workOrderTrigSendMsgBeanList) {
                mQUtil.sendSyncMessage(MqBizConst.SENDCUSTOMERSUPPORTCONST_TOPIC, JSONUtil.toJsonStr(workOrderTrigSendWxMsgBean));
            }
        }
    }


    /**
     * 更改工单受理人组件
     */
//    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "changeAcceptor", nodeName = "更改工单受理人组件", nodeType = NodeTypeEnum.COMMON)
    public void changeAcceptor(WorkOrderTrigger workOrderTrigger ,  WorkOrderStrategy workOrderStrategy,
        WorkOrderTriggerActive triggerActive) {
        //todo:changeAcceptor
//        log.info("changeAcceptor");
//        String methodName = bindCmp.getRequestData();
//        WorkOrderCmpInParamObject contextBean = bindCmp.getContextBean(WorkOrderCmpInParamObject.class);
//        log.info("changeAcceptor contextBean:{}", contextBean);

    }

    /**
     * 添加关注人组件
     */
//    @LiteflowMethod(value = LiteFlowMethodEnum.PROCESS, nodeId = "addFollower", nodeName = "添加关注人组件", nodeType = NodeTypeEnum.COMMON)
    public void addFollower(WorkOrderTrigger workOrderTrigger ,  WorkOrderStrategy workOrderStrategy) {
        log.info("addFollower");
        Long workOrderId = workOrderStrategy.getWorkOrderId();//工单ID

        Long triggerId = workOrderTrigger.getId();
        List<WorkOrderTriggerActive> triggerActives = workOrderTriggerActiveService.getBaseMapper()
                .selectList(new LambdaQueryWrapper<WorkOrderTriggerActive>()
                        .eq(WorkOrderTriggerActive::getTriggerId, triggerId)
                        .eq(WorkOrderTriggerActive::getActiveType, WorkOrderTriggerActiveTypeEnums.ADD_FOLLOWER.getCode())
                        .eq(WorkOrderTriggerActive::getDeleteStatus, DeleteStatus.not_deleted)
                );

        if (CollUtil.isNotEmpty(triggerActives)){
            List<WorkOrderFollower> triggerFollowers =
                workflowFollowerService.getBaseMapper().selectList(new LambdaQueryWrapper<WorkOrderFollower>()
                    .eq(WorkOrderFollower::getWorkOrderId, workOrderId)
                    .eq(WorkOrderFollower::getDeleteStatus, DeleteStatus.not_deleted)
            );
            Map<Long, Long> followerMap =null;
            if (CollUtil.isNotEmpty(triggerActives)){
                followerMap = triggerFollowers.stream().collect(Collectors.toMap(x -> x.getFollowerUserId(), x -> x.getWorkOrderId(),
                        (v1, v2) -> v1));
            }

            LoginUser loginUser = LoginHelper.getLoginUser();
            List<WorkOrderFollower> triggeredFollowerList = new ArrayList<>();
            for (WorkOrderTriggerActive triggerActive : triggerActives) {
                List<JudgmentValue> targetIdList = triggerActive.getTargetIdList();
                for (JudgmentValue judgmentValue : targetIdList) {
                    if (CollUtil.isNotEmpty(followerMap)&& ObjectUtil.isNotEmpty(followerMap.get(Long.valueOf(judgmentValue.getValue())))){
                        continue;
                    }

                    if (ObjectUtil.isNotEmpty(judgmentValue.getValue())){
                        WorkOrderFollower trailFollower = new WorkOrderFollower();
                        Long value = Long.valueOf(judgmentValue.getValue());
                        trailFollower.setFollowerUserId(value);
                        trailFollower.setWorkOrderId(workOrderId);
                        UserInfoServiceVo infoServiceVo=  sysDeptMapper.selectSysUser(value);
                        trailFollower.setFollowerName(infoServiceVo.getNickName());
                        trailFollower.setDeleteStatus(DeleteStatus.not_deleted);
                        trailFollower.setCreateByName(loginUser.getNickName());
                        trailFollower.setCreateBy(loginUser.getUserId());
                        trailFollower.setCreateTime(new Date());
                        triggeredFollowerList.add(trailFollower);
                    }
                }
            }
            if (CollUtil.isNotEmpty(triggeredFollowerList)){
                workflowFollowerService.saveBatch(triggeredFollowerList);
            }
        }
    }
}
