package com.cxrry.biz.customer.support.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderEditDetailVo {
  private Long id;
  /**
   * 工单分类 1.投诉工单 2.退款工单
   */
  private Integer type;

  /**
   * 受理状态 0待受理 1受理中 2已完结 3.已逾期
   */
  private Integer processStatus;

  private Long orderMenuId;

  /**
   * 投诉类型多级名称拼接字符串
   */
  private String orderMenuRemark;

  /**
   * 优先级 1低 2一般 3紧急 4非常紧急
   */
  private Integer priority;

  /**
   * 最新待受理人用户名称，多个逗号分开
   */
  private String acceptorStr;

  /**
   * 最新待受理人用户编号，多个逗号分开
   */
  private String acceptorNumber;

  /**
   * 工单描述内容
   */
  private String describetion;

  /**
   * 工单附件相对路径url，多个用逗号隔开
   */
  @TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
  private List<FileUrlConfig> fileUrl;

  /**
   * 客户ID
   */
  private Long customerId;

  /**
   * 客户主地址手机号
   */
  private String customerMainPhone;

  /**
   * 客户登录手机号
   */
  private String customerLoginPhone;

  /**
   * 客户名称
   */
  private String customerName;

  /**
   * 客户地址
   */
  private String customerAddress;

  /**
   * 受理站点ID
   */
  private Long siteId;

  /**
   * 受理站点名称
   */
  private String siteName;
  /**
   * 受理站点代号
   */
  private String siteMark;

  /**
   * 受理区域ID
   */
  private Long regionId;

  /**
   * 受理区域名称
   */
  private String regionName;

  /**
   * 受理大区ID
   */
  private Long rootRegionId;

  /**
   * 受理大区名称
   */
  private String rootRegionName;

  /**
   * 站点主管用户ID
   */
  private Long siteDirectorUserId;

  /**
   * 站点主管名称
   */
  private String siteDirectorUserName;


  /**
   * 销售代理id
   */
  private Long employeeId;

  /**
   * 销售代理名称
   */
  private String employeeName;
  /**
   * 销售代理编号
   */
  private String jobNumber;

  @ApiModelProperty(value = "公司id")
  private Long currentDeptId;

  @ApiModelProperty(value = "1.自动分配 2.指定人员")
  private Integer acceptedFlag;

  @ApiModelProperty("关注人")
  private List<WorkOrderFollower>workOrderFollowerList;

  private String complaintObjectName;

  @TableField(typeHandler = JsonTypeHandlerConstant.OperatorMessageHandler.class)
  private List<OperatorMessageConfig> operatorMessage;

  @ApiModelProperty(value = "客户地址id")
  private Long customerAddressId;

}


