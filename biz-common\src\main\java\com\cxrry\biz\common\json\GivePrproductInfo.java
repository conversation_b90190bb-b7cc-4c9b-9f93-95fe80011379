package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

@EqualsAndHashCode
@Data
public class GivePrproductInfo implements Serializable {

    /**
     * 赠品id
     */
    @ApiModelProperty("赠品id")
    private String givePrproductId;

    /**
     * 图片
     */
    @ApiModelProperty("图片")
    private String pictureUrl;

    @ApiModelProperty("赠送商品的名称")
    private String giveProductName;
    /**
     * 商品编号
     */
    @ApiModelProperty("商品编号")
    private String productCode;

    /**
     * 售卖商品id
     */
    @ApiModelProperty("售卖商品id")
    private Long cxrSaleProductId;

    @ApiModelProperty(value = "规格的标识", required = true)
    private Long specId;

    @ApiModelProperty(value = "规格1", required = true)
    private String specName1;

    @ApiModelProperty("规格值1")
    private String specValue1;

    @ApiModelProperty("规格2")
    private String specName2;

    @ApiModelProperty("规格值2")
    private String specValue2;

    @ApiModelProperty("售价")
    private BigDecimal price;

    @ApiModelProperty("成本价")
    private BigDecimal costPrice;

    @ApiModelProperty("划线价")
    private BigDecimal lineationPrice;

    @ApiModelProperty("商品数量")
    private Integer goodsSum;

    @ApiModelProperty("鲜奶兑换数量")
    private Integer milkConversionSum;


    @ApiModelProperty("数量")
    private Integer quantity;
    /**
     * 线上商品编码
     */
    @ApiModelProperty("线上商品编码")
    private String skuId;

    @ApiModelProperty("冗余字段产品名称")
    private String name;

    @ApiModelProperty("客户端展示的名称")
    private String displayName;
}
