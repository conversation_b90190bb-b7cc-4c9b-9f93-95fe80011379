package com.cxrry.biz.customer.support.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkOrderComplaintTotalVo {
  @ApiModelProperty(value = "排序")
  @ExcelProperty(value = "排序", index = 0)
  private Integer RowNum;
  @ApiModelProperty(value = "公司")
  @ExcelProperty(value = "公司", index = 1)
  private String complaintObjectDeptName;
  @ApiModelProperty(value = "大区")
  @ExcelProperty(value = "大区", index = 2)
  private String complaintObjectRootRegionName;
  @ApiModelProperty(value = "区域")
  @ExcelProperty(value = "区域", index = 3)
  private String complaintObjectRegionName;
  @ApiModelProperty(value = "省")
  @ExcelProperty(value = "省", index = 4)
  private String complaintObjectProvince;
  @ApiModelProperty(value = "市")
  @ExcelProperty(value = "市", index = 5)
  private String complaintObjectCity;
  @ApiModelProperty(value = "区")
  @ExcelProperty(value = "区", index = 6)
  private String complaintObjectArea;
  @ApiModelProperty(value = "站点")
  @ExcelProperty(value = "站点", index = 7)
  private String complaintObjectSiteName;
  @ApiModelProperty(value = "站点编号")
  @ExcelProperty(value = "站点编号", index = 8)
  private String complaintObjectSiteMark;
  @ApiModelProperty(value = "投诉对象")
  @ExcelProperty(value = "投诉对象", index = 9)
  private String complaintObjectName;
  @ApiModelProperty(value = "投诉对象编号")
  @ExcelProperty(value = "投诉对象编号", index = 10)
  private String complaintObjectNumber;
  @ApiModelProperty(value = "分类")
  @ExcelProperty(value = "分类", index = 11)
  private String orderMenuRemark;
  @ApiModelProperty(value = "数量")
  @ExcelProperty(value = "数量", index = 12)
  private String totalCount;

}
