package com.cxrry.biz.customer.support.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderCxrOperationDto {
  @ApiModelProperty(value = "操作人名称")
  private String operatorName;

  @ApiModelProperty(value = "操作类型：1完结 2回复 3转交 4回退 5逾期 6重启")
  private Integer operateType;

  @ApiModelProperty(value = "回复内容")
  private String relyNotes;

  @TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
  private List<FileUrlConfig> fileUrl;

  @ApiModelProperty(value = "转交后受理人名称")
  private String nextOperator;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
  @ApiModelProperty(value = "受理时间")
  private Date operateTime;

  @ApiModelProperty(value = "节点")
  private Integer nodeLevel;

  /**
   * 操作人在当前站点的最高职位名称（冗余）
   */
  private String operatorMaxJobName;

  /**
   * 操作人账号类型：1销售代理 2总部用户
   */
  private Integer operateAccount;

  /**
   * 流程类型 1真实节点 2虚拟节点
   */
  private Integer processType;



}
