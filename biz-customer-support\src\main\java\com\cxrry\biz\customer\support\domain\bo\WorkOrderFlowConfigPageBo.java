package com.cxrry.biz.customer.support.domain.bo;

import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class WorkOrderFlowConfigPageBo extends PageQuery implements Serializable {
  @ApiModelProperty("流程名称")
  private String processName;

  @ApiModelProperty("0.停用 1.启用")
  private Integer status;

}
