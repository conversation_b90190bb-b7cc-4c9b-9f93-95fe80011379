package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.logger.api.annotation.LogModel;
import com.cxrry.logger.api.annotation.LogTag;
import com.ruoyi.business.base.api.domain.bo.CxrPostId;
import com.ruoyi.business.base.api.typeHandler.TypeHandlerConstant;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 员工、职位关联对象 cxr_employee_post
 *
 * <AUTHOR>
 * @date 2022-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@LogModel(value = "员工、职位关联对象",tableName = "cxr_employee_post")
@TableName(value = "cxr_employee_post", autoResultMap = true)
public class CxrEmployeePost extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 员工id(员工id)
     */
    @LogTag(alias = "员工id")
    private Long cxrEmployeeId;
    /**
     * 职位id(职位type  数据字典中)
     */
    @LogTag(alias = "职位id")
    @TableField(value = "cxr_post_id", typeHandler = TypeHandlerConstant.EmployeePostTypeHandler.class)
    private CxrPostId cxrPostId;
    /**
     * 站点id(站点id)
     */
    @LogTag(alias = "站点id")
    private Long cxrSiteId;
    /**
     * 区域id
     */
    @LogTag(alias = "区域id")
    private Long cxrRegionId;
    /**
     * 分管开始时间
     */
    @LogTag(alias = "分管开始时间")
    private Date inChargeStartTime;
    /**
     * 分管结束时间
     */
    @LogTag(alias = "分管结束时间")
    private Date inChargeEndTime;

    /**
     * 分管状态(详情见字典)
     */
    @LogTag(alias = "分管状态")
    private String chargeStatus;


    /**
     * 查询业绩开始时间
     */
    @TableField(exist = false)
    private LocalDate fist;

    /**
     * 查询业绩结束时间（不包含）
     */
    @TableField(exist = false)
    private LocalDate last;

    /**
     * 站点id(站点id)
     */
    @TableField(exist = false)
    private String cxrSiteName;

    @TableField(exist = false)
    private String cxrEmployeeName;

    @TableField(exist = false)
    private String cxrEmployeeNumber;

}
