package com.cxrry.biz.customer.support.domain.dto;

import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class WorkOrderCxrAcceptorDto {
  @ApiModelProperty(value = "节点")
  private Integer nodeLevel;

  /**
   * 受理节点名称
   */
  private String acceptedNodeName;

  /**
   * 受理人名称
   */
  private String priorityerName;

  /**
   * 受理状态：0待受理 1受理中 2已完结 3.已逾期
   *
   */
  private Integer priorityStatus;

  @ApiModelProperty(value = "1.销售代理 2.总部用户")
  private Integer acceptedAccount;



}
