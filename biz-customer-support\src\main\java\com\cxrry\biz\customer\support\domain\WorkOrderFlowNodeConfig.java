package com.cxrry.biz.customer.support.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.customer.support.domain.json.WorkOrderAcceptorConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderButtonConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderDurationConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工单流程节点配置表
 * @TableName work_order_flow_node_config
 */
@TableName(value = "work_order_flow_node_config",autoResultMap = true)
@Data
public class WorkOrderFlowNodeConfig implements Serializable {

  /**
   * id
   */
  @NotNull(message = "[id]不能为空")
  @ApiModelProperty("id")
  private Long id;
  /**
   * 流程id
   */
  @ApiModelProperty("流程id")
  private Long flowConfigId;
  /**
   * 节点等级
   */
  @ApiModelProperty("节点等级")
  private Integer nodeGrade;
  /**
   * 时长的配置
   */
  @ApiModelProperty("时长的配置")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderDurationConfigTypeHandler.class)
  private List<WorkOrderDurationConfig> durationConfig;
  /**
   * 受理人配置(受理人指 定范围：1.指定人 2岗位 ，受 理人ID类型：1用户2职位3角色 受理人类型ID，受理人类型ID对 应的名称)
   */
  @ApiModelProperty("受理人配置(受理人指 定范围：1.指定人 2岗位 ，受 理人ID类型：1用户2职位3角色 受理人类型ID，受理人类型ID对 应的名称)")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderAcceptorConfigHandler.class)
  private List<WorkOrderAcceptorConfig> acceptorConfig;
  /**
   * 操作按钮（1.回复 2转 交 3完结 开启状态 操作权限字 符逗号隔开（例1,2,3）1.当前 节点受理人2下一节点受理人 3. 总部用户 99.不限制 权限类型 名称字符，逗号隔开（例：当前 节点、下一节点））
   * 操作按钮配置集合（1.回复 2转 交 3完结 开启状态 操作权限字 符逗号隔开（例1,2,3）1.当前 节点受理人2下一节点受理人 3. 总部用户 99.不限制 权限类型 名称字符，逗号隔开（例：当前 节点、下一节点））
   */
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderButtonConfigHandler.class)
  private List<WorkOrderButtonConfig> buttonConfig;


  /**
   * 创建人id
   */
  private Long createBy;

  /**
   * 创建人名称
   */
  private String createByName;

  /**
   * 创建人类型(详情见字典)
   */
  private String createByType;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 更新人id
   */
  private Long updateBy;

  /**
   * 更新人名称
   */
  private String updateByName;

  /**
   * 更新人类型(详情见字典)
   */
  private String updateByType;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 删除人id
   */
  private Long deleteBy;

  /**
   * 删除人名称
   */
  private String deleteByName;

  /**
   * 删除人类型(详情见字典)
   */
  private String deleteByType;

  /**
   * 删除时间
   */
  private Date deleteTime;

  /**
   * 删除状态(详情见字典)
   */
  private String deleteStatus;

}
