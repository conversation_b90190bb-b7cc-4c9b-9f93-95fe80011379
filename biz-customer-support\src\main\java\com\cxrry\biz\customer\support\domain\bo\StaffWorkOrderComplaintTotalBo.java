package com.cxrry.biz.customer.support.domain.bo;

import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class StaffWorkOrderComplaintTotalBo extends PageQuery {
  @ApiModelProperty(value = "1.销售代理 2.站点 3.区域")
  private Integer flag;
  @ApiModelProperty(value = "站点名称")
  private String complaintObjectSiteName;
  @ApiModelProperty(value = "投诉对象区域名称")
  private String complaintObjectRegionName;
  @ApiModelProperty(value = "投诉对象名称")
  private String complaintObjectName;
  @ApiModelProperty(value = "分类id")
  private List<Long> orderMenuIds;
  @ApiModelProperty(value = "开始时间")
  private LocalDate createStartTime;
  @ApiModelProperty(value = "结束时间")
  private LocalDate createEndTime;
  private List<Long> ids;
  private Integer postFlag; //1.董事长的权限

}
