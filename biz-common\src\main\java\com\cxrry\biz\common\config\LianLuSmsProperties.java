package com.cxrry.biz.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Data
@ConfigurationProperties(prefix = "lianlusms")
@RefreshScope
public class LianLuSmsProperties {
    private String mchId;
    private String appId;
    private String appKey;
    private String sendUrl;
    private String signName;
    private String signType;
    private String version;
    private String type;
}
