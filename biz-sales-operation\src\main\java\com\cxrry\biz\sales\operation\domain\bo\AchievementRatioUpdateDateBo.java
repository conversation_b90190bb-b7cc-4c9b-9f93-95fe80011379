package com.cxrry.biz.sales.operation.domain.bo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
@Data
public class AchievementRatioUpdateDateBo implements Serializable {

    private Long id;
    /**
     * L1晋升L2考核周期开始时间
     */
    private LocalDate oneStageStartTime;

    /**
     * L1晋升L2考核周期结束时间
     */
    private LocalDate oneStageEndTime;

    /**
     * L2考核开始日期
     */
    private LocalDate twoStageStartTime;

    /**
     * L2考核30天到期日期
     */
    private LocalDate twoStageEndTime;

    /**
     * 过小承包30天后开始时间
     */
    private LocalDate passAfterStartTime;

    /**
     * 过小承包30天后结束时间
     */
    private LocalDate passAfterEndTime;

    /**
     * 没有通过小承包30天后开始时间
     */
    private LocalDate notPassAfterStartTime;

    /**
     * 没有通过小承包30天后结束时间
     */
    private LocalDate notPassAfterEndTime;
}
