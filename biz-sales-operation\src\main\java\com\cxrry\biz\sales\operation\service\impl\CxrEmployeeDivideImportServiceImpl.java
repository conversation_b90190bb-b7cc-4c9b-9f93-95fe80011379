package com.cxrry.biz.sales.operation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cxrry.biz.common.mq.MqBizConst;
import com.cxrry.biz.common.utils.MqUtil;
import com.cxrry.biz.sales.operation.domain.excel.*;
import com.cxrry.biz.sales.operation.domain.vo.MarketingExpenseImport;
import com.cxrry.biz.sales.operation.entity.CxrEmployeeDivide;
import com.cxrry.biz.sales.operation.entity.CxrEmployeeDivideResult;
import com.cxrry.biz.sales.operation.enums.DivideEnums;
import com.cxrry.biz.sales.operation.enums.emp.OccupationStatus;
import com.cxrry.biz.sales.operation.manager.DivideComputeManager;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeDivideMapper;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeDivideResultMapper;
import com.cxrry.biz.sales.operation.service.CxrEmployeeDivideImportService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeDivideResultService;
import com.ruoyi.common.core.domain.LoginInfo;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.excel.utils.ExcelUtil;
import com.ruoyi.common.satoken.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@RefreshScope
@Slf4j
@Service
public class CxrEmployeeDivideImportServiceImpl implements CxrEmployeeDivideImportService {

    @Autowired
    private CxrEmployeeDivideResultService cxrEmployeeDivideResultService;
    @Autowired
    private CxrEmployeeDivideMapper employeeDivideMapper;

    @Autowired
    private DivideComputeManager divideComputeManager;
    @Autowired
    private MqUtil mqUtil;

    @Override
    public String importNegativeExpense(String month, MultipartFile file)  throws IOException{
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "上月负分成","上月负分成月份");
        checkActualHeaders(file,EXPECTED_HEADERS);
        List<LastNegativeExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                LastNegativeExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(LastNegativeExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));
        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .in(CxrEmployeeDivideResult::getDivideSn,
                        DivideEnums.DivideSn.EMPLOYEE_LAST_MONTH_NEGATIVE_ACTUAL_DIVIDE.getCode(),
                        DivideEnums.DivideSn.UP_ACTUAL_DIVIDE_MONTH.getCode()
                )
        );

        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));



        Set<CxrEmployeeDivideResult> divideResultsEdit=new HashSet<>();
        Set<CxrEmployeeDivideResult> divideResultsAdd=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        List<CxrEmployeeDivide> edits=new ArrayList<>();
        for (LastNegativeExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }

            if(ObjUtil.isEmpty(moneyListVo.getLastMonth())){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+"上月负分成月份不能为空 \\n");
                failureNum++;
                continue;
            }

            CxrEmployeeDivide employeeDivide = divides.get(0);
            if (checkImportMistakeNotResult(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,null,
                    month,moneyListVo.getBsMonth()
                    )){
                failureNum++;
                continue;
            }

            employeeDivide.setEmployeeLastMonthNegativeActualDivide(moneyListVo.getLastMonthNegativeActualDivide());
            employeeDivide.setUpActualDivideMonth(moneyListVo.getLastMonth().trim());
            employeeDivide.setUpActualDivideMonthImp(moneyListVo.getLastMonth().trim());
            employeeDivide.setUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateByName(loginUser.getUserName());
            edits.add(employeeDivide);
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivide.getId());
            if (CollUtil.isNotEmpty(divideResultList)){
                divideResultList.stream().map(x->{
                    if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.EMPLOYEE_LAST_MONTH_NEGATIVE_ACTUAL_DIVIDE.getCode())){
                        x.setResultValue(moneyListVo.getLastMonthNegativeActualDivide());
                    }

                    if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.UP_ACTUAL_DIVIDE_MONTH.getCode())){
                        x.setResultValueStr(moneyListVo.getLastMonth());
                    }

                    x.setUpdateTime(LocalDateTime.now());
                    x.setUpdateByName(loginUser.getNickName());
                    x.setUpdateBy(loginUser.getUserId());
                    divideResultsEdit.add(x);
                    return x;
                }).collect(Collectors.toList());
            }else {
                divideResultsAdd.add(addCxrEmployeeDivideResult(loginUser,employeeDivide.getId(),moneyListVo.getLastMonthNegativeActualDivide(),
                        DivideEnums.DivideSn.EMPLOYEE_LAST_MONTH_NEGATIVE_ACTUAL_DIVIDE.getCode(),
                        DivideEnums.DivideSn.EMPLOYEE_LAST_MONTH_NEGATIVE_ACTUAL_DIVIDE.getDesc(),employeeDivide.getOccupationStatus()));
            }
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        if (CollUtil.isNotEmpty(divideResultsEdit)){
            updateEmployeeDivide(divideResultsEdit,employeeDivides.get(0).getEndDate(),employeeIds);
        }

        if (CollUtil.isNotEmpty(divideResultsAdd)){
            cxrEmployeeDivideResultService.saveBatch(divideResultsAdd);
        }
        if (CollUtil.isNotEmpty(edits)){
            employeeDivideMapper.updateEmployeeDivideNegative(edits);
            LocalDate endDate = edits.get(0).getEndDate();
            if (CollUtil.isNotEmpty(employeeIds)){
                for (Long employeeId : employeeIds) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("employeeId",employeeId);
                    jsonObject.put("date", LocalDateTimeUtil.formatNormal(endDate));
                    mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, JSONUtil.toJsonStr(jsonObject));
                }
            }
        }

        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    @Override
    public String importAccommodationExpense(String month, MultipartFile file) throws IOException{
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "吃(元)","住(元)","空调(元)");
        checkActualHeaders(file,EXPECTED_HEADERS);

        List<ElaFeeExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                ElaFeeExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(ElaFeeExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .in(CxrEmployeeDivideResult::getDivideSn,
                        DivideEnums.DivideSn.ELA_FEE.getCode()
                )
        );


        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResults=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        List<CxrEmployeeDivide> edits=new ArrayList<>();
        for (ElaFeeExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());

            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);
            Long employeeDivideId = employeeDivide.getId();
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivideId);
            if (checkImportMistake(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList
            ,month,moneyListVo.getBsMonth()
            )){
                failureNum++;
                continue;
            }

            employeeDivide.setAcCost(moneyListVo.getConditioner());
            employeeDivide.setLiveCost(moneyListVo.getReside());
            employeeDivide.setEatCost(moneyListVo.getEat());
            employeeDivide.setUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateByName(loginUser.getUserName());
            edits.add(employeeDivide);
            divideResultList.stream().map(x->{
                if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.ELA_FEE.getCode())){
                    x.setResultValue(moneyListVo.getConditioner().add(moneyListVo.getEat()).add(moneyListVo.getReside()));
                }
                x.setUpdateTime(LocalDateTime.now());
                x.setUpdateByName(loginUser.getNickName());
                x.setUpdateBy(loginUser.getUserId());
                divideResults.add(x);
                return x;
            }).collect(Collectors.toList());
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        updateEmployeeDivide(divideResults,employeeDivides.get(0).getEndDate(),employeeIds);
        if (CollUtil.isNotEmpty(edits)){
            employeeDivideMapper.updateEmployeeDivideElaFee(edits);
        }

        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();

    }

    @Override
    public String importPlatformExpense(String month, MultipartFile file) throws IOException{
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "平台维护费");
        checkActualHeaders(file,EXPECTED_HEADERS);
        List<PlatformExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                PlatformExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(PlatformExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .in(CxrEmployeeDivideResult::getDivideSn,
                        DivideEnums.DivideSn.PLATFORM_MAINTAIN_FEE.getCode()
                )
        );


        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResults=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        for (PlatformExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);
            Long employeeDivideId = employeeDivide.getId();
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivideId);
            if (checkImportMistake(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList
                    ,month,moneyListVo.getBsMonth())){
                failureNum++;
                continue;
            }

            divideResultList.stream().map(x->{
                if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.PLATFORM_MAINTAIN_FEE.getCode())){
                    x.setResultValue(moneyListVo.getPlatformMaintainFee());
                }
                x.setUpdateTime(LocalDateTime.now());
                x.setUpdateByName(loginUser.getNickName());
                x.setUpdateBy(loginUser.getUserId());
                divideResults.add(x);
                return x;
            }).collect(Collectors.toList());
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        updateEmployeeDivide(divideResults,employeeDivides.get(0).getEndDate(),employeeIds);

        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    @Override
    public String importEarnestMoneyExpense(String month, MultipartFile file,Integer earnestMoneyType) throws IOException{
        /*List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "平台维护费");
        checkActualHeaders(file,EXPECTED_HEADERS);*/
        List<EarnestMoneyExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                EarnestMoneyExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(EarnestMoneyExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .eq(CxrEmployeeDivideResult::getDivideSn,
                        earnestMoneyType==2? DivideEnums.DivideSn.PAYABLE_EARNEST_MONEY.getCode():
                                DivideEnums.DivideSn.ALREADY_EARNEST_MONEY.getCode()
                )
        );


        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResultsEdit=new HashSet<>();
        Set<CxrEmployeeDivideResult> divideResultsAdd=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        List<CxrEmployeeDivide> edits=new ArrayList<>();
        for (EarnestMoneyExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);
            Long employeeDivideId = employeeDivide.getId();
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivideId);
            if (earnestMoneyType==2?
                    checkImportMistake(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList,month,moneyListVo.getBsMonth())
               :checkImportMistakeNotResult(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList,month,moneyListVo.getBsMonth())
            ){
                failureNum++;
                continue;
            }

            if (earnestMoneyType==2){
                employeeDivide.setPayableEarnestMoney(moneyListVo.getPayableEarnestMoney());
                divideResultList.stream().map(x->{
                    x.setResultValue(moneyListVo.getPayableEarnestMoney());
                    x.setUpdateTime(LocalDateTime.now());
                    x.setUpdateByName(loginUser.getNickName());
                    x.setUpdateBy(loginUser.getUserId());
                    x.setOccupationStatus(employeeDivide.getOccupationStatus());
                    divideResultsEdit.add(x);
                    return x;
                }).collect(Collectors.toList());
            }else {

                if (CollUtil.isNotEmpty(divideResultList)){
                    divideResultList.stream().map(x->{
                        x.setResultValue(moneyListVo.getPayableEarnestMoney());
                        x.setUpdateTime(LocalDateTime.now());
                        x.setUpdateByName(loginUser.getNickName());
                        x.setUpdateBy(loginUser.getUserId());
                        x.setOccupationStatus(employeeDivide.getOccupationStatus());
                        divideResultsEdit.add(x);
                        return x;
                    }).collect(Collectors.toList());
                }else {
                    divideResultsAdd.add(addCxrEmployeeDivideResult(loginUser,employeeDivide.getId(),moneyListVo.getPayableEarnestMoney(),
                            DivideEnums.DivideSn.ALREADY_EARNEST_MONEY.getCode(),DivideEnums.DivideSn.ALREADY_EARNEST_MONEY.getDesc(),employeeDivide.getOccupationStatus()));
                }
                employeeDivide.setAlreadyEarnestMoney(moneyListVo.getPayableEarnestMoney());
            }

            employeeDivide.setUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateByName(loginUser.getUserName());
            edits.add(employeeDivide);
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        if (earnestMoneyType==2){
            if (CollUtil.isNotEmpty(divideResultsEdit)){
                updateEmployeeDivide(divideResultsEdit,employeeDivides.get(0).getEndDate(),employeeIds);
                computeDivideEarnestMoney(divideResultsEdit);
            }
        }else {

            if (CollUtil.isNotEmpty(divideResultsEdit)){
                Map<Long, List<CxrEmployeeDivideResult>> updateDivideResult = divideResultsEdit.stream().collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));
                updateDivideResult.forEach((cxrEmployeeDivideId, cxrEmployeeDivideResults) -> {
                    int count = cxrEmployeeDivideResultService.updateBatchByImport(cxrEmployeeDivideResults, cxrEmployeeDivideId, 0);
                });
                computeDivideEarnestMoney(divideResultsEdit);
            }

            if (CollUtil.isNotEmpty(divideResultsAdd)){
                cxrEmployeeDivideResultService.saveBatch(divideResultsAdd);
                computeDivideEarnestMoney(divideResultsAdd);
            }

        }
        if (CollUtil.isNotEmpty(edits)){
            employeeDivideMapper.updateEmployeeDivideEarnestMoney(edits,earnestMoneyType);
        }
        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    @Override
    public String importRefundExpense(String month, MultipartFile file) throws IOException{
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "合作中退款提成");
        checkActualHeaders(file,EXPECTED_HEADERS);
        List<RefundExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                RefundExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(RefundExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .eq(CxrEmployeeDivideResult::getDivideSn,
                                DivideEnums.DivideSn.COOPERATE_REFUND_PUSH_MONEY.getCode()
                )
        );


        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResultsEdit=new HashSet<>();
        Set<CxrEmployeeDivideResult> divideResultsAdd=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        List<CxrEmployeeDivide> edits=new ArrayList<>();
        for (RefundExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);

            if (checkImportMistakeNotResult(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,null,month,moneyListVo.getBsMonth())){
                failureNum++;
                continue;
            }

            employeeDivide.setCooperateRefundPushMoney(moneyListVo.getCooperateRefundPushMoney());
            employeeDivide.setUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateTime(LocalDateTime.now());
            employeeDivide.setFinalUpdateByName(loginUser.getUserName());
            edits.add(employeeDivide);

            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivide.getId());
            if (CollUtil.isNotEmpty(divideResultList)){
                divideResultList.stream().map(x->{
                    if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.COOPERATE_REFUND_PUSH_MONEY.getCode())){
                        x.setResultValue(moneyListVo.getCooperateRefundPushMoney());
                    }
                    x.setUpdateTime(LocalDateTime.now());
                    x.setUpdateByName(loginUser.getNickName());
                    x.setUpdateBy(loginUser.getUserId());
                    divideResultsEdit.add(x);
                    return x;
                }).collect(Collectors.toList());
            }else {
                divideResultsAdd.add(addCxrEmployeeDivideResult(loginUser,employeeDivide.getId(),moneyListVo.getCooperateRefundPushMoney(),
                        DivideEnums.DivideSn.COOPERATE_REFUND_PUSH_MONEY.getCode(),DivideEnums.DivideSn.COOPERATE_REFUND_PUSH_MONEY.getDesc(),employeeDivide.getOccupationStatus()));
            }

            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }

        if (CollUtil.isNotEmpty(divideResultsEdit)){
            updateEmployeeDivide(divideResultsEdit,employeeDivides.get(0).getEndDate(),employeeIds);
        }

        if (CollUtil.isNotEmpty(divideResultsAdd)){
            cxrEmployeeDivideResultService.saveBatch(divideResultsAdd);
        }

        if (CollUtil.isNotEmpty(edits)){
            employeeDivideMapper.updateEmployeeDivideRefundPushMoney(edits);
            LocalDate endDate = edits.get(0).getEndDate();
            if (CollUtil.isNotEmpty(employeeIds)){
                for (Long employeeId : employeeIds) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("employeeId",employeeId);
                    jsonObject.put("date", LocalDateTimeUtil.formatNormal(endDate));
                    mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, JSONUtil.toJsonStr(jsonObject));
                }
            }
        }

        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    @Override
    public String importSubsidyExpense(String month, MultipartFile file) throws IOException{
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "杂费补贴");
        checkActualHeaders(file,EXPECTED_HEADERS);
        List<SubsidyExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                SubsidyExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(SubsidyExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .in(CxrEmployeeDivideResult::getDivideSn,
                        DivideEnums.DivideSn.EXTRAS_SUBSIDY_FEE.getCode()
                )
        );


        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResults=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        for (SubsidyExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);
            Long employeeDivideId = employeeDivide.getId();
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivideId);
            if (checkImportMistake(divides,moneyListVo.getEmployeeName(),
                    moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList,month,moneyListVo.getBsMonth())){
                failureNum++;
                continue;
            }

            divideResultList.stream().map(x->{
                if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.EXTRAS_SUBSIDY_FEE.getCode())){
                    x.setResultValue(moneyListVo.getExtrasSubsidyFee());
                }
                x.setUpdateTime(LocalDateTime.now());
                x.setUpdateByName(loginUser.getNickName());
                x.setUpdateBy(loginUser.getUserId());
                divideResults.add(x);
                return x;
            }).collect(Collectors.toList());
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        updateEmployeeDivide(divideResults,employeeDivides.get(0).getEndDate(),employeeIds);

        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    @Override
    public String importMarketingExpense(String month, MultipartFile file) throws IOException {
        List<String> EXPECTED_HEADERS = Arrays.asList("月份", "销售代理", "销售代理编号", "杂费补贴","总补贴费用(元)");
        checkActualHeaders(file,EXPECTED_HEADERS);
        List<MarketingSubsidyExcel> earnestMoneyListVos = ExcelUtil.importExcel(file.getInputStream(),
                MarketingSubsidyExcel.class, 1, 0);
        StringBuilder builder = new StringBuilder();

        StringBuilder failureMsg = new StringBuilder();
        int failureNum = 0;

        int succeedNum = 0;

        if (CollUtil.isEmpty(earnestMoneyListVos)){
            throw new ServiceException("导入数据为空");
        }

        Set<String> employeeNo = earnestMoneyListVos.stream().map(MarketingSubsidyExcel::getEmployeeNo).collect(Collectors.toSet());
        List<CxrEmployeeDivide> employeeDivides= employeeDivideMapper.employeeDivideImport(month,null,null,employeeNo);

        if (CollUtil.isEmpty(employeeDivides)){
            throw new ServiceException("未匹配到数据,请检查导入文件或月份日期");
        }
        Map<String, List<CxrEmployeeDivide>> employeeDivideMap = employeeDivides.stream().collect((Collectors.groupingBy(CxrEmployeeDivide::getEmployeeNo)));

        List<Long> employeeDivideIds = employeeDivides.stream().map(CxrEmployeeDivide::getId).collect(Collectors.toList());
        List<CxrEmployeeDivideResult> employeeDivideResults = cxrEmployeeDivideResultService.list(new LambdaQueryWrapper<CxrEmployeeDivideResult>()
                .in(CxrEmployeeDivideResult::getCxrEmployeeDivideId, employeeDivideIds)
                .in(CxrEmployeeDivideResult::getDivideSn,
                        DivideEnums.DivideSn.EXTRAS_SUBSIDY_FEE.getCode(),
                        DivideEnums.DivideSn.SUBSIDY_FEE.getCode()
                )
        );

        Map<Long, List<CxrEmployeeDivideResult>> divideResultMap = employeeDivideResults.stream()
                .collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));

        Set<CxrEmployeeDivideResult> divideResults=new HashSet<>();
        Set<Long> employeeIds=new HashSet<>();
        LoginInfo loginUser = LoginUtil.getLoginUser();
        for (MarketingSubsidyExcel moneyListVo : earnestMoneyListVos) {
            List<CxrEmployeeDivide> divides = employeeDivideMap.get(moneyListVo.getEmployeeNo());
            if(CollUtil.isEmpty(divides)){
                failureMsg.append("编号"+moneyListVo.getEmployeeNo()+"-"+ moneyListVo.getEmployeeName()+" 数据不存在\\n");
                failureNum++;
                continue;
            }
            CxrEmployeeDivide employeeDivide = divides.get(0);
            Long employeeDivideId = employeeDivide.getId();
            List<CxrEmployeeDivideResult> divideResultList = divideResultMap.get(employeeDivideId);
            if (checkImportMistake(divides,moneyListVo.getEmployeeName(),moneyListVo.getEmployeeNo(),failureMsg,failureNum,divideResultList,month,moneyListVo.getBsMonth())){
                failureNum++;
                continue;
            }

            divideResultList.stream().map(x->{
                if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.EXTRAS_SUBSIDY_FEE.getCode())){
                    x.setResultValue(moneyListVo.getExtrasSubsidyFee());
                }
                if (StrUtil.equals(x.getDivideSn(),DivideEnums.DivideSn.SUBSIDY_FEE.getCode())){
                    x.setResultValue(moneyListVo.getSubsidyFee());
                }
                x.setUpdateTime(LocalDateTime.now());
                x.setUpdateByName(loginUser.getNickName());
                x.setUpdateBy(loginUser.getUserId());
                divideResults.add(x);
                return x;
            }).collect(Collectors.toList());
            employeeIds.add( divides.get(0).getEmployeeId());
            succeedNum++;
        }
        updateEmployeeDivide(divideResults,employeeDivides.get(0).getEndDate(),employeeIds);
        if (failureNum>0){
            builder.append(succeedNum).append("条").append("数据已成功导入");
            builder.append("\\n").append(failureNum).append("条数据导入失败，失败名单如下\\n").append(failureMsg);
        }
        return builder.toString();
    }

    private void updateEmployeeDivide(Set<CxrEmployeeDivideResult> divideResults, LocalDate date,Set<Long> employeeIds){
        Map<Long, List<CxrEmployeeDivideResult>> updateDivideResult = divideResults.stream().collect(Collectors.groupingBy(CxrEmployeeDivideResult::getCxrEmployeeDivideId));
        updateDivideResult.forEach((cxrEmployeeDivideId, cxrEmployeeDivideResults) -> {
            int count = cxrEmployeeDivideResultService.updateBatchByImport(cxrEmployeeDivideResults, cxrEmployeeDivideId, 0);
        });
        if (CollUtil.isNotEmpty(employeeIds)){
            for (Long employeeId : employeeIds) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("employeeId",employeeId);
                jsonObject.put("date", LocalDateTimeUtil.formatNormal(date));
                mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC,MqBizConst.DIVIDE_COMPUTE_ALL_TAG, JSONUtil.toJsonStr(jsonObject));
            }
        }
        Set<Long> divideIds = divideResults.stream().map(CxrEmployeeDivideResult::getCxrEmployeeDivideId).collect(Collectors.toSet());
        employeeDivideMapper.update(null,new LambdaUpdateWrapper<CxrEmployeeDivide>()
                .in(CxrEmployeeDivide::getId, divideIds)
                .set(CxrEmployeeDivide::getUpdateTime, LocalDateTime.now())
                .set(CxrEmployeeDivide::getFinalUpdateTime,LocalDateTime.now())
                .set(CxrEmployeeDivide::getFinalUpdateByName, LoginUtil.getLoginUser().getUserName())
        );
    }

    private Boolean checkImportMistake(List<CxrEmployeeDivide> divides,

                                       String employeeName,
                                       String employeeNo,

                                       StringBuilder failureMsg,
                                       int failureNum,
                                       List<CxrEmployeeDivideResult> divideResultList,
                                       String month, String bsMonth
    ){

        if (!StrUtil.equals(month.trim(),bsMonth.trim())){
            failureMsg.append("编号"+employeeNo+"-"+ employeeName+"-"+bsMonth+"月份错误"+"\\n");
            return true;
        }
        if (CollUtil.isEmpty(divides)||divides.size()>1){
            failureMsg.append("编号"+employeeNo+"-"+ employeeName+"\\n");
            return true;
        }
        if (CollUtil.isEmpty(divideResultList)){
            failureMsg.append("编号"+employeeNo+"-"+ employeeName+"\\n");
            return true;
        }
        return false;
    }

    private Boolean checkImportMistakeNotResult(List<CxrEmployeeDivide> divides,

                                                String employeeName,
                                                String employeeNo,

                                       StringBuilder failureMsg,
                                       int failureNum,
                                       List<CxrEmployeeDivideResult> divideResultList,
                                                String month, String bsMonth

    ){
        if (!StrUtil.equals(month.trim(),bsMonth.trim())){
            failureMsg.append("编号"+employeeNo+"-"+ employeeName+"-"+bsMonth+"月份错误"+"\\n");
            return true;
        }

        if (CollUtil.isEmpty(divides)||divides.size()>1){
            failureMsg.append("编号"+employeeNo+"-"+ employeeName+"\\n");
            return true;
        }
        return false;
    }

    private CxrEmployeeDivideResult addCxrEmployeeDivideResult(LoginInfo loginUser, Long employeeDivideId, BigDecimal resultValue,String code,String desc,String occStatus){
        CxrEmployeeDivideResult divideResult = new CxrEmployeeDivideResult();
        divideResult.setDivideSn(code);
        divideResult.setDivideName(desc);
        divideResult.setCreateTime(LocalDateTime.now());
        divideResult.setCxrEmployeeDivideId(employeeDivideId);
        divideResult.setResultValue(resultValue);
        divideResult.setStatus(1);
        divideResult.setRemark("后台导入");
        divideResult.setUpdateByName(loginUser.getUserName());
        divideResult.setUpdateTime(LocalDateTime.now());
        divideResult.setUpdateBy(loginUser.getUserId());
        divideResult.setOccupationStatus(occStatus);
        return divideResult;
    }

    private void computeDivideEarnestMoney(Set<CxrEmployeeDivideResult> results){
        for (CxrEmployeeDivideResult id : results) {
            //重新计算拿货价
            if (ObjectUtil.equals(id.getOccupationStatus(), OccupationStatus.INDUCTION.getCode())){
                divideComputeManager.computeDivideEarnestMoney(id.getCxrEmployeeDivideId());
                mqUtil.sendSyncMessage(MqBizConst.DIVIDE_COMPUTE_TOPIC, MqBizConst.DIVIDE_COMPUTE_EARNEST_MONEY_NEXT_TAG, id.getCxrEmployeeDivideId().toString()); //先不进行自动计算
            }
        }
    }

    private List<String> checkActualHeaders(MultipartFile file,List<String> EXPECTED_HEADERS){
        Workbook workbook = null;
        try {
            workbook = WorkbookFactory.create((file.getInputStream()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Sheet sheet = workbook.getSheetAt(0);
        Row headerRow = sheet.getRow(0);
        List<String> actualHeaders = new ArrayList<>();
        for (Cell cell : headerRow) {
            actualHeaders.add(cell.getStringCellValue());
        }
        if (!actualHeaders.equals(EXPECTED_HEADERS)) {
            throw new IllegalArgumentException("表头不匹配，请检查文件格式");
        }
        return actualHeaders;
    }

}
