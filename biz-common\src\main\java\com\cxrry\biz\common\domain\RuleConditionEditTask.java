package com.cxrry.biz.common.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 规则条件编辑任务表
 * @TableName rule_condition_edit_task
 */
@TableName(value = "rule_condition_edit_task")
@Data
public class RuleConditionEditTask implements Serializable {
  @TableField(exist = false)
  private static final long serialVersionUID = 1L;

  /**
   * 条件id
   */
  @TableId
  private Long id;

  /**
   * 规则集ID
   */
  @ApiModelProperty(value = "规则集id")
  private Long ruleSetId;

  /**
   * 规则编码
   */
  private String ruleSetCode;

  /**
   * 新规则阶段List
   */
  private String newStageList;

  /**
   * 旧规则阶段List
   */
  private String oldStageList;

  /**
   * 执行类型：1立即执行 2自定义执行
   */
  private Integer executeType;

  /**
   * 执行日期
   */
  private Date executeDay;

  /**
   * 执行状态：0未执行 1已执行
   */
  private Integer executeStatus;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 删除状态
   */
  private String deleteStatus;


}