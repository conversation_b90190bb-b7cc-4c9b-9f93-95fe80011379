package com.cxrry.biz.customer.support.domain.dto;

import com.cxrry.biz.common.json.JudgmentValue;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TriggerConditionDto implements Serializable {

    private Long id;


    private Long triggerId;

    /**
     * 条件组别1全部满足2其中一个满足
     */
    private Integer conditionalGroup;

    /**
     * 条件项1分类2状态3优先级4受理人
     */
    private Integer conditionEntry;

    /**
     * 前置条件判断1是2不是3包含任意4不包含任意
     */
    private Integer precondition;

    /**
     * 变更条件判断1已变更2更改为3更改自3未更改为4不是更改自启用状态启用状态删除状态删除状态
     */
    private Integer alteredCondition;

    /**
     * 判断值
     */
    private List<JudgmentValue> judgmentValue;

    // 条件拼接
    private String triggerConditionScript;

    private List<String> username;

}
