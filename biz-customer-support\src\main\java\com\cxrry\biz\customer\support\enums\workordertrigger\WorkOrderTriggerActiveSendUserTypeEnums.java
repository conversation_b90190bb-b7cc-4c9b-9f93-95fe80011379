package com.cxrry.biz.customer.support.enums.workordertrigger;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WorkOrderTriggerActiveSendUserTypeEnums {
    ACCEPTOR(1, "受理人"),
    SITE_MANAGER(2, "站点主管"),
    REGION_MANAGER(3, "区域经理"),
    REGION_DIRECTOR(4, "大区总监");

    private Integer code;
    private String description;

    public static WorkOrderTriggerActiveSendUserTypeEnums getByCode(Integer code) {
        for (WorkOrderTriggerActiveSendUserTypeEnums role : WorkOrderTriggerActiveSendUserTypeEnums.values()) {
            if (role.getCode() == code) {
                return role;
            }
        }
        throw new IllegalArgumentException("Invalid user role code: " + code);
    }
}
