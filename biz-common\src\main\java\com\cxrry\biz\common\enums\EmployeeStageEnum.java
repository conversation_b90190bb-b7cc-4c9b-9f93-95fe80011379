package com.cxrry.biz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 销售代理考核阶段
 */
@AllArgsConstructor
@Getter
public enum EmployeeStageEnum {
    PROMOTION_L1_TO_L2(1,EmployeeLevelEnum.L1, EmployeeLevelEnum.L2,ChangeTypeEnum.UPGRADE, "L1升L2"),
    PROMOTION_L1_TO_L3_30_DAYS(2,EmployeeLevelEnum.L1,EmployeeLevelEnum.L3, ChangeTypeEnum.UPGRADE,"L1升L3(30天)"),
    PROMOTION_L1_TO_L3_REST_DAYS(3,EmployeeLevelEnum.L1,EmployeeLevelEnum.L3,ChangeTypeEnum.UPGRADE, "L1升L3(当月剩余天数)"),
    PROMOTION_L1_TO_L3_NAT_MONTH(4, EmployeeLevelEnum.L1,EmployeeLevelEnum.L3,ChangeTypeEnum.UPGRADE,"L1升L3(自然月考核)"),

    PROMOTION_L2_TO_L3_30_DAYS(5,EmployeeLevelEnum.L2,EmployeeLevelEnum.L3, ChangeTypeEnum.UPGRADE,"L2升L3(30天)"),
    PROMOTION_L2_TO_L3_REST_DAYS(6, EmployeeLevelEnum.L2,EmployeeLevelEnum.L3,ChangeTypeEnum.UPGRADE,"L2升L3(当月剩余天数)"),
    PROMOTION_L2_TO_L3_NAT_MONTH(7, EmployeeLevelEnum.L2,EmployeeLevelEnum.L3,ChangeTypeEnum.UPGRADE,"L2升L3(自然月考核)"),

    PROMOTION_L3_TO_L4_NORMAL(8, EmployeeLevelEnum.L3,EmployeeLevelEnum.L4,ChangeTypeEnum.UPGRADE,"L3升L4(正常考核)"),
    PROMOTION_L3_TO_L4_CONSECUTIVE_MONTHS(9,EmployeeLevelEnum.L3,EmployeeLevelEnum.L4, ChangeTypeEnum.UPGRADE,"L3升L4"
        + "(降级后)"),

    DEGRADATION_L5_TO_L4(10,EmployeeLevelEnum.L5,EmployeeLevelEnum.L4, ChangeTypeEnum.DOWNGRADE,"L5下降L4"),
    DEGRADATION_L4_TO_L3(11, EmployeeLevelEnum.L4,EmployeeLevelEnum.L3,ChangeTypeEnum.DOWNGRADE,"L4下降L3"),
    DEGRADATION_L3_TO_L2(12,EmployeeLevelEnum.L3,EmployeeLevelEnum.L2, ChangeTypeEnum.DOWNGRADE,"L3下降L2");
    private final Integer stage;

    private final EmployeeLevelEnum  srcLevel;
    private final EmployeeLevelEnum targetLevel;
    private final ChangeTypeEnum changeType;//1 范围内（升级） 2范围外（降级）
    private final String description;


    // 根据序号获取枚举对象的方法
    public static EmployeeStageEnum fromSequence(int sequence) {
        for (EmployeeStageEnum stage : values()) {
            if (stage.getStage() == sequence) {
                return stage;
            }
        }
        throw new IllegalArgumentException("No EmployeeStageEnum with sequence: " + sequence);
    }

    public static String getNameByType(Integer type) {
        return fromSequence(type).getDescription();
    }

    // 达标情况，用于获取下一个晋升阶段
    public EmployeeStageEnum getNextPromotionStageOnSuccess() {
        switch (this) {
            case PROMOTION_L1_TO_L2:
                return PROMOTION_L2_TO_L3_30_DAYS;
            case PROMOTION_L1_TO_L3_REST_DAYS:
            case PROMOTION_L1_TO_L3_NAT_MONTH:
            case PROMOTION_L1_TO_L3_30_DAYS:
            case PROMOTION_L2_TO_L3_30_DAYS:
            case PROMOTION_L2_TO_L3_REST_DAYS:
            case PROMOTION_L2_TO_L3_NAT_MONTH:
                return PROMOTION_L3_TO_L4_NORMAL;
            case PROMOTION_L3_TO_L4_NORMAL:
            case PROMOTION_L3_TO_L4_CONSECUTIVE_MONTHS:
                // L4是最高级别，没有晋升阶段
                return null;
            default:
                throw new IllegalStateException("Invalid current stage: " + this);
        }
    }


    // 达标情况，用于获取下一个降级阶段
    public EmployeeStageEnum getNextDemotionStageOnSuccess() {
        switch (this) {
            case DEGRADATION_L5_TO_L4:
                return DEGRADATION_L4_TO_L3;
            case DEGRADATION_L4_TO_L3:
                return DEGRADATION_L3_TO_L2;
            case DEGRADATION_L3_TO_L2:
                return null;
            default:
                throw new IllegalStateException("Invalid current stage: " + this);
        }
    }
}