//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cxrry.biz.common.enums;

import cn.hutool.core.util.StrUtil;

public enum RebateType {
  /**
   * 一级介绍人
   */
  REBATE_ONE(1, "one", "一级介绍人"),
  /**
   * 二级介绍人
   */
  REBATE_TWO(2, "two", "二级介绍人"),
  /**
   * 师傅
   */
  REBATE_MASTER(3, "master", "师傅"),
  /**
   * 一级介绍人、二级介绍人、师傅汇总
   */
  REBATE_TOTAL(33, "total", "汇总");


  private final Integer value;
  private final String scope;
  private final String name;

  public static RebateType getEnumByValue(Integer value) {
    if (value == null) {
      return null;
    } else {
      RebateType[] postTypeArray = values();

      for(int i = 0; i < postTypeArray.length; ++i) {
        RebateType postType = postTypeArray[i];
        if (postType.getValue().equals(value)) {
          return postType;
        }
      }

      return null;
    }
  }

  public int compare(String another) {
    Integer value = Integer.valueOf(this.getValue());
    Integer anotherValue = Integer.valueOf(another);
    return value - anotherValue;
  }

  public Integer getValue() {
    return this.value;
  }

  public String getScope() {
    return this.scope;
  }

  public String getName() {
    return this.name;
  }

  private RebateType(Integer value, String scope, String name) {
    this.value = value;
    this.scope = scope;
    this.name = name;
  }
}
