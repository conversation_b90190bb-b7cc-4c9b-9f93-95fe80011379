package com.cxrry.biz.sales.operation.manager.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cxrry.biz.common.utils.DateUtils;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecord;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordLog;
import com.cxrry.biz.sales.operation.domain.dto.DelayCalculationResult;
import com.cxrry.biz.sales.operation.enums.cycle.AdjustSourceTypeEnum;
import com.cxrry.biz.sales.operation.manager.CycleDelayManager;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordLogService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordService;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 考核周期延期处理管理器实现
 */
@Slf4j
@Service
public class CycleDelayManagerImpl implements CycleDelayManager {

    @Autowired
    private CxrEmployeeAccessCycleRecordLogService accessCycleRecordLogService;
    
    @Autowired
    private CxrEmployeeAccessCycleRecordService accessCycleRecordService;

    @Override
    public DelayCalculationResult calculateDelay(CxrEmployeeAccessCycleRecord cycleRecord) {
        return this.calculateDelay(cycleRecord, null);
    }

    @Override
    public Boolean applyDelay(CxrEmployeeAccessCycleRecord cycleRecord, DelayCalculationResult delayResult) {
        if (delayResult == null || !delayResult.getHasDelay()) {
            log.info("无需延期，跳过应用");
            return true;
        }

        try {
            // 更新考核周期的实际结束日期
            cycleRecord.setActualAssessmentEndDate(delayResult.getNewEndDate());
            cycleRecord.setUpdateCycleReason(delayResult.getDelayProcessDescription());
            
            boolean success = accessCycleRecordService.updateById(cycleRecord);
            if (success) {
                log.info("成功应用延期{}天到周期{}", delayResult.getDelayDays(), cycleRecord.getId());
            }
            return success;
        } catch (Exception e) {
            log.error("应用延期失败", e);
            return false;
        }
    }

    @Override
    public DelayCalculationResult autoProcessDelayWithResult(CxrEmployeeAccessCycleRecord cycleRecord) {
        return autoProcessDelayWithResult(cycleRecord, null);
    }

    @Override
    public DelayCalculationResult autoProcessDelayWithResult(CxrEmployeeAccessCycleRecord cycleRecord,
        CxrEmployeeAccessCycleRecordLog currentAdjust) {
        DelayCalculationResult result = calculateDelay(cycleRecord, currentAdjust);
        
        // if (result.getHasDelay()) {
        //     // 如果没有传入当前调整，则直接应用延期
        //     if (currentAdjust == null) {
        //         Boolean success = applyDelay(cycleRecord, result);
        //         if (!success) {
        //             return DelayCalculationResult.builder()
        //                 .delayDays(0)
        //                 .hasDelay(false)
        //                 .calculationReason("延期应用失败")
        //                 .build();
        //         }
        //     }
        //     // 如果传入了当前调整，则不应用延期，只返回计算结果
            
        //     log.info("员工{}周期{}延期处理完成：{}天，原因：{}", 
        //         cycleRecord.getCxrEmployeeId(), 
        //         cycleRecord.getId(), 
        //         result.getDelayDays(), 
        //         result.getCalculationReason());
        // }
        
        return result;
    }

    @Override
    public Integer autoProcessDelay(CxrEmployeeAccessCycleRecord cycleRecord) {
        DelayCalculationResult result = autoProcessDelayWithResult(cycleRecord);
        return result.getDelayDays();
    }

    @Override
    public List<CxrEmployeeAccessCycleRecordLog> getActiveDelayRecords(CxrEmployeeAccessCycleRecord cycleRecord) {
        Date cycleStartDate = cycleRecord.getOriginalAssessmentStartDate();
        Date cycleEndDate = cycleRecord.getOriginalAssessmentEndDate();
        
        if (cycleStartDate == null || cycleEndDate == null) {
            log.warn("考核周期开始或结束日期为空，无法查询延期记录");
            return Collections.emptyList();
        }
        
        // 转换为LocalDate用于查询
        LocalDate cycleStartLocal =         DateUtil.toInstant(new Date(cycleStartDate.getTime())).atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate cycleEndLocal =         DateUtil.toInstant(new Date(cycleEndDate.getTime())).atZone(ZoneId.systemDefault()).toLocalDate();
        
        // 查询与考核周期有时间交集的延期记录
        List<CxrEmployeeAccessCycleRecordLog> allRecords = accessCycleRecordLogService.list(
            new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordLog>()
                .eq(CxrEmployeeAccessCycleRecordLog::getEmployeeId, cycleRecord.getCxrEmployeeId())
                // 调整结束日期 >= 考核开始日期 AND 调整开始日期 <= 考核结束日期
                .ge(CxrEmployeeAccessCycleRecordLog::getAdjustEndDate, cycleStartLocal)
                .le(CxrEmployeeAccessCycleRecordLog::getAdjustStartDate, cycleEndLocal)
                .orderByDesc(CxrEmployeeAccessCycleRecordLog::getUpdateTime)
        );
        
        // 进一步过滤，确保记录与考核周期确实有交集
        return allRecords.stream()
            .filter(record -> validateDelayRecord(record, cycleStartDate, cycleEndDate))
            .collect(Collectors.toList());
    }

    @Override
    public Boolean validateDelayRecord(CxrEmployeeAccessCycleRecordLog delayRecord, Date cycleStartDate, Date cycleEndDate) {
        if (delayRecord == null || cycleStartDate == null || cycleEndDate == null) {
            return false;
        }

        Date adjustStartDate = DateUtils.convertLocalDateToDate(delayRecord.getAdjustStartDate());
        Date adjustEndDate = DateUtils.convertLocalDateToDate(delayRecord.getAdjustEndDate());

        // 检查调整日期是否与考核周期有交集
        return !(adjustEndDate.before(cycleStartDate) || adjustStartDate.after(cycleEndDate));
    }

    /**
     * 按类型分组并获取最新记录
     */
    private Map<AdjustSourceTypeEnum, CxrEmployeeAccessCycleRecordLog> groupByTypeAndGetLatest(
            List<CxrEmployeeAccessCycleRecordLog> records) {
        
        return records.stream()
            .collect(Collectors.groupingBy(
                record -> AdjustSourceTypeEnum.fromCode(record.getSourceType()),
                Collectors.maxBy(Comparator.comparing(CxrEmployeeAccessCycleRecordLog::getUpdateTime))
            ))
            .entrySet().stream()
            .filter(entry -> entry.getKey() != null && entry.getValue().isPresent())
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().get()
            ));
    }

    /**
     * 按类型分组记录，个人请假保留全部记录，其他类型取最新记录
     */
    private GroupedDelayRecords groupRecordsByTypeWithPersonalLeaveAll(
            List<CxrEmployeeAccessCycleRecordLog> records) {
        
        Map<AdjustSourceTypeEnum, List<CxrEmployeeAccessCycleRecordLog>> allGrouped = records.stream()
            .collect(Collectors.groupingBy(record -> AdjustSourceTypeEnum.fromCode(record.getSourceType())))
            .entrySet().stream()
            .filter(entry -> entry.getKey() != null)
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        GroupedDelayRecords result = new GroupedDelayRecords();
        
        // 处理个人请假 - 保留全部记录
        if (allGrouped.containsKey(AdjustSourceTypeEnum.PERSONAL_LEAVE)) {
            result.setPersonalLeaveRecords(allGrouped.get(AdjustSourceTypeEnum.PERSONAL_LEAVE));
        }
        
        // 处理手动调整 - 取最新记录
        if (allGrouped.containsKey(AdjustSourceTypeEnum.MANUAL_ADJUST)) {
            result.setManualAdjustRecord(allGrouped.get(AdjustSourceTypeEnum.MANUAL_ADJUST).stream()
                .max(Comparator.comparing(CxrEmployeeAccessCycleRecordLog::getUpdateTime))
                .orElse(null));
        }
        
        // 处理公司放假 - 取最新记录
        if (allGrouped.containsKey(AdjustSourceTypeEnum.COMPANY_HOLIDAY)) {
            result.setCompanyHolidayRecord(allGrouped.get(AdjustSourceTypeEnum.COMPANY_HOLIDAY).stream()
                .max(Comparator.comparing(CxrEmployeeAccessCycleRecordLog::getUpdateTime))
                .orElse(null));
        }
        
        return result;
    }

    /**
     * 分组延期记录的包装类
     */
    private static class GroupedDelayRecords {
        private List<CxrEmployeeAccessCycleRecordLog> personalLeaveRecords;
        private CxrEmployeeAccessCycleRecordLog manualAdjustRecord;
        private CxrEmployeeAccessCycleRecordLog companyHolidayRecord;
        
        public boolean hasPersonalLeave() {
            return CollUtil.isNotEmpty(personalLeaveRecords);
        }
        
        public boolean hasManualAdjust() {
            return manualAdjustRecord != null;
        }
        
        public boolean hasCompanyHoliday() {
            return companyHolidayRecord != null;
        }
        
        // getters and setters
        public List<CxrEmployeeAccessCycleRecordLog> getPersonalLeaveRecords() {
            return personalLeaveRecords;
        }
        
        public void setPersonalLeaveRecords(List<CxrEmployeeAccessCycleRecordLog> personalLeaveRecords) {
            this.personalLeaveRecords = personalLeaveRecords;
        }
        
        public CxrEmployeeAccessCycleRecordLog getManualAdjustRecord() {
            return manualAdjustRecord;
        }
        
        public void setManualAdjustRecord(CxrEmployeeAccessCycleRecordLog manualAdjustRecord) {
            this.manualAdjustRecord = manualAdjustRecord;
        }
        
        public CxrEmployeeAccessCycleRecordLog getCompanyHolidayRecord() {
            return companyHolidayRecord;
        }
        
        public void setCompanyHolidayRecord(CxrEmployeeAccessCycleRecordLog companyHolidayRecord) {
            this.companyHolidayRecord = companyHolidayRecord;
        }
    }

    /**
     * 应用简化规则（使用分组记录）
     */
    private DelayCalculationResult applySimplifiedRulesWithGroupedRecords(
            GroupedDelayRecords groupedRecords,
            Date cycleStartDate, Date cycleEndDate) {

        // 如果存在手动调整，只取手动调整
        if (groupedRecords.hasManualAdjust()) {
            return useManualAdjustOnly(groupedRecords.getManualAdjustRecord(), cycleStartDate, cycleEndDate);
        }

        boolean hasPersonal = groupedRecords.hasPersonalLeave();
        boolean hasCompany = groupedRecords.hasCompanyHoliday();

        if (hasPersonal && hasCompany) {
            // 计算个人请假（全部记录）与公司放假的交集
            return calculatePersonalLeaveAndCompanyHolidayIntersection(
                groupedRecords.getPersonalLeaveRecords(),
                groupedRecords.getCompanyHolidayRecord(),
                cycleStartDate,
                cycleEndDate
            );
        } else if (hasPersonal) {
            return useAllPersonalLeaveRecords(groupedRecords.getPersonalLeaveRecords(), 
                cycleStartDate, cycleEndDate);
        } else if (hasCompany) {
            return useSingleTypeDelay(groupedRecords.getCompanyHolidayRecord(), 
                cycleStartDate, cycleEndDate, "公司放假延期");
        }

        return DelayCalculationResult.noDelay();
    }

    /**
     * 使用手动调整
     */
    private DelayCalculationResult useManualAdjustOnly(CxrEmployeeAccessCycleRecordLog manualRecord,Date cycleStartDate,
        Date cycleEndDate) {

        int delayDays = calculateDaysInCycle(DateUtils.convertLocalDateToDate(manualRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(manualRecord.getAdjustEndDate()), cycleStartDate, cycleEndDate);
        Date newEndDate = DateUtil.offsetDay(cycleEndDate, delayDays);
        String processDescription = String.format(
            "发现手动调整记录(ID:%d)，按简化规则优先处理手动调整。延期%d天",
            manualRecord.getId(),
            Math.max(0, delayDays)
        );
        
        return DelayCalculationResult.builder()
            .delayDays(Math.max(0, delayDays))
            .newEndDate(newEndDate)
            .hasDelay(delayDays > 0)
            .calculationReason("手动调整延期")
            .delayProcessDescription(processDescription)
            .usedRecordIds(ListUtil.of(manualRecord.getId()))
            .build();
    }

    /**
     * 使用单一类型延期
     */
    private DelayCalculationResult useSingleTypeDelay(CxrEmployeeAccessCycleRecordLog record, 
            Date cycleStartDate, Date cycleEndDate, String reason) {
        
        int daysInCycle = calculateDaysInCycle(
            DateUtils.convertLocalDateToDate(record.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(record.getAdjustEndDate()),
            cycleStartDate, cycleEndDate
        );

        if (daysInCycle <= 0) {
            return DelayCalculationResult.builder()
                .delayDays(0)
                .hasDelay(false)
                .calculationReason("无需延期")
                .delayProcessDescription(String.format(
                    "检测到%s记录(ID:%d)，但该记录与考核周期无交集，无需延期",
                    reason.replace("延期", ""),
                    record.getId()
                ))
                .build();
        }

        Date newEndDate = DateUtil.offsetDay(cycleEndDate, daysInCycle);
        
        String processDescription = String.format(
            "检测到%s记录(ID:%d)，调整时间:%s至%s，在考核周期内有效天数:%d天，延期%d天，新结束日期:%s",
            reason.replace("延期", ""),
            record.getId(),
            DateUtil.formatDate(DateUtils.convertLocalDateToDate(record.getAdjustStartDate())),
            DateUtil.formatDate(DateUtils.convertLocalDateToDate(record.getAdjustEndDate())),
            daysInCycle,
            daysInCycle,
            DateUtil.formatDate(newEndDate)
        );
        
        return DelayCalculationResult.builder()
            .delayDays(daysInCycle)
            .newEndDate(newEndDate)
            .hasDelay(true)
            .calculationReason(reason + daysInCycle + "天")
            .delayProcessDescription(processDescription)
            .usedRecordIds(ListUtil.of(record.getId()))
            .build();
    }

    /**
     * 计算最大交集延期
     */
    private DelayCalculationResult calculateMaxIntersection(
            CxrEmployeeAccessCycleRecordLog personalRecord,
            CxrEmployeeAccessCycleRecordLog companyRecord,
            Date cycleStartDate, Date cycleEndDate) {

        // 计算个人请假在考核周期内的天数
        int personalDaysInCycle = calculateDaysInCycle(
            DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
            cycleStartDate, cycleEndDate
        );

        // 计算公司放假在考核周期内的天数
        int companyDaysInCycle = calculateDaysInCycle(
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
            cycleStartDate, cycleEndDate
        );

        // 检查是否有重叠
        boolean hasOverlap = checkOverlap(
            DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
            cycleStartDate, cycleEndDate
        );

        int finalDelayDays;
        String reason;
        String processDescription;

        if (hasOverlap) {
            // 有重叠，计算合并后的总天数
            finalDelayDays = calculateMergedDaysInCycle(
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
                cycleStartDate, cycleEndDate
            );
            reason = String.format("个人请假与公司放假有重叠，合并延期天数%d天", finalDelayDays);
            processDescription = String.format(
                "检测到个人请假记录(ID:%d)和公司放假记录(ID:%d)。个人请假在周期内%d天，公司放假在周期内%d天。" +
                "两者存在时间重叠，按合并规则延期%d天",
                personalRecord.getId(),
                companyRecord.getId(),
                personalDaysInCycle,
                companyDaysInCycle,
                finalDelayDays
            );
        } else {
            // 无重叠，累加
            finalDelayDays = personalDaysInCycle + companyDaysInCycle;
            reason = String.format("个人请假与公司放假无重叠，累加延期天数%d天", finalDelayDays);
            processDescription = String.format(
                "检测到个人请假记录(ID:%d)和公司放假记录(ID:%d)。个人请假在周期内%d天，公司放假在周期内%d天。" +
                "两者无时间重叠，按累加规则延期%d天",
                personalRecord.getId(),
                companyRecord.getId(),
                personalDaysInCycle,
                companyDaysInCycle,
                finalDelayDays
            );
        }

        Date newEndDate = DateUtil.offsetDay(cycleEndDate, finalDelayDays);

        return DelayCalculationResult.builder()
            .delayDays(finalDelayDays)
            .newEndDate(newEndDate)
            .hasDelay(finalDelayDays > 0)
            .calculationReason(reason)
            .delayProcessDescription(processDescription)
            .personalLeaveDaysInCycle(personalDaysInCycle)
            .companyHolidayDaysInCycle(companyDaysInCycle)
            .hasOverlap(hasOverlap)
            .usedRecordIds(Arrays.asList(personalRecord.getId(), companyRecord.getId()))
            .build();
    }

    /**
     * 计算某个时间段在考核周期内的天数
     */
    private int calculateDaysInCycle(Date startDate, Date endDate, Date cycleStart, Date cycleEnd) {
        Date effectiveStart = Collections.max(Arrays.asList(startDate, cycleStart));
        Date effectiveEnd = endDate;

        if (effectiveStart.compareTo(effectiveEnd) <= 0) {
            return (int) DateUtil.between(effectiveStart, effectiveEnd, DateUnit.DAY, false) + 1;
        }
        return 0;
    }

    /**
     * 检查两个时间段在考核周期内是否有重叠
     */
    private boolean checkOverlap(Date start1, Date end1, Date start2, Date end2, 
            Date cycleStart, Date cycleEnd) {
        
        // 计算在考核周期内的有效时间段
        Date effectiveStart1 = Collections.max(Arrays.asList(start1, cycleStart));
        Date effectiveEnd1 = Collections.min(Arrays.asList(end1, cycleEnd));
        Date effectiveStart2 = Collections.max(Arrays.asList(start2, cycleStart));
        Date effectiveEnd2 = Collections.min(Arrays.asList(end2, cycleEnd));

        // 检查两个有效时间段是否有重叠
        return !(effectiveEnd1.before(effectiveStart2) || effectiveEnd2.before(effectiveStart1));
    }

    /**
     * 计算两个时间段在考核周期内合并后的总天数
     */
    private int calculateMergedDaysInCycle(Date start1, Date end1, Date start2, Date end2, 
            Date cycleStart, Date cycleEnd) {
        
        // 计算在考核周期内的有效时间段
        Date effectiveStart1 = Collections.max(Arrays.asList(start1, cycleStart));
        Date effectiveEnd1 = end1;
        Date effectiveStart2 = Collections.max(Arrays.asList(start2, cycleStart));
        Date effectiveEnd2 = end2;

        // 如果任一时间段无效，返回另一个的天数
        if (effectiveStart1.after(effectiveEnd1)) {
            return calculateDaysInCycle(start2, end2, cycleStart, cycleEnd);
        }
        if (effectiveStart2.after(effectiveEnd2)) {
            return calculateDaysInCycle(start1, end1, cycleStart, cycleEnd);
        }

        // 计算合并后的时间范围
        Date mergedStart = Collections.min(Arrays.asList(effectiveStart1, effectiveStart2));
        Date mergedEnd = Collections.max(Arrays.asList(effectiveEnd1, effectiveEnd2));

        // 返回合并后的天数
        return (int) DateUtil.between(mergedStart, mergedEnd, DateUnit.DAY, false) + 1;
    }

    private DelayCalculationResult calculateDelay(CxrEmployeeAccessCycleRecord cycleRecord, 
                                                CxrEmployeeAccessCycleRecordLog currentAdjust) {
        log.info("开始计算员工{}周期{}的延期天数", cycleRecord.getCxrEmployeeId(), cycleRecord.getId());
        
        // 1. 查询所有有效延期记录（包含当前调整）
        List<CxrEmployeeAccessCycleRecordLog> allRecords = getActiveDelayRecordsWithCurrent(cycleRecord, currentAdjust);
        if (CollUtil.isEmpty(allRecords)) {
            log.info("员工{}周期{}无延期记录", cycleRecord.getCxrEmployeeId(), cycleRecord.getId());
            return DelayCalculationResult.noDelay();
        }

        // 2. 按类型分组，个人请假取全部记录，其他类型取最新记录
        GroupedDelayRecords groupedRecords = groupRecordsByTypeWithPersonalLeaveAll(allRecords);

        Date cycleStartDate = cycleRecord.getOriginalAssessmentStartDate();
        Date cycleEndDate = cycleRecord.getOriginalAssessmentEndDate();

        // 3. 应用简化规则
        return applySimplifiedRulesWithGroupedRecords(groupedRecords, cycleStartDate, cycleEndDate);
    }

    private List<CxrEmployeeAccessCycleRecordLog> getActiveDelayRecordsWithCurrent(CxrEmployeeAccessCycleRecord cycleRecord,
                                                                                  CxrEmployeeAccessCycleRecordLog currentAdjust) {
        // 1. 获取已保存的延期记录
        List<CxrEmployeeAccessCycleRecordLog> existingRecords = getActiveDelayRecords(cycleRecord);
        
        // 2. 如果有当前调整记录，且与周期有交集，则加入列表
        if (currentAdjust != null) {
            Date cycleStartDate = cycleRecord.getOriginalAssessmentStartDate();
            Date cycleEndDate = cycleRecord.getOriginalAssessmentEndDate();
            
            // 验证当前调整是否与考核周期有交集
            if (validateCurrentAdjustRecord(currentAdjust, cycleStartDate, cycleEndDate)) {
                // 检查是否已经存在相同ID的记录，避免重复添加
                boolean alreadyExists = existingRecords.stream()
                    .anyMatch(record -> record.getId().equals(currentAdjust.getId()));
                
                if (!alreadyExists) {
                    existingRecords.add(currentAdjust);
                    log.info("加入当前调整记录到延期计算中，调整类型：{}", currentAdjust.getSourceType());
                } else {
                    log.info("当前调整记录(ID:{})已存在于延期记录中，跳过重复添加", currentAdjust.getId());
                }
            }
        }
        
        return existingRecords;
    }

    private Boolean validateCurrentAdjustRecord(CxrEmployeeAccessCycleRecordLog currentAdjust, 
                                               Date cycleStartDate, Date cycleEndDate) {
        if (currentAdjust == null || cycleStartDate == null || cycleEndDate == null) {
            return false;
        }

        Date adjustStartDate = DateUtils.convertLocalDateToDate(currentAdjust.getAdjustStartDate());
        Date adjustEndDate = DateUtils.convertLocalDateToDate(currentAdjust.getAdjustEndDate());

        // 检查调整日期是否与考核周期有交集
        return !(adjustEndDate.before(cycleStartDate) || adjustStartDate.after(cycleEndDate));
    }

    /**
     * 计算个人请假（全部记录）与公司放假的交集
     */
    private DelayCalculationResult calculatePersonalLeaveAndCompanyHolidayIntersection(
            List<CxrEmployeeAccessCycleRecordLog> personalLeaveRecords,
            CxrEmployeeAccessCycleRecordLog companyRecord,
            Date cycleStartDate, Date cycleEndDate) {

        // 1. 先计算所有个人请假记录的总天数（不重叠）
        int totalPersonalLeaveDays = 0;
        List<Long> usedPersonalLeaveIds = new ArrayList<>();
        StringBuilder personalLeaveDetails = new StringBuilder();
        
        for (CxrEmployeeAccessCycleRecordLog personalRecord : personalLeaveRecords) {
            int daysInCycle = calculateDaysInCycle(
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                cycleStartDate, cycleEndDate
            );
            
            if (daysInCycle > 0) {
                totalPersonalLeaveDays += daysInCycle;
                usedPersonalLeaveIds.add(personalRecord.getId());
                personalLeaveDetails.append(String.format(
                    "个人请假(ID:%d,%s至%s,周期内%d天);",
                    personalRecord.getId(),
                    DateUtil.formatDate(DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate())),
                    DateUtil.formatDate(DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate())),
                    daysInCycle
                ));
            }
        }

        // 2. 计算公司放假在考核周期内的天数
        int companyHolidayDays = calculateDaysInCycle(
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
            DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
            cycleStartDate, cycleEndDate
        );

        // 3. 计算个人请假与公司放假的重叠天数
        int overlapDays = 0;
        StringBuilder overlapDetails = new StringBuilder();
        
        for (CxrEmployeeAccessCycleRecordLog personalRecord : personalLeaveRecords) {
            // 检查每个个人请假记录与公司放假是否有重叠
            boolean hasOverlap = checkOverlap(
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
                cycleStartDate, cycleEndDate
            );
            
            if (hasOverlap) {
                // 计算具体的重叠天数
                int personalOverlapDays = calculateMergedDaysInCycle(
                    DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                    DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                    DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate()),
                    DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate()),
                    cycleStartDate, cycleEndDate
                );
                
                // 重叠天数 = 合并后天数 - 个人请假天数 - 公司放假天数 + 实际重叠天数
                // 简化：重叠天数 = 个人请假天数 + 公司放假天数 - 合并后天数
                int personalDays = calculateDaysInCycle(
                    DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                    DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                    cycleStartDate, cycleEndDate
                );
                
                int currentOverlap = personalDays + companyHolidayDays - personalOverlapDays;
                overlapDays += currentOverlap;
                
                if (currentOverlap > 0) {
                    overlapDetails.append(String.format(
                        "个人请假(ID:%d)与公司放假重叠%d天;",
                        personalRecord.getId(),
                        currentOverlap
                    ));
                }
            }
        }

        // 4. 计算最终延期天数：个人请假总天数 + 公司放假天数 - 重叠天数
        int finalDelayDays = totalPersonalLeaveDays + companyHolidayDays - overlapDays;
        
        String reason = String.format("多条个人请假与公司放假处理，延期%d天", finalDelayDays);
        
        String processDescription = String.format(
            "检测到%d条个人请假记录：%s 总计%d天。检测到公司放假记录(ID:%d)：%s至%s，周期内%d天。%s" +
            "最终延期计算：%d(个人请假) + %d(公司放假) - %d(重叠) = %d天",
            personalLeaveRecords.size(),
            personalLeaveDetails.toString(),
            totalPersonalLeaveDays,
            companyRecord.getId(),
            DateUtil.formatDate(DateUtils.convertLocalDateToDate(companyRecord.getAdjustStartDate())),
            DateUtil.formatDate(DateUtils.convertLocalDateToDate(companyRecord.getAdjustEndDate())),
            companyHolidayDays,
            overlapDays > 0 ? overlapDetails.toString() : "无重叠。",
            totalPersonalLeaveDays,
            companyHolidayDays,
            overlapDays,
            finalDelayDays
        );

        Date newEndDate = DateUtil.offsetDay(cycleEndDate, finalDelayDays);
        
        List<Long> allUsedRecordIds = new ArrayList<>(usedPersonalLeaveIds);
        allUsedRecordIds.add(companyRecord.getId());

        return DelayCalculationResult.builder()
            .delayDays(finalDelayDays)
            .newEndDate(newEndDate)
            .hasDelay(finalDelayDays > 0)
            .calculationReason(reason)
            .delayProcessDescription(processDescription)
            .personalLeaveDaysInCycle(totalPersonalLeaveDays)
            .companyHolidayDaysInCycle(companyHolidayDays)
            .hasOverlap(overlapDays > 0)
            .usedRecordIds(allUsedRecordIds)
            .build();
    }

    /**
     * 使用所有个人请假记录
     */
    private DelayCalculationResult useAllPersonalLeaveRecords(List<CxrEmployeeAccessCycleRecordLog> personalLeaveRecords, 
            Date cycleStartDate, Date cycleEndDate) {
        
        // 循环计算每个个人请假记录的天数并累加
        int totalPersonalLeaveDays = 0;
        List<Long> usedPersonalLeaveIds = new ArrayList<>();
        StringBuilder personalLeaveDetails = new StringBuilder();
        
        for (CxrEmployeeAccessCycleRecordLog personalRecord : personalLeaveRecords) {
            int daysInCycle = calculateDaysInCycle(
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate()),
                cycleStartDate, cycleEndDate
            );
            
            if (daysInCycle > 0) {
                totalPersonalLeaveDays += daysInCycle;
                usedPersonalLeaveIds.add(personalRecord.getId());
                personalLeaveDetails.append(String.format(
                    "个人请假(ID:%d,%s至%s,周期内%d天);",
                    personalRecord.getId(),
                    DateUtil.formatDate(DateUtils.convertLocalDateToDate(personalRecord.getAdjustStartDate())),
                    DateUtil.formatDate(DateUtils.convertLocalDateToDate(personalRecord.getAdjustEndDate())),
                    daysInCycle
                ));
            }
        }

        if (totalPersonalLeaveDays <= 0) {
            return DelayCalculationResult.builder()
                .delayDays(0)
                .hasDelay(false)
                .calculationReason("无需延期")
                .delayProcessDescription(String.format(
                    "检测到%d条个人请假记录，但都与考核周期无交集，无需延期",
                    personalLeaveRecords.size()
                ))
                .build();
        }

        Date newEndDate = DateUtil.offsetDay(cycleEndDate, totalPersonalLeaveDays);
        
        String processDescription = String.format(
            "检测到%d条个人请假记录：%s 总计在周期内有效天数:%d天，延期%d天，新结束日期:%s",
            personalLeaveRecords.size(),
            personalLeaveDetails.toString(),
            totalPersonalLeaveDays,
            totalPersonalLeaveDays,
            DateUtil.formatDate(newEndDate)
        );
        
        return DelayCalculationResult.builder()
            .delayDays(totalPersonalLeaveDays)
            .newEndDate(newEndDate)
            .hasDelay(true)
            .calculationReason(String.format("个人请假延期%d天", totalPersonalLeaveDays))
            .delayProcessDescription(processDescription)
            .usedRecordIds(usedPersonalLeaveIds)
            .build();
    }

    /**
     * 应用简化规则（保留旧版本兼容性）
     */
    private DelayCalculationResult applySimplifiedRules(
            Map<AdjustSourceTypeEnum, CxrEmployeeAccessCycleRecordLog> latestByType,
            Date cycleStartDate, Date cycleEndDate) {

        // 如果存在手动调整，只取手动调整
        if (latestByType.containsKey(AdjustSourceTypeEnum.MANUAL_ADJUST)) {
            return useManualAdjustOnly(latestByType.get(AdjustSourceTypeEnum.MANUAL_ADJUST), cycleStartDate, cycleEndDate);
        }

        boolean hasPersonal = latestByType.containsKey(AdjustSourceTypeEnum.PERSONAL_LEAVE);
        boolean hasCompany = latestByType.containsKey(AdjustSourceTypeEnum.COMPANY_HOLIDAY);

        if (hasPersonal && hasCompany) {
            // 计算最大交集
            return calculateMaxIntersection(
                latestByType.get(AdjustSourceTypeEnum.PERSONAL_LEAVE),
                latestByType.get(AdjustSourceTypeEnum.COMPANY_HOLIDAY),
                cycleStartDate,
                cycleEndDate
            );
        } else if (hasPersonal) {
            return useSingleTypeDelay(latestByType.get(AdjustSourceTypeEnum.PERSONAL_LEAVE), 
                cycleStartDate, cycleEndDate, "个人请假延期");
        } else if (hasCompany) {
            return useSingleTypeDelay(latestByType.get(AdjustSourceTypeEnum.COMPANY_HOLIDAY), 
                cycleStartDate, cycleEndDate, "公司放假延期");
        }

        return DelayCalculationResult.noDelay();
    }
}







