package com.cxrry.biz.sales.operation.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class AccountInformationDetailVo implements Serializable {

    private Long id;

    /**
     * 账户信息确认状态：0.未确认，1.已确认
     */
    private Integer accountInformationConfirmStatus;

    /**
     * 账号信息确认时间
     */
    private LocalDateTime accountInformationTime;

    /**
     * 账号信息反馈问题
     */
    private String accountInformationIssue;

    /**
     * 账号信息确认人
     */
    private String accountInformationConfirmName;

    /**
     * 开户银行名称
     */
    private String depositBankName;

    /**
     * 开户银行卡姓名
     */
    private String depositBankCardName;

    /**
     * 开户银行卡电话
     */
    private String depositBankCardPhone;

    /**
     * 开户银行卡身份证
     */
    private String depositBankCardIdCard;

    /**
     * 开户银行卡卡号
     */
    private String depositBankCardNumber;


}
