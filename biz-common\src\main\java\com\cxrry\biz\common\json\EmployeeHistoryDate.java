package com.cxrry.biz.common.json;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;

/**
 * Description:
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/9/6 21:52
 */
@Data
public class EmployeeHistoryDate implements Serializable {

    /**
     * 合作id
     */
    private Long inductionId;

    /**
     * 入职开始时间
     */
    private Date startDate;

    /**
     * 入职结束时间
     */
    private Date endDate;

    /**
     * 结束日期
     */
    private LocalDate end;

    /**
     * 离职状态
     */
    private String Occupation;
    /**
     * 离职审核人id
     */
    private Long auditId;

    /**
     * 离职审核人名称
     */
    private String auditNmae;

    /**
     * 离职时的销售代理名称
     */
    private String employeeName;
    /**
     * 离职时的销售代理等级
     */
    private String level;

}
