package com.cxrry.biz.customer.support.execute;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cxrry.biz.customer.support.domain.CxrEmployeePost;
import com.cxrry.biz.customer.support.domain.SysUserPost;
import com.cxrry.biz.customer.support.domain.WorkOrder;
import com.cxrry.biz.customer.support.domain.WorkOrderOperation;
import com.cxrry.biz.customer.support.domain.WorkOrderPriorityNode;
import com.cxrry.biz.customer.support.domain.bo.TaskParameterBo;
import com.cxrry.biz.customer.support.domain.vo.SysUserVosPlus;
import com.cxrry.biz.customer.support.enums.workorder.TaskOperationEnums;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderOperatorTypeEnums;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import com.cxrry.biz.customer.support.mapper.erp.SysDeptMapper;
import com.cxrry.biz.customer.support.service.WorkOrderOperationService;
import com.cxrry.biz.customer.support.service.WorkOrderPriorityNodeService;
import com.cxrry.biz.customer.support.service.WorkOrderService;
import com.cxrry.biz.customer.support.service.WorkOrderTriggerActiveService;
import com.cxrry.biz.customer.support.service.WorkOrderTriggerConditionService;
import com.cxrry.biz.customer.support.utils.WorkOrderRoleCmpUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.system.api.RemoteUserService;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class TimeoutHandler {

    private static final Logger logger = LoggerFactory.getLogger(TimeoutHandler.class);
    private static final ExecutorService executorService = Executors
            .newCachedThreadPool(
                    new ThreadFactoryBuilder().setNameFormat("timeoutExecute-pool-%d").build());

    private final static String SYS_REMARk = "销售代理同步初始密码:cxr123456";

    @Autowired
    private TaskTimeoutHelper retrievers;
    @Autowired
    private WorkOrderPriorityNodeService workOrderPriorityNodeService;
    @Autowired
    private WorkOrderTriggerActiveService workOrderTriggerActiveService;
    @Autowired
    private WorkOrderTriggerConditionService workflowTriggerConditionService;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private WorkOrderOperationService workOrderOperationService;

    @Autowired
    private TaskTimeoutHelper taskTimeoutHelper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @DubboReference
    private RemoteUserService remoteUserService;
    @Autowired
    private  WorkOrderRoleCmpUtil workOrderRoleCmpUtil;

    private volatile boolean running = true;

    @PostConstruct
    public void handle() {
        executorService.submit(() -> {
            int count = 0;
            while (running) {
                try {
                    String response = retrievers.retrieve();
                    logger.info("任务【{}】超时，开始处理", response);
                    TaskParameterBo parameterBo = BeanUtil.copyProperties(JSONUtil.parse(response),TaskParameterBo.class);
                    if (ObjectUtil.isNotEmpty(parameterBo)) {
                        Long priorityNodeId = parameterBo.getPriorityNodeId();
                        String operationType = parameterBo.getOperationType();

                        if (TaskOperationEnums.overtime.getOperation().equals(operationType)){
                            WorkOrderPriorityNode orderPriorityNode = workOrderPriorityNodeService.getById(priorityNodeId);
                            if (ObjectUtil.isEmpty(orderPriorityNode)){
                                throw new ServiceException("节点数据异常！");
                            }
                            Integer nodeLevel = orderPriorityNode.getNodeLevel();
                            WorkOrderPriorityNode nextNode=workOrderPriorityNodeService.getNextNode(nodeLevel,
                                orderPriorityNode.getWorkOrderId());
                            if (ObjectUtil.isEmpty(nextNode)){
                                logger.info("任务下一个节点【{}】，不存在", priorityNodeId);
                            }

                            LocalDateTime startDateTime = finishHoursBetween(orderPriorityNode);
                            orderPriorityNode.setPriorityStatus(WorkOrderProcessStatusEnums.HAVEEXPECTED.getCode());
                            // 关闭节点
                            workOrderPriorityNodeService.updateById(orderPriorityNode);
                            operatioAlteration(priorityNodeId);

                                // 开启下一个节点
                                nextNode.setPriorityStatus(WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode());
                                logger.info("任务开启下一个节点【{}】，开始处理", nextNode.getId());
                                boolean update = workOrderPriorityNodeService.updateById(nextNode);
                                Long workOrderId = nextNode.getWorkOrderId();
                                WorkOrder workOrder =null;
                                if (update){
                                    Long nextNodeId = nextNode.getId();
                                    workOrderOperationService.update(null,new LambdaUpdateWrapper<WorkOrderOperation>()
                                        .eq(WorkOrderOperation::getWorkOrderPriorityNodeId,nextNodeId)
                                        .set(WorkOrderOperation::getPriorityStatus,
                                            WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode())
                                    );
                                    List<WorkOrderOperation> operationList=
                                        workOrderOperationService.getNodeOperation(nextNodeId);

                                    LambdaUpdateWrapper<WorkOrder> updateWrapper = new LambdaUpdateWrapper<>();
                                    updateWrapper.eq(WorkOrder::getId,workOrderId)
                                        .set(WorkOrder::getAcceptedNodeName,nextNode.getAcceptedNodeName())
                                    ;
                                    if (CollUtil.isNotEmpty(operationList)){
                                        // 触发器 主表信息变更
                                        List<Long> operatorIds = operationList.stream()
                                            .filter(x->ObjectUtil.isNotEmpty(x.getPriorityerId()))
                                            .map(WorkOrderOperation::getPriorityerId).collect(Collectors.toList());
//                                        List<SysUser> sysUsers = remoteUserService.getUserListById(operatorIds);

                                        if (CollUtil.isNotEmpty(operatorIds)){
                                            List<SysUserVosPlus> sysUsers = sysDeptMapper.getUserListVoList(
                                                operatorIds);
                                            String nickName = sysUsers.stream().map(SysUserVosPlus::getNickName)
                                                .collect(Collectors.joining(","));

                                            String jobNumber = sysUsers.stream()
                                                .filter(x -> ObjectUtil.isNotEmpty(x.getEmployeeId()))
                                                .map(SysUserVosPlus::getUserName)
                                                .collect(Collectors.joining(","));


                                            workOrder = workOrderService.getById(workOrderId);
                                            SysUserVosPlus operatorUser = sysUsers.get(0);

                                            updateWrapper
                                                .set(WorkOrder::getAcceptorStr,
                                                    nickName)
                                                .set(WorkOrder::getAcceptorNumber,
                                                    jobNumber
                                                )
                                                .set(WorkOrder::getAcceptedAccount,
                                                    StrUtil.equals(operatorUser.getRemark(),SYS_REMARk)?1:2
                                                );
                                        }

                                    }
                                    workOrderService.update(null,updateWrapper);
                                }

                                TaskParameterBo nextTask=new TaskParameterBo();
                                nextTask.setPriorityNodeId(nextNode.getId()); // 下一个节点

                                long startMilli = startDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
                                Date finishTime = nextNode.getLimitFinishTime();
                                Instant finishInstant = finishTime.toInstant();
                                LocalDateTime endTime = LocalDateTime.ofInstant(finishInstant,
                                    ZoneId.systemDefault());
                                long endMilli = endTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();

                            WorkOrderPriorityNode priorityNode = workOrderPriorityNodeService.getNextNode(
                                nextNode.getNodeLevel(),
                                orderPriorityNode.getWorkOrderId());
                               if (ObjectUtil.isNotEmpty(priorityNode)){
                                    nextTask.setOperationType(TaskOperationEnums.overtime.getOperation());
                                }else {
                                    // 最后一个节点
                                    nextTask.setOperationType(TaskOperationEnums.overdue.getOperation());
                               }
                                taskTimeoutHelper.setTimeout(JSONUtil.toJsonStr(nextTask), endMilli - startMilli,
                                    startMilli);
                                // 通知 短信 / 模板消息

                                WorkOrder newWorkOrder = workOrderService.getById(workOrderId);
                                workOrderRoleCmpUtil.startEditRole(workOrder,newWorkOrder);
                                logger.info("任务开启下一个节点【{}】，succeed succeed succeed", nextNode.getId());
                        }else if (TaskOperationEnums.overdue.getOperation().equals(operationType)){
                            // 逾期逻辑

                            logger.info("逾期节点【{}】，", priorityNodeId);
                            WorkOrderPriorityNode orderPriorityNode = workOrderPriorityNodeService.getById(priorityNodeId);
                            if (ObjectUtil.isEmpty(orderPriorityNode)){
                                throw new ServiceException("节点数据异常！");
                            }
                            finishHoursBetween(orderPriorityNode);
                            orderPriorityNode.setPriorityStatus(WorkOrderProcessStatusEnums.HAVEEXPECTED.getCode());
                            workOrderPriorityNodeService.updateById(orderPriorityNode);
                            operatioAlteration(priorityNodeId);
                            Long workOrderId = orderPriorityNode.getWorkOrderId();
                            WorkOrder workOrder = workOrderService.getById(workOrderId);

                            // 逾期节点状态更改
                            workOrderService.update(null,new LambdaUpdateWrapper<WorkOrder>()
                                    .eq(WorkOrder::getId, workOrder.getId())
                                .set(WorkOrder::getOverdueNum, workOrder.getOverdueNum() + 1)
                                .set(WorkOrder::getProcessStatus, WorkOrderProcessStatusEnums.HAVEEXPECTED.getCode())
                                .set(WorkOrder::getActFinishHours,workOrder.getLimitFinishHours())
                                .set(WorkOrder::getActFinishTime,new Date())
                                .set(WorkOrder::getAcceptorStr,workOrder.getCreateByName())
                                .set(WorkOrder::getAcceptorNumber,null)
                                .set(WorkOrder::getAcceptedNodeName,null)
                            );
                            // 通知 短信 / 模板消息
                            WorkOrder newWorkOrder = workOrderService.getById(workOrderId);
                            workOrderRoleCmpUtil.startEditRole(workOrder,newWorkOrder);

                            logger.info("逾期节点【{}】，succeed succeed succeed",priorityNodeId);
                        }
                    }
                    count = 0;
                } catch (InterruptedException ignore) {
                    logger.error("任务超时处理任务已被中断", ignore);
                } catch (Exception e) {
                    try {
                        if (count <= 500) {
                            count += 10;
                        }
                        Thread.sleep(count * 10);
                    } catch (InterruptedException ex) {
                        Thread.currentThread().interrupt();
                    }
                    logger.error("任务超时处理任务出现异常，Go to sleep : {}", count * 10, e);
                }
            }
        });
    }

    public void stop() {
        running = false;
    }

    private LocalDateTime finishHoursBetween(WorkOrderPriorityNode orderPriorityNode){
        Date startTime = orderPriorityNode.getStartTime();
        Instant instant = startTime.toInstant();
        LocalDateTime startDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        LocalDateTime endDateTime = LocalDateTime.now();
        Duration duration = Duration.between(startDateTime, endDateTime);
        Long hours = duration.toHours();
        Long minutes = duration.toMinutes() % 60;
        String fromHour = StrUtil.format("{}.{}",hours,minutes);
        orderPriorityNode.setActFinishTime(new Date());
        orderPriorityNode.setActFinishHours(Double.valueOf(fromHour));
        return startDateTime;
    }

    private boolean operatioAlteration(Long priorityNodeId){

        List<WorkOrderOperation> workOrderOperations = workOrderOperationService.getBaseMapper()
            .selectList(new LambdaQueryWrapper<WorkOrderOperation>()
                .eq(WorkOrderOperation::getWorkOrderPriorityNodeId, priorityNodeId)
                .in(WorkOrderOperation::getPriorityStatus,WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode(),
                    WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode() )
                .eq(WorkOrderOperation::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            );

        if (CollUtil.isNotEmpty(workOrderOperations)){
            for (WorkOrderOperation orderOperation : workOrderOperations) {
                orderOperation.setOperateType( WorkOrderOperatorTypeEnums.EXPECTED_AUTO_MATICFLO_WORDER.getCode());
                orderOperation.setPriorityStatus(WorkOrderProcessStatusEnums.HAVEEXPECTED.getCode());
                orderOperation.setUpdateTime(new Date());
                orderOperation.setOperateTime(new Date());

                if (ObjectUtil.isNotEmpty(orderOperation.getPriorityerId())){
                    orderOperation.setOperatorId(orderOperation.getPriorityerId());
                    orderOperation.setOperatorName(orderOperation.getPriorityerName());


                    List<com.cxrry.biz.customer.support.domain.CxrEmployeePost> employeePosts=
                        workOrderService.getByUserId(orderOperation.getPriorityerId());

                    if (CollUtil.isNotEmpty(employeePosts)){
                        Optional<CxrEmployeePost> employeePost = employeePosts.stream()
                            .max(Comparator.comparingInt(a -> Integer.valueOf(a.getCxrPostId().getValue())));

                        List<SysUserPost> userPosts=sysDeptMapper.getUserPosts(orderOperation.getPriorityerId());
                        if (CollUtil.isNotEmpty(userPosts)){
                            Optional<SysUserPost> userPost = userPosts.stream()
                                .max(Comparator.comparingInt(a -> Integer.valueOf(a.getPostId().intValue())));
                            orderOperation.setOperatorMaxJobName(
                                userPost.get().getPostId()>Long.valueOf(employeePost.get().getCxrPostId().getValue())?
                                    userPost.get().getPostAliasName():
                                    employeePost.get().getCxrPostId().getName());
                        }else{
                            orderOperation.setOperatorMaxJobName(employeePost.get().getCxrPostId().getName());
                        }
                    }
                }
            }
            return  workOrderOperationService.updateBatchById(workOrderOperations);
        }
        return true;
    }

    private boolean addOperation(Long workerId,Long operateId,String operatorName,Long priorityNodeId){
        WorkOrderOperation workOrderOperation = new WorkOrderOperation();
        workOrderOperation.setWorkOrderId(workerId);
        workOrderOperation.setOperatorId(operateId);
        workOrderOperation.setOperatorName(operatorName);
        workOrderOperation.setOperateTime(new Date());
        workOrderOperation.setOperateType(WorkOrderOperatorTypeEnums.EXPECTED_AUTO_MATICFLO_WORDER.getCode());
        workOrderOperation.setPriorityStatus(WorkOrderProcessStatusEnums.FINISHED.getCode());
        workOrderOperation.setFileUrl(null);
        workOrderOperation.setRelyNotes("逾期流转");
        workOrderOperation.setWorkOrderPriorityNodeId(priorityNodeId);
        workOrderOperation.setUpdateByName(operatorName);
        workOrderOperation.setUpdateTime(new Date());
        workOrderOperation.setCreateTime(new Date());
        workOrderOperation.setCreateByName(operatorName);
        workOrderOperation.setCreateBy(operateId);
        workOrderOperation.setDeleteStatus(DeleteStatus.not_deleted);
       return workOrderOperationService.save(workOrderOperation);
    }

}
