package com.cxrry.biz.common.json;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>Description: 客户地址配送信息</p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/19 11:58
 **/
@ApiModel
@Data
public class CustomerDistributioninfoDTO implements Serializable {

    @ApiModelProperty(value = "上午起送开启状态")
    public Boolean amEnable = Boolean.FALSE;
    @ApiModelProperty(value = "上午起送时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public LocalDate amSendDate;
    @ApiModelProperty(value = "上午配送鲜奶详情")
    public List<MilkDistributionDTO> amMilkDistributionList;
    @ApiModelProperty(value = "下午起送开启状态")
    public Boolean pmEnable = Boolean.FALSE;
    @ApiModelProperty(value = "下午起送时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public LocalDate pmSendDate;
    @ApiModelProperty(value = "下午配送鲜奶详情")
    public List<MilkDistributionDTO> pmMilkDistributionList;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;
    @ApiModelProperty(value = "区域id")
    private Long sysAreaId;


    /**
     * 默认账户地址 Y 是 N否
     */
    private String defalutAccountAddress;

    @ApiModelProperty(value = "小区id")
    private Long residentialQuartersId;
    @ApiModelProperty(value = "小区名称")
    private String residentialQuartersName;
    @ApiModelProperty(value = "详细地址")
    private String address;
    @ApiModelProperty(value = "配送站点id")
    private Long siteId;
    @ApiModelProperty(value = "配送站点名称")
    private String siteName;
    @ApiModelProperty(value = "配送员id")
    private Long distributionId;
    @ApiModelProperty(value = "配送员名字")
    private String distributionName;
    @ApiModelProperty(value = "上午停奶开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate amStopStartDate;
    @ApiModelProperty(value = "上午停奶结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate amStopEndDate;
    @ApiModelProperty(value = "下午停奶开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate pmStopStartDate;

    @ApiModelProperty(value = "下午停奶结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate pmStopEndDate;

    @ApiModelProperty("省")
    private String province;

    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;

    /**
     * 区
     */
    @ApiModelProperty("区")
    private String area;

    private List<CheckboxList> checkboxList;

    private Long addressId;

    @ApiModelProperty("修改历史")
    private AlterRecord alterRecord;
    @ApiModelProperty("上午排奶修改历史")
    private List<MilkDistributionDTO> alterAmTaste;
    @ApiModelProperty("下午派奶修改历史")
    private List<MilkDistributionDTO> alterPmTaste;


    @Data
    public static class CheckboxList implements Serializable {

        private String name;
        private Boolean checked;
        private Boolean disabled;
    }

}


