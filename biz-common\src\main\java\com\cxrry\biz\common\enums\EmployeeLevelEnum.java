package com.cxrry.biz.common.enums;

public enum EmployeeLevelEnum {
    L1(1),
    L2(2),
    L3(3),
    L4(4),
    L5(5),
    L6(6),
    L7(7);

    private final int level;

    EmployeeLevelEnum(int level) {
        this.level = level;
    }

    public int getLevel() {
        return level;
    }

    // 根据整数值获取枚举对象的方法
    public static EmployeeLevelEnum fromLevel(int level) {
        for (EmployeeLevelEnum l : values()) {
            if (l.getLevel() == level) {
                return l;
            }
        }
        throw new IllegalArgumentException("No Level with level: " + level);
    }
}