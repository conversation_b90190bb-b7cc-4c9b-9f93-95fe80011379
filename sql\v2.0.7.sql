
ALTER table cxr_employee_divide_confirm_limit
add
  `table_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '报表类型：0 确定分成 1确定银行卡信息 ';



ALTER table cxr_employee_divide add
    `account_information_confirm_name` varchar(28) DEFAULT NULL COMMENT '账号信息确认人';
ALTER table cxr_employee_divide add
    `account_information_confirm_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '账户信息确认状态：0.未确认，1.已确认';
ALTER table cxr_employee_divide add
    `account_information_time` datetime DEFAULT NULL COMMENT '账号信息确认时间';
ALTER table cxr_employee_divide add
    `account_information_issue` varchar(400) DEFAULT NULL COMMENT '账号信息反馈问题';



CREATE TABLE `cxr_divide_ratio_evaluation_record`(
                                                     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                     `one_stage_start_time` date DEFAULT NULL COMMENT 'L1晋升L2考核周期开始时间',
                                                     `one_stage_end_time` date DEFAULT NULL COMMENT 'L1晋升L2考核周期结束时间',
                                                     `two_stage_start_time` date DEFAULT NULL COMMENT 'L2考核开始日期',
                                                     `two_stage_end_time` date DEFAULT NULL COMMENT 'L2考核30天到期日期',
                                                     `pass_after_start_time` date DEFAULT NULL COMMENT '过小承包30天后开始时间',
                                                     `pass_after_end_time` date DEFAULT NULL COMMENT '过小承包30天后结束时间',
                                                     `not_pass_after_start_time` date DEFAULT NULL COMMENT '没有通过小承包30天后开始时间',
                                                     `not_pass_after_end_time` date DEFAULT NULL COMMENT '没有通过小承包30天后结束时间',
                                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1938432830563921923 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='业绩比例分成考核记录';

CREATE TABLE `cxr_employee_divide_month_last_record`(
                                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                        `employee_id` bigint DEFAULT NULL COMMENT '销售代理id',
                                                        `one_stage_start_time` date DEFAULT NULL COMMENT 'L1晋升L2考核周期开始时间',
                                                        `one_stage_end_time` date DEFAULT NULL COMMENT 'L1晋升L2考核周期结束时间',
                                                        `two_stage_start_time` date DEFAULT NULL COMMENT 'L2考核开始日期',
                                                        `two_stage_end_time` date DEFAULT NULL COMMENT 'L2考核30天到期日期',
                                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1938432830563921923 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='月底实发分成记录';



ALTER table  cxr_employee_divide  add `up_actual_divide_month_imp` char(7) DEFAULT NULL COMMENT '上月负分成月份';


-- nacos
-- depositBankCardName: "开户姓名"
-- depositBankCardPhone: "开户手机号"
-- accountInformationConfirmStatusStr: "账户信息确认状态"
-- accountInformationConfirmName: "账户信息确认人"
-- accountInformationTime: "账户信息确认时间"
-- accountInformationIssue: "账号信息反馈问题"