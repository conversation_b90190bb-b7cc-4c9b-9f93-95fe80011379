package com.cxrry.biz.customer.support.domain.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class SysUserVosPlus implements Serializable {


    private Long userId;

    /**
     * 公司ID
     */

    private Long deptId;

    /**
     * 部门ID
     */

    private Long actDeptId;

    /**
     * 用户账号
     */

    private String userName;

    /**
     * 用户昵称
     */

    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */

    private String userType;


    private String email;


    private String phonenumber;

    /**
     * 用户性别
     */

    private String sex;

    /**
     * 用户头像
     */

    private String avatar;

    /**
     * 密码
     */

    private String password;
    /**
     * 帐号状态（0正常 1停用）
     */

    private String status;
    /**
     * 删除标志（0代表存在 2代表删除）
     */

    private String delFlag;
    /**
     * 最后登录IP
     */

    private String loginIp;
    /**
     * 最后登录时间
     */

    private Date loginDate;
    /**
     * 备注
     */

    private String remark;


    private Long employeeId;

    /**
     * 创建人类型(详情见字典)
     */
    private String createByType;

    /**
     * 创建人类型(详情见字典)
     */
    private String createBy;

}