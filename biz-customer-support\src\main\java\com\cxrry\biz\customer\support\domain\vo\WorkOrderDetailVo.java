package com.cxrry.biz.customer.support.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.biz.customer.support.domain.dto.WorkOrderCxrAcceptorDto;
import com.cxrry.biz.customer.support.domain.dto.WorkOrderCxrOperationDto;
import com.cxrry.biz.customer.support.domain.dto.WorkOrderOperationDto;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderDetailVo extends WorkOrderBaseEntity{
  @ApiModelProperty(value = "工单编号")
  private String code;

  /**
   * 工单分类 1.投诉工单 2.退款工单
   */
  private Integer type;
  /**
   * 创建人名称
   */
  private String createByName;
  @ApiModelProperty(value = "工单创建时间")
  private Date createTime;
  @ApiModelProperty(value = "优先级 1低 2一般 3紧急 4非常紧急")
  private Integer priority;
  @ApiModelProperty(value = "投诉类型名称")
  private String orderMenuRemark;
  @ApiModelProperty(value = "逾期次数")
  private Integer overdueNum;
  @ApiModelProperty(value = "开启次数")
  private Integer openNum;
  @ApiModelProperty(value = "关注人")
  List<WorkOrderFollower> workOrderFollowerList;

  @ApiModelProperty(value = "受理人")
  private String acceptorStr;

  @ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
  private Integer processStatus;

  @ApiModelProperty(value = "工单内容")
  private String describetion;
  @TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
  private List<FileUrlConfig> fileUrl;

  @ApiModelProperty(value = "客户名称")
  private String customerName;

  @ApiModelProperty(value = "客户主手机号")
  private String customerMainPhone;

  /**
   * 客户登录手机号
   */
  private String customerLoginPhone;

  @ApiModelProperty(value = "客户地址")
  private String customerAddress;

  @ApiModelProperty(value = "本单要求完成时间")
  private Date limitFinishTime;

  @ApiModelProperty(value = "最新节点要求结束时间")
  private Date lastNodeLimitFinishTime;


  /**
   * 受理站点名称
   */
  private String siteName;

  /**
   * 受理区域名称
   */
  private String regionName;

  /**
   * 站点主管名称
   */
  private String siteDirectorUserName;


  @ApiModelProperty(value = "工单服务记录")
  private List<WorkOrderCxrOperationDto> operationDtoList;

  @ApiModelProperty(value = "受理人记录")
  private List<WorkOrderCxrAcceptorDto>cxrAcceptorDtoList;

  @ApiModelProperty(value = "1.自动分配 2.指定人员")
  private Integer acceptedFlag;

  @ApiModelProperty(value = "工单本级节点id")
  private Long priorityNodeId;

  @ApiModelProperty(value = "受理人")
  private List<Long> priorityerIds;

  @ApiModelProperty(value = "受理人")
  private List<String> priorityerNames;

  private String complaintObjectName;
  @TableField(typeHandler = JsonTypeHandlerConstant.OperatorMessageHandler.class)
  private List<OperatorMessageConfig> operatorMessage;

  @ApiModelProperty(value = "配送员")
  private String employeeName;

  @ApiModelProperty(value = "客户地址id")
  private Long customerAddressId;
}
