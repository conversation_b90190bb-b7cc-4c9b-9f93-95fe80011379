package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@ApiModel
@Data
@Accessors(chain = true)
public class GoodsInfo implements Serializable {

    /**
     * 商品id
     */
    @ApiModelProperty("商品id")
    private Long productId;

    /**
     * 商品名称
     */
    @ApiModelProperty("商品名称")
    private String productName;

    /**
     * 商品别名
     */
    @ApiModelProperty("商品别名")
    private String productAlias;
    /**
     * 商品数量
     */
    @ApiModelProperty("商品数量")
    private Long quantity = 0L;
}
