package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 * 工单按钮权限表
 * @TableName work_order_button_permission
 */
@TableName(value ="work_order_button_permission")
@Data
public class WorkOrderButtonPermission implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 工单表ID
     */
    private Long workOrderId;

    /**
     * 工单流程节点ID
     */
    private Long workOrderPriorityNodeId;

    /**
     * 按钮类型 1回复 2转交 3完结
     */
    private Integer buttonType;

    /**
     * 是否限制 0限制 1不限制
     */
    private Integer isLimit;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 职位ID
     */
    private Long postId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * ID对应的名称
     */
    private String idName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}