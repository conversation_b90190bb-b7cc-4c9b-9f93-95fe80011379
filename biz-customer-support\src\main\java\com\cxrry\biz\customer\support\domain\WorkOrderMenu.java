package com.cxrry.biz.customer.support.domain;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @TableName work_order_menu
 */
@Data
public class WorkOrderMenu implements Serializable {

  /**
   * 工单列表id
   */
  @NotNull(message = "[工单列表id]不能为空")
  @ApiModelProperty("工单列表id")
  private Long id;
  /**
   * 父级id
   */
  @ApiModelProperty("父级id")
  private Long parentId;

  @ApiModelProperty("组级列表")
  private String ancestors;
  /**
   * 菜单名
   */
  @ApiModelProperty("菜单名")
  private String menuName;

  @ApiModelProperty("菜单排序")
  private Integer sort;


  /**
   * 菜单顺序
   */
  @ApiModelProperty("菜单顺序")
  private Integer menuNum;

  private Long createBy;
  private String createByName;

  private Date createTime;

  private Long updateBy;
  private String updateByName;
  private Date updateTime;
  private Long deleteBy;

  private String deleteByName;
  private Date deleteTime;
  /**
   * 0未删除 2已删除
   */
  @ApiModelProperty("0未删除 2已删除")
  private Integer deleteStatus;

}
