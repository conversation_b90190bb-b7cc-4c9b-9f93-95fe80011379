package com.cxrry.biz.customer.support.controller.staff;

import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderReportTotalPageBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderReportTotalPageVo;
import com.cxrry.biz.customer.support.service.StaffWorkOrderReportTotalService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
        value = "工单统计报表相关",
        tags = {"工单统计报表相关"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/staff/workOrderReportTotal")
public class StaffWorkOrderReportTotalController {

    private final StaffWorkOrderReportTotalService staffWorkOrderReportTotalService;



    @ApiOperation("分页")
    @PostMapping("/groupPage")
    public R<PageTableDataInfo<WorkOrderReportTotalPageVo>> groupPage(@RequestBody WorkOrderReportTotalPageBo bo) {
        return R.ok(staffWorkOrderReportTotalService.groupPage(bo));
    }



}
