package com.cxrry.biz.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysUserPostVo implements Serializable {
    /**
     * 销售代理ID
     */
    @ApiModelProperty(value = "销售代理ID")
    private Long employeeId;
    /**
     * 职务ID
     */
    @ApiModelProperty(value = "职务ID")
    private Long postId;
    /**
     * 岗位编码
     */
    @ApiModelProperty(value = "岗位编码")
    private String postCode;
    /**
     * 职务名称
     */
    @ApiModelProperty(value = "职务名称")
    private String postName;
}
