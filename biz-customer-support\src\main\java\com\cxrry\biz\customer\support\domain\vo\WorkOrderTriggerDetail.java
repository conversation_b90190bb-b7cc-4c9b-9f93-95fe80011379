package com.cxrry.biz.customer.support.domain.vo;

import com.cxrry.biz.customer.support.domain.dto.TriggerActiveDto;
import com.cxrry.biz.customer.support.domain.dto.TriggerConditionDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkOrderTriggerDetail implements Serializable {

    private Long id;

    private Long triggerId;
    private String triggerEvent;
    /**
     * 判断类型1工单创建2工单修改
     */
    private Integer judgmentType;

    //触发条件
    private List<TriggerConditionDto> triggerConditions;

    private List<TriggerActiveDto> triggerActiveDtos;
    // 前置条件
    private List<TriggerConditionDto> triggerScopeCondition;


}
