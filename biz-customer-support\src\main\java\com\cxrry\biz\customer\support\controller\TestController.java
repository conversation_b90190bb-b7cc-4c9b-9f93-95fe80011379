package com.cxrry.biz.customer.support.controller;

import cn.hutool.json.JSONUtil;
import com.cxrry.biz.customer.support.cmp.TestCmp;
import com.cxrry.biz.customer.support.domain.bo.TaskParameterBo;
import com.cxrry.biz.customer.support.enums.workorder.TaskOperationEnums;
import com.cxrry.biz.customer.support.execute.TaskTimeoutHelper;
import com.cxrry.biz.customer.support.utils.WorkOrderRoleCmpUtil;
import com.ruoyi.common.core.domain.R;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/test")
public class TestController {

    private final TestCmp testCmp;

    private final WorkOrderRoleCmpUtil workOrderCmpUtil;
    private final  TaskTimeoutHelper taskTimeoutHelper;

    @GetMapping("/lf")
    public R<String> test(String param) {
        testCmp.test(param);
        return R.ok("test");
    }

    @GetMapping("/lf2")
    public R<String> test2(String param) {
//        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
//        stringObjectHashMap.put("type", "1");
//        workOrderCmpUtil.startAddRole(stringObjectHashMap);
        return R.ok("test");
    }

    @GetMapping("/taskTimeout")
    public R<Void> taskTimeout(String endDateTime,Long nodeId,String operationType) {

        LocalDateTime localDateTime = LocalDateTime.parse(endDateTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        LocalDateTime startDateTime = LocalDateTime.now();

        TaskParameterBo nextTask=new TaskParameterBo();
        nextTask.setPriorityNodeId(nodeId);  // 下一个节点  新增传第一个节点
        nextTask.setOperationType(operationType);

        long startMilli = startDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        long endMilli = localDateTime.toInstant(ZoneOffset.ofHours(8)).toEpochMilli();
        taskTimeoutHelper.setTimeout(JSONUtil.toJsonStr(nextTask), endMilli - startMilli,
            startMilli);
        return null;
    }
}
