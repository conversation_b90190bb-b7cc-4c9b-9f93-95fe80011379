package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/19 12:00
 **/
@ApiModel
@Data
public class MilkDistributionDTO implements Serializable {

    @ApiModelProperty(value = "商品id")
    private Long productId;
    @ApiModelProperty(value = "商品名称")
    private String productName;

    @ApiModelProperty(value = "星期一")
    private Long mondayQuantity = 0L;

    @ApiModelProperty(value = "星期二")
    private Long tuesdayQuantity = 0L;

    @ApiModelProperty(value = "星期三")
    private Long wednesdayQuantity = 0L;

    @ApiModelProperty(value = "星期四")
    private Long thursdayQuantity = 0L;

    @ApiModelProperty(value = "星期五")
    private Long fridayQuantity = 0L;

    @ApiModelProperty(value = "星期六")
    private Long saturdayQuantity = 0L;

    /*修改历史所用*/
    @ApiModelProperty("是否改变")
    private boolean modc;
    private boolean tudc;
    private boolean thdc;
    private boolean sadc;
    private boolean wedc;
    private boolean frdc;
}
