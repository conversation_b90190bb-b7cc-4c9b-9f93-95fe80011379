package com.cxrry.biz.customer.support.domain.json;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("工单节点按钮权限")
public class WorkOrderButtonConfig implements Serializable {

  @ApiModelProperty("按钮权限")
  private String buttonName;
  @ApiModelProperty("1.开 0.关")
  private Integer status;
  @ApiModelProperty("执行动作 1.回复工单 2.转交工单 3.完结工单")
  private Integer operationName;
  @ApiModelProperty("操作权限:1.当前节点受理人 2.下一节点受理人 3.总部用户 4.所有系统用户")
  private List<Integer> operationPermission;//[1,2,3]

}
