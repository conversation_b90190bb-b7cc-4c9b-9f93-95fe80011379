package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/10/19 17:30
 **/
@ApiModel
@Data
public class SiteFreshMilkAssessmentTimeInfo implements Serializable {

    @ApiModelProperty(value = "是否选中")
    private Boolean checked;

    @ApiModelProperty(value = "子项")
    private List<SiteFreshMilkAssessmentTimeInfo> item;

}
