package com.cxrry.biz.sales.operation.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 月底实发分成记录
 * @TableName cxr_employee_divide_month_last_record
 */
@TableName(value ="cxr_employee_divide_month_last_record")
@Data
public class CxrEmployeeDivideMonthLastRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 销售代理id
     */
    private Long employeeId;

    /**
     * L1晋升L2考核周期开始时间
     */
    private Date oneStageStartTime;

    /**
     * L1晋升L2考核周期结束时间
     */
    private Date oneStageEndTime;

    /**
     * L2考核开始日期
     */
    private Date twoStageStartTime;

    /**
     * L2考核30天到期日期
     */
    private Date twoStageEndTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}