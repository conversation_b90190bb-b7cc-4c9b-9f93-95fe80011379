package com.cxrry.biz.common.rule;


import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;

@Data
public class RunRequest implements Serializable {

	/**
	 * 执行的规则编码
	 */
	private String ruleSetCode;

	/**
	 * 规则集ID
	 */
	private Long ruleSetId;


	/**
	 * 执行日期
	 * 用于可模拟执行日期
	 */
	private Date runDate = new Date();

	/**
	 * 输入参数
	 */
	private Input input;

	/**
	 * 限定的执行部门,同时满足
	 */
	@NotEmpty(message = "deptId不能为空")
	private List<Long> deptIds;

}
