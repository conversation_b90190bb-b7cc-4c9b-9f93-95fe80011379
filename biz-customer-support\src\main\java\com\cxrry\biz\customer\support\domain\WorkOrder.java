package com.cxrry.biz.customer.support.domain;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import com.cxrry.biz.customer.support.service.WorkOrderStrategy;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @TableName work_order
 */
@TableName(value = "work_order",autoResultMap = true)
@Data
public class WorkOrder implements Serializable, WorkOrderStrategy {

  /**
   *
   */
  @TableId
  private Long id;
  /**
   * 工单编号
   */
  private String code;

  /**
   * 工单分类 1.投诉工单 2.退款工单
   */
  private Integer type;

  /**
   * 投诉类型ID（分类ID）
   */
  private Long orderMenuId;

  /**
   * 投诉类型多级名称拼接字符串
   */
  private String orderMenuRemark;

  /**
   * 优先级 1低 2一般 3紧急 4非常紧急
   */
  private Integer priority;

  /**
   * 受理状态 0待受理 1受理中 2已完结 3.已逾期
   *
   * @see WorkOrderProcessStatusEnums
   */
  private Integer processStatus;

  /**
   * 最新待受理人用户名称，多个逗号分开
   */
  private String acceptorStr;

  /**
   * 最新待受理人用户编号，多个逗号分开
   */
  private String acceptorNumber;

  /**
   * 工单描述内容
   */
  private String describetion;

  /**
   * 工单附件相对路径url，多个用逗号隔开
   */
  @TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
  private List<FileUrlConfig> fileUrl;

  /**
   * 客户ID
   */
  private Long customerId;

  /**
   * 客户主地址手机号
   */
  private String customerMainPhone;

  /**
   * 客户登录手机号
   */
  private String customerLoginPhone;

  /**
   * 客户名称
   */
  private String customerName;

  /**
   * 客户地址
   */
  private String customerAddress;

  /**
   * 受理站点ID
   */
  private Long siteId;

  /**
   * 受理站点名称
   */
  private String siteName;
  /**
   * 受理站点代号
   */
  private String siteMark;

  /**
   * 受理区域ID
   */
  private Long regionId;

  /**
   * 受理区域名称
   */
  private String regionName;

  /**
   * 受理大区ID
   */
  private Long rootRegionId;

  /**
   * 受理大区名称
   */
  private String rootRegionName;

  /**
   * 站点主管用户ID
   */
  private Long siteDirectorUserId;

  /**
   * 站点主管名称
   */
  private String siteDirectorUserName;


  /**
   * 销售代理id
   */
  private Long employeeId;

  /**
   * 销售代理名称
   */
  private String employeeName;
  /**
   * 销售代理编号
   */
  private String jobNumber;

  /**
   * 本单要求处理时长/h 小时数
   */
  private Double limitFinishHours;

  /**
   * 本单要求完成时间
   */
  private Date limitFinishTime;

  /**
   * 本单实际完成时长/h
   */
  private Double actFinishHours;

  /**
   * 本单实际完成时间
   */
  private Date actFinishTime;

  /**
   * 创建人id
   */
  private Long createBy;

  /**
   * 创建人名称
   */
  private String createByName;

  /**
   * 创建人类型(详情见字典)
   */
  private String createByType;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 更新人id
   */
  private Long updateBy;

  /**
   * 更新人名称
   */
  private String updateByName;

  /**
   * 更新人类型(详情见字典)
   */
  private String updateByType;

  /**
   * 更新时间
   */
  private Date updateTime;

  /**
   * 删除人id
   */
  private Long deleteBy;

  /**
   * 删除人名称
   */
  private String deleteByName;

  /**
   * 删除人类型(详情见字典)
   */
  private String deleteByType;

  /**
   * 删除时间
   */
  private Date deleteTime;

  /**
   * 删除状态(详情见字典)
   */
  private String deleteStatus;

  @ApiModelProperty(value = "公司id")
  private Long currentDeptId;
  @ApiModelProperty(value = "公司名称")
  private String sysDeptName;
  @ApiModelProperty(value = "省")
  private String province;
  @ApiModelProperty(value = "市")
  private String city;
  @ApiModelProperty(value = "区")
  private String area;
  @ApiModelProperty(value = "完成人")
  private String finishName;
  @ApiModelProperty(value = "完成时间")
  private Date finishTime;
  @ApiModelProperty(value = "1.自动分配 2.指定人员")
  private Integer acceptedFlag;
  @ApiModelProperty(value = "逾期次数")
  private Integer overdueNum;
  @ApiModelProperty(value = "开启次数")
  private Integer openNum;
  @ApiModelProperty(value = "1.销售代理 2.总部用户")
  private Integer acceptedAccount;
  @ApiModelProperty(value = "当前受理节点名称")
  private String acceptedNodeName;
  @ApiModelProperty(value = "投诉对象id")
  private Long complaintObjectId;
  @ApiModelProperty(value = "投诉对象名称")
  private String complaintObjectName;

  private String complaintObjectNumber; // 投诉对象的编号

  private String complaintObjectSiteName; // 投诉对象站点名称

  private String complaintObjectSiteMark; // 投诉对象站点编号

  private Long complaintObjectSiteId; // 投诉对象站点id

  private String complaintObjectDeptName; // 投诉对象公司名

  private Long complaintObjectDeptId; // 投诉对象公司id

  private Long complaintObjectRegionId; // 投诉对象区域id

  private String complaintObjectRegionName; // 投诉对象区域名称
  private Long complaintObjectRootRegionId; // 投诉对象大区id

  private String complaintObjectRootRegionName; // 投诉对象大区名称

  private String complaintObjectProvince; // 投诉对象省

  private String complaintObjectCity; // 投诉对象市
  private String complaintObjectArea; // 投诉对象区


  @TableField(exist = false)
  private static final long serialVersionUID = 1L;


  @Override
  public Long getWorkOrderId() {
    return id;
  }

  @Override
  public Long getCategoryId() {
    return orderMenuId;
  }

  @Override
  public Integer getStatus() {
    return processStatus;
  }

  @Override
  public String getAcceptor() {
    return acceptorStr;
  }

  @TableField(typeHandler = JsonTypeHandlerConstant.OperatorMessageHandler.class)
  private List<OperatorMessageConfig> operatorMessage;

  @ApiModelProperty(value = "客户地址id")
  private Long customerAddressId;

}