package com.cxrry.biz.customer.support.enums.workordertrigger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 触发器前置判断条件类型枚举
 */
@AllArgsConstructor
@Getter
public enum WorkOrderTriggerConditionPreconditionJudg {
    IS(1, "是"),
    IS_NOT(2, "不是"),
    CONTAINS_ANY(3, "包含任意"),
    DOESNT_CONTAIN_ANY(4, "不包含任意");

    private final Integer code;
    private final String description;
}
