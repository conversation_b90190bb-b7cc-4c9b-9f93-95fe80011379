package com.cxrry.biz.sales.operation.manager.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cxrry.biz.common.mq.MqSaleAccessCycelConst;
import com.cxrry.biz.common.utils.DateUtils;
import com.cxrry.biz.common.utils.MqUtil;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecord;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordLog;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeLeave;
import com.cxrry.biz.sales.operation.domain.SalesAccessLogHistory;
import com.cxrry.biz.sales.operation.domain.dto.EmpLeaveChangeDTO;
import com.cxrry.biz.sales.operation.domain.dto.RunAccessCycleTaskDTO;
import com.cxrry.biz.sales.operation.enums.cycle.AdjustSourceTypeEnum;
import com.cxrry.biz.sales.operation.enums.leave.LeaveApprovalStatus;
import com.cxrry.biz.sales.operation.manager.EmpLeaveChangeManage;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeLeaveMapper;
import com.cxrry.biz.sales.operation.mapper.SalesAccessLogHistoryMapper;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordLogService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordService;
import com.cxrry.biz.sales.operation.service.SalesAccessLogHistoryService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmpLeaveChangeManageImpl implements EmpLeaveChangeManage {

	private final CxrEmployeeLeaveMapper cxrEmployeeLeaveMapper;
	private final SalesAccessLogHistoryMapper salesAccessLogHistoryMapper;
	private final SalesAccessLogHistoryService salesAccessLogHistoryService;
	private final CxrEmployeeAccessCycleRecordService cxrEmployeeAccessCycleRecordService;
	private final CxrEmployeeAccessCycleRecordLogService cxrEmployeeAccessCycleRecordLogService;
	private final MqUtil mqUtil;
	@Override
	@DSTransactional
	public void empLeaveChange(EmpLeaveChangeDTO dto) {
		CxrEmployeeLeave cxrEmployeeLeave = cxrEmployeeLeaveMapper.selectById(dto.getLeaveId());
		if (!cxrEmployeeLeave.getApprovalStatus().equals(LeaveApprovalStatus.APPROV.getValue())){
			log.info("员工请假变更失败，请假单状态不正确，请假单id：{}，请假单状态：{}", dto.getLeaveId(), cxrEmployeeLeave.getApprovalStatus());
			return;
		}
		if(!cxrEmployeeLeave.getDelayFlag()){
			log.info("员工请假变更失败，请假单非延期状态，请假单id：{}", dto.getLeaveId());
			return;
		}

		Date actStartTime = DateUtils.convertLocalDateToDate(cxrEmployeeLeave.getActStartTime());
		Date actEndTime = DateUtils.convertLocalDateToDate(cxrEmployeeLeave.getActEndTime());
		//1.先回退，并保存回退记录
		List<Long> rerunLogIds = new ArrayList<>();
		List<CxrEmployeeAccessCycleRecord> cxrEmployeeAccessCycleRecords = cxrEmployeeAccessCycleRecordService.rerunActive(
				cxrEmployeeLeave.getEmployeeId(),actStartTime,
				StrUtil.format("个人请假延期{}-{}", cxrEmployeeLeave.getActStartTime(),
						cxrEmployeeLeave.getActEndTime()),rerunLogIds);
		//2.延期，产生延期记录，关联回退记录
		List<CxrEmployeeAccessCycleRecord> inProgressList = cxrEmployeeAccessCycleRecordService.getInProgress(
				cxrEmployeeLeave.getEmployeeId());
		if (ObjectUtil.isEmpty(inProgressList)){
			log.error("员工：{} 没有进行中的周期,请假单ID{}",cxrEmployeeLeave.getEmployeeId(),cxrEmployeeLeave.getId());
			return;
		}

		cxrEmployeeAccessCycleRecordLogService.remove(new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecordLog>()
				.eq(CxrEmployeeAccessCycleRecordLog::getSourceId, dto.getLeaveId()));
//		int days = (int) DateUtil.between(DateUtils.convertLocalDateToDate(cxrEmployeeLeave.getActStartTime()),
//				DateUtils.convertLocalDateToDate(cxrEmployeeLeave.getActEndTime()),
//				DateUnit.DAY
//				,false) + 1;
//		if(days > 0){
//
//		}
		List<CxrEmployeeAccessCycleRecordLog> cxrEmployeeAccessCycleRecordLogs =
				cxrEmployeeAccessCycleRecordService.adjustCycleByDays(AdjustSourceTypeEnum.PERSONAL_LEAVE,dto.getLeaveId(),
				inProgressList, actStartTime,
				actEndTime, StrUtil.format("个人请假延期{}-{}", cxrEmployeeLeave.getActStartTime(),
						cxrEmployeeLeave.getActEndTime()) + ";按月的周期不变动");
		//保存关联记录
		if(!rerunLogIds.isEmpty() && !cxrEmployeeAccessCycleRecordLogs.isEmpty()){
			ArrayList<SalesAccessLogHistory> salesAccessLogHistorieAddList = this.buildSalesAccessLogHistories(
					cxrEmployeeAccessCycleRecordLogs, rerunLogIds);
			salesAccessLogHistoryService.saveBatch(salesAccessLogHistorieAddList);
		}
		//3.重跑
		if(cxrEmployeeAccessCycleRecords.isEmpty()){
			log.info("没有可重跑的记录");
			return;
		}
		//从最早的日期重跑,取最早的日期
		Date minStartDate =
				cxrEmployeeAccessCycleRecords.stream().map(CxrEmployeeAccessCycleRecord::getActualAssessmentStartDate).min(Date::compareTo).get();
		//奔跑吧，规则
		mqUtil.sendSyncMessage(MqSaleAccessCycelConst.SALE_ACCESS_CYCLE_TOPIC,
				MqSaleAccessCycelConst.SALE_RE_RUN_TASK_TAG,
				JSONUtil.toJsonStr( RunAccessCycleTaskDTO.builder()
						.employeeId(Arrays.asList(cxrEmployeeLeave.getEmployeeId())).rerunStartDate(minStartDate).build()));
	}

	@NotNull
	private ArrayList<SalesAccessLogHistory> buildSalesAccessLogHistories(
			List<CxrEmployeeAccessCycleRecordLog> cxrEmployeeAccessCycleRecordLogs, List<Long> rerunLogIds) {
		ArrayList<SalesAccessLogHistory> salesAccessLogHistorieAddList = new ArrayList<>();
		for (CxrEmployeeAccessCycleRecordLog cxrEmployeeAccessCycleRecordLog : cxrEmployeeAccessCycleRecordLogs) {
			for (Long rerunLogId : rerunLogIds) {
				SalesAccessLogHistory salesAccessLogHistory = new SalesAccessLogHistory();
				salesAccessLogHistory.setAccessLogId(cxrEmployeeAccessCycleRecordLog.getId());
				salesAccessLogHistory.setAccessId(rerunLogId);
				salesAccessLogHistorieAddList.add(salesAccessLogHistory);
			}
		}
		return salesAccessLogHistorieAddList;
	}
}
