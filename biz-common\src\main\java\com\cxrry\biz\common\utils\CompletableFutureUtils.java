package com.cxrry.biz.common.utils;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

public class CompletableFutureUtils {

    public static<T> void join(List<T> list, Consumer consumer) {
      CompletableFuture.allOf(list.stream().map(item -> CompletableFuture.runAsync(() -> consumer.accept(item))).toArray(CompletableFuture[]::new)).join();
    }
}
