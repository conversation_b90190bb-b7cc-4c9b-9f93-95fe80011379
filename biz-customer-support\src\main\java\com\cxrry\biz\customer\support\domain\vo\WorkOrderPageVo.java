package com.cxrry.biz.customer.support.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WorkOrderPageVo {
  private Long id;
  @ExcelProperty(value = "工单编号", index = 0)
  private String code;
  @ApiModelProperty(value = "公司名称")
  @ExcelProperty(value = "公司", index = 1)
  private String sysDeptName;
  @ApiModelProperty(value ="大区")
  @ExcelProperty(value = "大区", index = 2)
  private String rootRegionName;
  @ApiModelProperty(value ="区域名称")
  @ExcelProperty(value = "区域", index = 3)
  private String regionName;
  @ApiModelProperty(value = "省")
  @ExcelProperty(value = "省", index = 4)
  private String province;
  @ApiModelProperty(value = "市")
  @ExcelProperty(value = "市", index = 5)
  private String city;
  @ApiModelProperty(value = "区")
  @ExcelProperty(value = "区", index = 6)
  private String area;
  @ApiModelProperty(value ="站点名称")
  @ExcelProperty(value = "站点", index = 7)
  private String siteName;
  @ApiModelProperty(value ="站点编号")
  @ExcelProperty(value = "站点编号", index = 8)
  private String siteMark;
  @ApiModelProperty(value = "受理人节点名称")
  @ExcelProperty(value = "受理人节点名称", index = 9)
  private String acceptedNodeName;
  @ApiModelProperty(value ="最新待受理人用户名称，多个逗号分开")
  @ExcelProperty(value = "受理人", index = 10)
  private String acceptorStr;
  @ApiModelProperty(value ="最新待受理人用户编号，多个逗号分开")
  @ExcelProperty(value = "受理人编号", index = 11)
  private String acceptorNumber;
  @ApiModelProperty(value = "逾期次数")
  @ExcelProperty(value = "逾期次数", index = 12)
  private Integer overdueNum;
  @ApiModelProperty(value = "重启次数")
  @ExcelProperty(value = "重启次数", index = 13)
  private Integer openNum;
  @ApiModelProperty(value ="工单分类 1.投诉工单 2.退款工单")
  @ExcelProperty(value = "工单类型", index = 14)
  private Integer type;

  @ApiModelProperty(value = "投诉对象")
  @ExcelProperty(value = "投诉对象", index = 15)
  private String complaintObjectName;

  @ApiModelProperty(value = "分类")
  @ExcelProperty(value = "分类", index =16)
  private String orderMenuRemark;
  @ApiModelProperty(value = "内容")
  @ExcelProperty(value = "工单内容", index = 17)
  private String describetion;
  @ApiModelProperty(value ="客户手机号")
  @ExcelProperty(value = "客户手机号", index = 18)
  private String customerMainPhone;
  @ApiModelProperty(value ="客户登录手机号")
  @ExcelProperty(value = "客户登录账号", index = 19)
  private String customerLoginPhone;
  @ApiModelProperty(value ="客户名称")
  @ExcelProperty(value = "客户名称", index = 20)
  private String customerName;
  @ApiModelProperty(value = "客户地址")
  @ExcelProperty(value = "客户地址", index = 21)
  private String customerAddress;
  @ApiModelProperty(value ="创建人名称")
  @ExcelProperty(value = "创建人", index = 22)
  private String createByName;
  @ApiModelProperty(value = "工单完结人")
  @ExcelProperty(value = "工单完结人", index = 23)
  private String finishName;
  @ApiModelProperty(value = "创建时间")
  @ExcelProperty(value = "创建时间", index = 24)
  private Date createTime;
  @ApiModelProperty(value = "完结时间")
  @ExcelProperty(value = "完结时间", index = 25)
  private Date actFinishTime;
  @ApiModelProperty(value ="1低 2一般 3紧急 4非常紧急")
  @ExcelProperty(value = "优先级", index = 26)
  private Integer priority;
  @ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
  @ExcelProperty(value = "状态", index = 27)
  private Integer processStatus;
  @ApiModelProperty(value = "回复人")
  @ExcelProperty(value = "回复人", index = 28)
  private String operatorName;
  @ApiModelProperty(value = "回复时间")
  @ExcelProperty(value = "回复时间", index = 29)
  private Date operateTime;
  @ApiModelProperty(value = "回复内容")
  @ExcelProperty(value = "回复内容", index = 30)
  private String relyNotes;


}
