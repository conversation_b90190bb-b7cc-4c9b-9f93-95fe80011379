package com.cxrry.biz.customer.support.domain.vo;

import com.cxrry.biz.customer.support.domain.dto.WorkOrderOperationDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 配送端-工单列表视图
 *
 */
@Data
public class WorkOrderStaffListVo extends WorkOrderBaseEntity implements Serializable{

	@ApiModelProperty(value = "工单受理人表ID")
	private Long workOrderOperationId;

	@ApiModelProperty(value = "工单编号")
	private String code;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "工单创建时间")
	private Date createTime;

	@ApiModelProperty(value = "受理站点名称")
	private String siteName;

	@ApiModelProperty(value = "受理区域名称")
	private String regionName;

	@ApiModelProperty(value = "投诉类型名称")
	private String orderMenuRemark;

	@ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
	private Integer processStatus;

	@ApiModelProperty(value = "客户名称")
	private String customerName;

	@ApiModelProperty(value = "客户登录手机号")
	private String customerMainPhone;

	@ApiModelProperty(value = "客户地址")
	private String customerAddress;

	@ApiModelProperty(value = "最新节点要求结束时间")
	private Date lastNodeLimitFinishTime;

	@ApiModelProperty(value = "当前受理人")
	private String acceptorStr;

	@ApiModelProperty(value = "当前受理人id")
	private Long  acceptorId;

	@ApiModelProperty(value = "最新服务记录")
	private WorkOrderOperationDto lastOperationDto;

//	@ApiModelProperty(value = "最新回复人名称")
//	private String  lastReplyUserName;
//
//	@ApiModelProperty(value = "最新回复内容")
//	private String  lastReplyContent;
//
//	@ApiModelProperty(value = "最新回复内容附件地址")
//	private List<FileUrlConfig> lastReplyFileUrl;
//
//	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//	@ApiModelProperty(value = "最新回复内容时间")
//	private Date lastReplyTime;



}
