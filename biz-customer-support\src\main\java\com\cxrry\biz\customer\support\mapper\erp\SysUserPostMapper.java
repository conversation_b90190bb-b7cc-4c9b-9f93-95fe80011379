package com.cxrry.biz.customer.support.mapper.erp;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.cxrry.biz.customer.support.constants.IConst;
import com.cxrry.biz.customer.support.domain.SysUserPost;
import com.cxrry.biz.customer.support.domain.vo.CxrEmployeePostVo;
import com.ruoyi.common.mybatis.core.mapper.BaseMapperPlus;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户与岗位关联表 数据层
 *
 * <AUTHOR> Li
 */

@DS(value = IConst.DS_ERP)
@Mapper
public interface SysUserPostMapper
    extends BaseMapperPlus<SysUserPostMapper, SysUserPost, SysUserPost> {

  List<CxrEmployeePostVo> sysUserPostList(@Param("ids") List<Long> id,@Param("regionId") Long regionId);

  List<SysUserPost>queryPostByUserId(Long userId);

}
