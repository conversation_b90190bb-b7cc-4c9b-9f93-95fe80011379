package com.cxrry.biz.customer.support.domain.bo;

import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderComplaintTotalBo  extends PageQuery {
  @ApiModelProperty(value = "投诉对象公司id")
  private Long complaintObjectDeptId;
  @ApiModelProperty(value = "投诉对象大区名称")
  private String complaintObjectRootRegionName;
  @ApiModelProperty(value = "投诉对象区域名称")
  private String complaintObjectRegionName;
  @ApiModelProperty(value = "省")
  private String complaintObjectProvince;
  @ApiModelProperty(value = "市")
  private String complaintObjectCity;
  @ApiModelProperty(value = "区")
  private String complaintObjectArea;
  @ApiModelProperty(value = "站点名称")
  private String complaintObjectSiteName;
  @ApiModelProperty(value = "站点编号")
  private String complaintObjectSiteMark;
  @ApiModelProperty(value = "投诉对象id")
  private List<Long> complaintObjectIds;
  @ApiModelProperty(value = "投诉对象的编号")
  private String complaintObjectNumber;
  @ApiModelProperty(value = "分类id")
  private List<Long> orderMenuIds;
  @ApiModelProperty(value = "创建开始时间")
  private LocalDate createStartTime;
  @ApiModelProperty(value = "创建结束时间")
  private LocalDate createEndTime;

}
