package com.cxrry.biz.customer.support.dubbo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;

import com.cxrry.biz.customer.support.domain.CxrEmployee;
import com.cxrry.biz.customer.support.domain.WorkOrder;
import com.cxrry.biz.customer.support.domain.WorkOrderOperation;
import com.cxrry.biz.customer.support.domain.WorkOrderOperationDayStatistic;
import com.cxrry.biz.customer.support.domain.WorkOrderPriorityNode;
import com.cxrry.biz.customer.support.enums.workorder.AssigneeScopeEnums;

import com.cxrry.biz.customer.support.enums.workorder.WorkOrderProcessStatusEnums;
import com.cxrry.biz.customer.support.mapper.erp.CxrEmployeeMapper;
import com.cxrry.biz.customer.support.mapper.erp.CxrSiteMapper;
import com.cxrry.biz.customer.support.mapper.erp.SysDeptMapper;
import com.cxrry.biz.customer.support.service.WorkOrderOperationDayStatisticService;
import com.cxrry.biz.customer.support.service.WorkOrderOperationService;
import com.cxrry.biz.customer.support.service.WorkOrderPriorityNodeService;
import com.cxrry.biz.customer.support.service.WorkOrderService;
import com.ruoyi.business.base.api.domain.CxrSite;
import com.ruoyi.business.base.api.dubbo.RemoteWorkOrderService;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.system.api.RemoteDeptService;
import com.ruoyi.system.api.domain.SysDept;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteWorkOrderServiceImpl implements RemoteWorkOrderService {

  private final WorkOrderOperationService workOrderOperationService;

  private final WorkOrderPriorityNodeService workOrderPriorityNodeService;

  private final WorkOrderService workOrderService;

  private final WorkOrderOperationDayStatisticService statisticService;;

  @Autowired
  private CxrSiteMapper cxrSiteMapper;

  @Autowired
  private CxrEmployeeMapper cxrEmployeeMapper;

  @DubboReference
  private RemoteDeptService remoteDeptService;


  @Autowired
  private SysDeptMapper sysDeptMapper;

  @Override
  public Boolean checkUserMount(Long userId) {

    List<WorkOrderOperation> operations = workOrderOperationService.getBaseMapper()
        .selectList(new LambdaQueryWrapper<WorkOrderOperation>()
            .eq(WorkOrderOperation::getPriorityerId, userId)
            .eq(WorkOrderOperation::getDeleteStatus, DeleteStatus.not_deleted)
            .in(WorkOrderOperation::getPriorityStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
                WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode()
            )
        );

    if (CollUtil.isNotEmpty(operations)){
      List<Long> nodeIds = operations.stream().filter(x -> ObjectUtil.isNotEmpty(x.getWorkOrderPriorityNodeId()))
          .map(WorkOrderOperation::getWorkOrderPriorityNodeId).collect(
              Collectors.toList());
      if (CollUtil.isNotEmpty(nodeIds)){

        List<WorkOrderPriorityNode> priorityNodes = workOrderPriorityNodeService.getBaseMapper().selectList(new LambdaQueryWrapper<WorkOrderPriorityNode>()
            .select(WorkOrderPriorityNode::getId, WorkOrderPriorityNode::getWorkOrderId)
            .in(WorkOrderPriorityNode::getId, nodeIds)
            .eq(WorkOrderPriorityNode::getDeleteStatus, DeleteStatus.not_deleted)
            .eq(WorkOrderPriorityNode::getPriorityUserType, AssigneeScopeEnums.SPECIFIC_PERSON.getCode())
            .in(WorkOrderPriorityNode::getPriorityStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
                WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode())
        );
        if (CollUtil.isNotEmpty(priorityNodes)){
          Set<Long> workIds = priorityNodes.stream().map(WorkOrderPriorityNode::getWorkOrderId).collect(Collectors.toSet());
          return   workOrderService.getBaseMapper().selectCount(
              new LambdaQueryWrapper<WorkOrder>()
                  .select(WorkOrder::getId)
                  .in(WorkOrder::getId,workIds)
                  .in(WorkOrder::getProcessStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
                      WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode())
                  .eq(WorkOrder::getDeleteStatus,DeleteStatus.NOT_DELETED.getValue())
          )>0;
        }
      }
    }

    return false;
  }

  // 卸任
  @Override
  public Boolean workOrderRoam(Long userId, Long siteId, String postTypeValue) {
    return
        workOrderOperationService.update(null, new LambdaUpdateWrapper<WorkOrderOperation>()
        .eq(WorkOrderOperation::getPriorityerId,userId)
        .eq(WorkOrderOperation::getPriorityPostId,postTypeValue)
        .eq(WorkOrderOperation::getDeleteStatus, DeleteStatus.not_deleted)
        .in(WorkOrderOperation::getPriorityStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
            WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode()
        ).set(WorkOrderOperation::getDeleteStatus,DeleteStatus.deleted)
    );
  }

  // 上任  岗位类型
  @Override
  public Boolean workOrderAssumeOffice(Long userId, Long siteId, String postTypeValue,String name,String number) {
    List<WorkOrder> orders = workOrderService.getBaseMapper().selectList(new LambdaQueryWrapper<WorkOrder>()
        .eq(WorkOrder::getSiteId, siteId)
            .in(WorkOrder::getProcessStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
                WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode())
        .eq(WorkOrder::getDeleteStatus, DeleteStatus.not_deleted)
    );

    if (CollUtil.isNotEmpty(orders)){
      List<Long> orderIds = orders.stream().map(WorkOrder::getId).collect(Collectors.toList());

      List<WorkOrderPriorityNode> orderPriorityNodes = workOrderPriorityNodeService.getBaseMapper()
          .selectList(new LambdaQueryWrapper<WorkOrderPriorityNode>()
              .in(WorkOrderPriorityNode::getPriorityStatus, WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode(),
                  WorkOrderProcessStatusEnums.TOBEPROCESSED_CENTRE.getCode())
              .in(WorkOrderPriorityNode::getWorkOrderId, orderIds
              )
              .eq(WorkOrderPriorityNode::getDeleteStatus, DeleteStatus.not_deleted)
          );
      List<WorkOrderOperation> operationList=new ArrayList<>();
      for (WorkOrderPriorityNode priorityNode : orderPriorityNodes) {

        if (ObjectUtil.equals(priorityNode.getPriorityUserType(), AssigneeScopeEnums.SPECIFIC_PERSON.getCode())){
          WorkOrderOperation orderOperation = workOrderOperationService.getOne(new LambdaQueryWrapper<WorkOrderOperation>()
              .eq(WorkOrderOperation::getWorkOrderPriorityNodeId, priorityNode.getId())
              .eq(WorkOrderOperation::getPriorityPostId, postTypeValue)
          );
          if (ObjectUtil.isNotEmpty(orderOperation)){
            WorkOrderOperation newOperation =new WorkOrderOperation();
            BeanUtils.copyProperties(orderOperation,newOperation);
            newOperation.setId(null);
            newOperation.setPriorityerId(userId);
            newOperation.setPriorityerName(name);
            newOperation.setPriorityerNumber(number);
            CxrSite cxrSite = cxrSiteMapper.selectById(siteId);
            newOperation.setPriorityerSiteId(cxrSite.getId());
            newOperation.setPriorityerSiteMark(cxrSite.getSiteMark());
            newOperation.setPriorityerSiteName(cxrSite.getName());
            newOperation.setPriorityerDeptId(cxrSite.getCurrentDeptId());
            SysDept sysDept = remoteDeptService.queryById(cxrSite.getCurrentDeptId());
            newOperation.setPriorityerDeptName(sysDept.getDeptName());
            newOperation.setPriorityerRegionId(cxrSite.getCxrRootRegionId());
            newOperation.setPriorityerRegionName(cxrSite.getCxrRootRegionName());
            SysDept maxRootDept = sysDeptMapper.getMaxRootDept(siteId);
            newOperation.setPriorityerRootRegionId(maxRootDept.getDeptId());
            newOperation.setPriorityerRootRegionName(maxRootDept.getDeptName());
            newOperation.setPriorityerProvince(cxrSite.getProvice());
            newOperation.setPriorityerCity(cxrSite.getCity());
            newOperation.setPriorityerArea(cxrSite.getArea());

            newOperation.setOperateTime(null);
            newOperation.setOperatorId(null);
            newOperation.setOperatorName(null);
            operationList.add(newOperation);
          }
        }
      }
      if (CollUtil.isNotEmpty(operationList)){
        workOrderOperationService.saveBatch(operationList);
      }
    }
    return true;
  }

  @Override
  public Boolean WorkOrderStatisticJob(LocalDate now) {

    List<WorkOrderOperation> operationList = workOrderOperationService.getBaseMapper()
        .selectList(new LambdaQueryWrapper<WorkOrderOperation>()
            .select(WorkOrderOperation::getPriorityerId)
            .like(WorkOrderOperation::getCreateTime, now)
            .isNotNull(WorkOrderOperation::getPriorityerId)
            .eq(WorkOrderOperation::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
            .groupBy(WorkOrderOperation::getPriorityerId)
        );

    if (CollUtil.isNotEmpty(operationList)){
      List<Long> priorityers = operationList.stream().map(WorkOrderOperation::getPriorityerId).collect(Collectors.toList());
      List<WorkOrderOperation> orderOperations = workOrderOperationService.getBaseMapper()
          .selectList(new LambdaQueryWrapper<WorkOrderOperation>()
              .in(WorkOrderOperation::getPriorityerId, priorityers)
              .like(WorkOrderOperation::getCreateTime, now)
              .isNotNull(WorkOrderOperation::getPriorityerId)
              .eq(WorkOrderOperation::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
          );

      Map<Long, List<WorkOrderOperation>> orderOperationMap = orderOperations.stream()
          .collect(Collectors.groupingBy(WorkOrderOperation::getPriorityerId));
      List<WorkOrderOperationDayStatistic> dayStatistics = operationList.stream().map(x -> {
        WorkOrderOperationDayStatistic dayStatistic = new WorkOrderOperationDayStatistic();
        dayStatistic.setDistributionDate(now);
        Long priorityerId = x.getPriorityerId();
        CxrEmployee cxrEmployee = cxrEmployeeMapper.selectById(priorityerId);
        dayStatistic.setEmployeeId(x.getPriorityerId());
        dayStatistic.setEmployeeName(cxrEmployee.getName());
        dayStatistic.setJobNumber(cxrEmployee.getJobNumber());
        dayStatistic.setSiteId(cxrEmployee.getCxrSiteId());
        CxrSite cxrSite = cxrSiteMapper.selectById(cxrEmployee.getCxrSiteId());
        dayStatistic.setSiteMark(cxrSite.getSiteMark());
        dayStatistic.setSiteName(cxrSite.getName());
        dayStatistic.setCxrRegionId(cxrSite.getCxrRootRegionId());
        dayStatistic.setCxrRegionName(cxrSite.getCxrRootRegionName());
        SysDept rootDept = sysDeptMapper.getMaxRootDept(cxrEmployee.getCxrSiteId());
        dayStatistic.setCxrParentRegionId(rootDept.getDeptId());
        dayStatistic.setCxrParentRegionName(rootDept.getDeptName());

        List<WorkOrderOperation> operations = orderOperationMap.get(priorityerId);
        Long count = operations.stream().filter(s -> !ObjectUtil.equals(s.getPriorityStatus(),
            WorkOrderProcessStatusEnums.TOBEPROCESSED.getCode())).count();
        List<Long> workOrderIds = operations.stream().map(WorkOrderOperation::getWorkOrderId).distinct()
            .collect(Collectors.toList());
        dayStatistic.setAddWorkOrderSum(workOrderIds.size());
        dayStatistic.setProcessingWorkOrderSum(count.intValue());
        Long completedWorkOrderSum = workOrderService.count(new LambdaQueryWrapper<WorkOrder>()
            .in(WorkOrder::getId, workOrderIds)
            .eq(WorkOrder::getDeleteStatus, DeleteStatus.not_deleted)
            .eq(WorkOrder::getProcessStatus, WorkOrderProcessStatusEnums.FINISHED.getCode())
        );
        dayStatistic.setCompletedWorkOrderSum(completedWorkOrderSum.intValue());
        Long overtimeWorkOrderSum = operations.stream().filter(s -> ObjectUtil.equals(s.getPriorityStatus(),
            WorkOrderProcessStatusEnums.HAVEEXPECTED.getCode())).count();
        dayStatistic.setOvertimeWorkOrderSum(overtimeWorkOrderSum.intValue());

        return dayStatistic;

      }).collect(Collectors.toList());
      if (CollUtil.isNotEmpty(dayStatistics)){
        statisticService.saveOrUpdateBatch(dayStatistics);
      }
    }
    return false;
  }


}
