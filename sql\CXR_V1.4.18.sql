CREATE TABLE `sales_access_log_history` (
                                            `access_log_id` bigint NOT NULL COMMENT '周期修改记录ID',
                                            `access_id` bigint NOT NULL COMMENT '关联影响到的周期ID',
                                            PRIMARY KEY (`access_log_id`,`access_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


ALTER TABLE `biz_sales_operation`.`cxr_employee_access_cycle_record_log`
    ADD COLUMN `source_type` int NOT NULL DEFAULT 0 COMMENT '0手动 1公司放假 2请假' AFTER `id`;
ALTER TABLE `biz_sales_operation`.`cxr_employee_access_cycle_record_log`
    ADD COLUMN `source_id` bigint NULL COMMENT '来源ID' AFTER `source_type`;
#  洗数据
UPDATE cxr_employee_access_cycle_record_log SET source_type = 1 WHERE update_cycle_reason like '%公司统一%';

ALTER TABLE `biz_sales_operation`.`cxr_employee_access_cycle_record`
    MODIFY COLUMN `update_cycle_reason` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日期调整原因' AFTER `advance_src_stage`;

ALTER TABLE `biz_sales_operation`.`cxr_employee_access_cycle_record_log`
    MODIFY COLUMN `update_cycle_reason` varchar(1200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT
        NULL  ;

