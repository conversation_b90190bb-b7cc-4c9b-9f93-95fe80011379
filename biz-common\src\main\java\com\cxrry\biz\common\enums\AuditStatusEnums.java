package com.cxrry.biz.common.enums;

public enum AuditStatusEnums {
    ToAudit(1, "未审核"),
    Audit(2, "已审核"),
    Refuse(3, "已拒绝");


    private Integer code;
    private String message;

    AuditStatusEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }


}