<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.cxrry</groupId>
    <artifactId>cxrry-biz</artifactId>
    <version>2.0.0</version>
  </parent>

  <groupId>com.cxrry</groupId>
  <artifactId>biz-common</artifactId>
  <version>2.0.0</version>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-common-auto-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cxrry</groupId>
      <artifactId>basic-common</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.5.22</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>io.swagger</groupId>
      <artifactId>swagger-annotations</artifactId>
      <version>1.6.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>ma.glasnost.orika</groupId>
      <artifactId>orika-core</artifactId>
    </dependency>
    <dependency>
        <groupId>com.ruoyi</groupId>
        <artifactId>ruoyi-common-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cxrry</groupId>
      <artifactId>basic-rocketmq-boot-starter</artifactId>
      <version>2.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-api-calculate</artifactId>
      <version>1.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-api-business-base</artifactId>
      <version>1.1.0</version>
    </dependency>
    <dependency>
      <groupId>com.ruoyi</groupId>
      <artifactId>ruoyi-api-system</artifactId>
      <version>2.0.1</version>
    </dependency>
    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml</artifactId>
      <version>5.2.3</version>
    </dependency>

    <dependency>
      <groupId>org.apache.poi</groupId>
      <artifactId>poi-ooxml-schemas</artifactId>
      <version>4.1.2</version>
    </dependency>
	</dependencies>

</project>