package com.cxrry.biz.customer.support.enums.workorder;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WorkOrderOperatorTypeEnums {

    UNFINISHED(0,"默认"),
    FINISHED_WORK_ORDER(1,"完结工单"),
    REPLYTO_WORK_ORDER(2,"回复工单"),
    FORWARD_WORK_ORDER(3,"转交工单"),
    ROLLBACK_ORDER(4,"回退"),
    EXPECTED_AUTO_MATICFLO_WORDER(5,"工单逾期自动流转"),
    WORK_ORDER_RESTART(6,"工单重启"),

    ;
    private final Integer code;
    private final String description;
}
