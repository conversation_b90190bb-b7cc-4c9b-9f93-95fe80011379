package com.cxrry.biz.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SalesLevelMatchLogVo implements Serializable {

    private Long id;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 升降级阶段
     */
    private Integer stage;

    /**
     * 销售代理ID
     */
    private Long cxrEmployeeId;

    /**
     * 考核周期开始时间
     */
    private Date assessmentStartDate;

    /**
     * 考核周期结束时间
     */
    private Date assessmentEndDate;

    /**
     * 执行时间
     */
    private Date runDate;

    /**
     * 执行时间
     */
    private String matchResult;

    /**
     * 创建时间
     */
    private Date createTime;
}
