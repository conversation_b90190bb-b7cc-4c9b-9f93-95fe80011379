package com.cxrry.biz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户下单类型标签 ENUM
 */
@Getter
@AllArgsConstructor
public enum PlaceOrderTypeLabelEnums {

    /**
     * 0-未下单用户
     */
    NO_ORDER_USER(0, "未下单用户"),
    /**
     * 1-鲜奶订单用户
     */
    FRESH_MILK_ORDER_USER(1, "鲜奶订单用户"),
    /**
     * 2-零售订单用户
     */
    RETAIL_ORDER_USER(2, "零售订单用户"),
    ;

    private final Integer code;
    private final String message;
}
