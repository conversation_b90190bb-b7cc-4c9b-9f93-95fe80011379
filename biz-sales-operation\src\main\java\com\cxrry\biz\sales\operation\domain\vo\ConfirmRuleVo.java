package com.cxrry.biz.sales.operation.domain.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ConfirmRuleVo implements Serializable {

    /**
     * 分成id
     */
    private Long divideId;

    /**
     * 开启时间-分成确认
     */
    private Integer startDivideDate;

    /**
     * 开启时间-结分成确认
     */
    private Integer endDivideDate;


    /**
     * 开启时间-账户确认
     */
    private Integer startAccountDate;

    /**
     * 开启时间-账户确认
     */
    private Integer endAccountDate;

    /**
     * 账户信息确认状态：0.未确认，1.已确认(确认无误) 2.已确认(账号信息异常)
     */
    private Integer accountInformationConfirmStatus;

    /**
     * 实发分成确认标志：0.未确认，1.已确认
     */
    private Integer actualDivideConfirmStatus;

}
