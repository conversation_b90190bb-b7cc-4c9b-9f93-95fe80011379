package com.cxrry.biz.common.json;

import lombok.Data;

import java.io.Serializable;

/**
 * 修改痕迹  历史字段    例:"oldeAmEnable":"早上起送改成暂停"
 */
@Data
public class AlterRecord implements Serializable {


    private String oldName;

    private String oldPhone;

    private String oldProvice;

    private String oldCity;

    private String oldArea;

    private String oldSiteName;

    private String oldAdress;

    private String oldResidentialQuartersName;

    private String oldeAmEnable;

    private String oldePmEnable;

    private boolean oldAmDate;
    private boolean oldPmDate;
    private String newPhone;

    /**
     * 是否首次
     */
    private boolean isFirst;

}
