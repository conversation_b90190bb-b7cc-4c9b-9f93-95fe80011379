package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.common.json.JudgmentValue;
import com.cxrry.biz.common.typeHandler.TypeHandlerConstant;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 工单触发条件
 * @TableName work_order_trigger_condition
 */
@Data
@TableName(value ="work_order_trigger_condition", autoResultMap = true)
public class WorkOrderTriggerCondition implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 触发id
     */
    private Long triggerId;

    /**
     * 判断类型1前置条件2字段变更条件
     */
    private Integer judgmentType;

    /**
     * 条件组别1全部满足2其中一个满足
     */
    private Integer conditionalGroup;

    /**
     * 条件项1分类2状态3优先级4受理人
     */
    private Integer conditionEntry;

    /**
     * 前置条件判断1是2不是3包含任意4不包含任意
     */
    private Integer precondition;

    /**
     * 变更条件判断1已更改2更改为3更改自4未更改为5不是更改自
     */
    private Integer alteredCondition;

    /**
     * 判断值
     */

    @TableField(typeHandler = TypeHandlerConstant.judgmentValueJson.class)
    private List<JudgmentValue> judgmentValue;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;


    private String triggerConditionScript;

    private String username;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}