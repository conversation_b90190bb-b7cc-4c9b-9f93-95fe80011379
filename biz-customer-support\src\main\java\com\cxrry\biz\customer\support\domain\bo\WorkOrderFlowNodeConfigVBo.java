package com.cxrry.biz.customer.support.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.json.WorkOrderButtonConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderFlowNodeConfigVBo {
    private Long id;

  @ApiModelProperty("按钮")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderButtonConfigHandler.class)
  private List<WorkOrderButtonConfig> buttonConfig;
}
