package com.cxrry.biz.common.typeHandler;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.cxrry.biz.common.json.BusinessAgent;
import com.cxrry.biz.common.json.ContractOrderCustomerInfo;
import com.cxrry.biz.common.json.CustomerAddressMilkDistributionInfo;
import com.cxrry.biz.common.json.CustomerInfo;
import com.cxrry.biz.common.json.CxrRoadWayDetailSeq;
import com.cxrry.biz.common.json.DistributionInfo;
import com.cxrry.biz.common.json.EmployeeHistoryDate;
import com.cxrry.biz.common.json.GivePrproductInfo;
import com.cxrry.biz.common.json.GoodsInfo;
import com.cxrry.biz.common.json.MilkDistributionInfo;
import com.cxrry.biz.common.json.ReserveDirector;
import com.cxrry.biz.common.json.RoadWayDetail;
import com.cxrry.biz.common.json.SiteFreshMilkAssessmentTimeInfo;
import com.cxrry.biz.common.json.*;

import java.util.List;


/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/15 10:51
 **/
public class TypeHandlerConstant {

    public static class BusinessAgentTypeHandler
            extends AbstractJsonTypeHandler<List<BusinessAgent>> {

        @Override
        protected List<BusinessAgent> parse(String json) {
            return JSONUtil.toList(json, BusinessAgent.class);
        }

        @Override
        protected String toJson(List<BusinessAgent> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }
    public static class AgentTypeHandler extends AbstractJsonTypeHandler<List<BusinessAgent>> {

        @Override
        protected List<BusinessAgent> parse(String json) {
            return JSONUtil.toList(json, BusinessAgent.class);
        }

        @Override
        protected String toJson(List<BusinessAgent> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class EmployeeHistoryDateTypeHandler extends AbstractJsonTypeHandler<List<EmployeeHistoryDate>> {

        @Override
        protected List<EmployeeHistoryDate> parse(String json) {
            return JSONUtil.toList(json, EmployeeHistoryDate.class);
        }

        @Override
        protected String toJson(List<EmployeeHistoryDate> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class ListMilkDistributionInfoTypeHandler extends
        AbstractJsonTypeHandler<List<MilkDistributionInfo>> {

        @Override
        protected List<MilkDistributionInfo> parse(String json) {
            return JSONUtil.toList(json, MilkDistributionInfo.class);
        }

        @Override
        protected String toJson(List<MilkDistributionInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }









    public static class ReserveDirectorDateTypeHandler extends AbstractJsonTypeHandler<List<ReserveDirector>> {

        @Override
        protected List<ReserveDirector> parse(String json) {
            return JSONUtil.toList(json, ReserveDirector.class);
        }

        @Override
        protected String toJson(List<ReserveDirector> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class CustomerAddressMilkDistributionInfoTypeHandler extends
        AbstractJsonTypeHandler<CustomerAddressMilkDistributionInfo> {


        @Override
        protected CustomerAddressMilkDistributionInfo parse(String json) {
            return JSONUtil.toBean(json, CustomerAddressMilkDistributionInfo.class);
        }

        @Override
        protected String toJson(CustomerAddressMilkDistributionInfo obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class RoadWayDetailTypeHandler extends AbstractJsonTypeHandler<List<RoadWayDetail>> {


        @Override
        protected List<RoadWayDetail> parse(String json) {
            return JSONUtil.toList(json, RoadWayDetail.class);
        }

        @Override
        protected String toJson(List<RoadWayDetail> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class DistributionInfoTypeHandler extends AbstractJsonTypeHandler<List<DistributionInfo>> {


        @Override
        protected List<DistributionInfo> parse(String json) {
            return JSONUtil.toList(json, DistributionInfo.class);
        }

        @Override
        protected String toJson(List<DistributionInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class OriginalDataHandler extends AbstractJsonTypeHandler<Object> {

        @Override
        protected Object parse(String json) {
            return JSONUtil.parseObj(json);
        }

        @Override
        protected String toJson(Object obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class CxrRoadWayDetailSeqTypeHandle extends AbstractJsonTypeHandler<List<CxrRoadWayDetailSeq>> {


        @Override
        protected List<CxrRoadWayDetailSeq> parse(String json) {
            return JSONUtil.toList(json, CxrRoadWayDetailSeq.class);
        }

        @Override
        protected String toJson(List<CxrRoadWayDetailSeq> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class GoodsInfoTypeHandler extends AbstractJsonTypeHandler<List<GoodsInfo>> {


        @Override
        protected List<GoodsInfo> parse(String json) {
            return JSONUtil.toList(json, GoodsInfo.class);
        }

        @Override
        protected String toJson(List<GoodsInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class SiteFreshMilkAssessmentTimeInfoTypeHandler extends
        AbstractJsonTypeHandler<List<SiteFreshMilkAssessmentTimeInfo>> {


        @Override
        protected List<SiteFreshMilkAssessmentTimeInfo> parse(String json) {
            return JSONUtil.toList(json, SiteFreshMilkAssessmentTimeInfo.class);
        }

        @Override
        protected String toJson(List<SiteFreshMilkAssessmentTimeInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class applyGivePrproduct extends AbstractJsonTypeHandler<List<GivePrproductInfo>> {

        @Override
        protected List<GivePrproductInfo> parse(String json) {
            return JSONUtil.toList(json, GivePrproductInfo.class);
        }

        @Override
        protected String toJson(List<GivePrproductInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class LongList extends AbstractJsonTypeHandler<List<Long>> {

        @Override
        protected List<Long> parse(String json) {
            return JSONUtil.toList(json, Long.class);
        }

        @Override
        protected String toJson(List<Long> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }












    public static class CustomerInfoTypeHandler extends AbstractJsonTypeHandler<List<CustomerInfo>> {

        @Override
        protected List<CustomerInfo> parse(String json) {
            return JSONUtil.toList(json, CustomerInfo.class);
        }

        @Override
        protected String toJson(List<CustomerInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }

    public static class ContractOrderCustomerInfoTypeHandler
        extends AbstractJsonTypeHandler<List<ContractOrderCustomerInfo>> {

        @Override
        protected List<ContractOrderCustomerInfo> parse(String json) {
            return JSONUtil.toList(json, ContractOrderCustomerInfo.class);
        }

        @Override
        protected String toJson(List<ContractOrderCustomerInfo> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }


    public static class judgmentValueJson extends AbstractJsonTypeHandler<List<JudgmentValue>> {
        public judgmentValueJson() {
        }

        protected List<JudgmentValue> parse(String json) {
            return JSONUtil.toList(json, JudgmentValue.class);
        }

        protected String toJson(List<JudgmentValue> obj) {
            return JSONUtil.toJsonStr(obj);
        }
    }




}
