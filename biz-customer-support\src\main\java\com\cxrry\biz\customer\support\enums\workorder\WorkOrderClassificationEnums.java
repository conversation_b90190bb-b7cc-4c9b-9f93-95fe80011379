package com.cxrry.biz.customer.support.enums.workorder;

import com.cxrry.biz.customer.support.enums.workordertrigger.WorkOrderTriggerActiveSendUserTypeEnums;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WorkOrderClassificationEnums {


    COMPLAINT_WORK_ORDER(1,"投诉工单"),
    REFUND_ORDER(2,"退款工单"),
    BUSINESS_ORDER(3,"业务工单")
    ;
    private final Integer code;
    private final String description;

    public static WorkOrderClassificationEnums getByCode(int code) {
        for (WorkOrderClassificationEnums role : WorkOrderClassificationEnums.values()) {
            if (role.getCode() == code) {
                return role;
            }
        }
        throw new IllegalArgumentException("Invalid  code: " + code);
    }
}
