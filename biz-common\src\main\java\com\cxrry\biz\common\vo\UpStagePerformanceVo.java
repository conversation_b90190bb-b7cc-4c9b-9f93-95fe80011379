package com.cxrry.biz.common.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class UpStagePerformanceVo implements Serializable {

    /**
     * 销售代理
     */
    private Long employeeId;
    /**
     * 业绩
     */
    private BigDecimal performance;

    /**
     * 上阶段
     */
    private Integer stage;

    /**
     * 上阶段名称
     */
    private String stageName;

    public UpStagePerformanceVo() {
    }

    public UpStagePerformanceVo(Long employeeId,Integer stage,String stageName,BigDecimal performance) {
        this.employeeId = employeeId;
        this.stage = stage;
        this.stageName = stageName;
        this.performance = performance;
    }
}
