package com.cxrry.biz.common.enums;

import cn.hutool.core.util.NumberUtil;

/**
 * Description: 支付状态枚举
 *1、待支付2、支付失败3、支付成功4、重复支付 5.支付关闭 6.申请退款
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/17 16:50
 */
public enum PayStatusEnums {

    /**
     *
     */
    UPAY_ORDER_NOT_EXISTS(-1, "订单未创建"),

    NO_PAY(0, "不需要支付"),
    WAIT_PAY(1, "待支付"),
    PAY_FAILED(2, "支付失败"),
    PAY_SUCCEEDED(3, "支付成功"),
    PAY_ERROR_RECOVERY(4, "重复支付")
    ,
    PAY_CLOSE(5, "支付关闭"),
    PAY_REFUND(6, "申请退款")
    ;

    private int value;
    private String desc;

    PayStatusEnums(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getdesc(int value) {
        for (PayStatusEnums payStatusEnums : values()) {
            if (NumberUtil.equals(payStatusEnums.getValue(), value)) {
                return payStatusEnums.getDesc();
            }
        }
        return "";
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }
}
