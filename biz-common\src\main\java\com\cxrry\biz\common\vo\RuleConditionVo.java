package com.cxrry.biz.common.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 条件表
 * @TableName rule_condition
 */
@Data
public class RuleConditionVo implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
  /**
   * 条件id
   */
  @TableId
  private Long id;

  /**
   * 条件类型:1.当前等级 2.业绩 3.考试周期 4.职务
   */
  @ApiModelProperty(value = "条件类型:1.当前等级 2.业绩 3.考试周期 4.职务")
  private Integer type;

  /**
   * 数据
   */
  @ApiModelProperty(value = "数据")
  private String dataJson;

  /**
   * 条件组id
   */
  @ApiModelProperty(value = "条件组id")
  private Long conditionGroupId;

  /**
   * 规则ID
   */
  @ApiModelProperty(value = "规则id")
  private Long ruleId;

  /**
   * 规则集ID
   */
  @ApiModelProperty(value = "规则集id")
  private Long ruleSetId;

  /**
   * 规则编码
   */
  private String ruleSetCode;

 /**
  * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

  /**
   * 删除状态
   */
  private String deleteStatus;

}