package com.cxrry.biz.customer.support.domain.bo;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderProcessingTotalBo  extends PageQuery {

  @ApiModelProperty(value = "公司id")
  private Long priorityerDeptId;
  @ApiModelProperty(value = "大区")
  private String priorityerRootRegionName;
  @ApiModelProperty(value = "区域")
  private String priorityerRegionName;
  @ApiModelProperty(value = "省")
  private String priorityerProvince;
  @ApiModelProperty(value = "市")
  private String priorityerCity;
  @ApiModelProperty(value = "区")
  private String priorityerArea;
  @ApiModelProperty(value = "站点")
  private String priorityerSiteName;
  @ApiModelProperty(value = "站点编号")
  private String priorityerSiteMark;
  @ApiModelProperty(value = "销售代理id")
  private List<Long> priorityerIds;
  @ApiModelProperty(value = "代理编号")
  private String priorityerNumber;
  @ApiModelProperty(value = "状态")
  private String occupationStatus;
  @ApiModelProperty(value = "分类id")
  private List<Long> orderMenuIds;
  @ApiModelProperty(value = "创建开始时间")
  private LocalDate createStartTime;
  @ApiModelProperty(value = "创建结束时间")
  private LocalDate createEndTime;
  @ApiModelProperty(value = "1.销售代理 2.站点 3.区域")
  private Integer flag;


}
