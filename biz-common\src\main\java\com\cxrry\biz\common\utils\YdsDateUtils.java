package com.cxrry.biz.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.time.*;
import java.util.Calendar;
import java.util.Date;


/**
 * 日期处理
 *
 * <AUTHOR>
 */
@Slf4j
public class YdsDateUtils extends DateUtil{
    /**
     * 时间格式(yyyy-MM)
     */
    public final static String DATE_YYYY_MM = "yyyy-MM";
    /**
     * 时间格式(yyyy-MM-dd)
     */
    public final static String DATE_PATTERN = "yyyy-MM-dd";
    /**
     * 时间格式(yyyy-MM-dd HH:mm:ss)
     */
    public final static String DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";


    public static final String TIME_FORMATTER = "HH:mm:ss";

    /**
     * 无分隔符日期格式 "yyyyMMddHHmmssSSS"
     */
    public static String DATE_TIME_PATTERN_YYYY_MM_DD_HH_MM_SS_SSS = "yyyyMMddHHmmssSSS";
    /**
     * 不带秒的标准日期格式 "yyyy.MM.dd HH:mm"
     */
    public static String PATTERN_YYYY_MM_DD_HH_MM = "yyyy.MM.dd HH:mm";

    public static String PATTERN_YYYY_MM_DD_T_HH_MM_SSZ = "yyyy-MM-dd'T'HH:mm:ssZ";

    public static String PATTERN_YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";

    /**
     * 日期转换格式数组
     */
    public static String[][] regularExp = new String[][]{

            // 默认格式
            {"\\d{4}-((([0][1,3-9]|[1][0-2]|[1-9])-([0-2]\\d|[3][0,1]|[1-9]))|((02|2)-(([1-9])|[0-2]\\d)))\\s+([0,1]\\d|[2][0-3]|\\d):([0-5]\\d|\\d):([0-5]\\d|\\d)",
                    DATE_TIME_PATTERN},
            // 仅日期格式 年月日 时 分 秒
            {"\\d{4}.((([0][1,3-9]|[1][0-2]|[1-9]).([0-2]\\d|[3][0,1]|[1-9]))|((02|2).(([1-9])|[0-2]\\d)))\\s+([0,1]\\d|[2][0-3]|\\d):([0-5]\\d|\\d)",
                    PATTERN_YYYY_MM_DD_HH_MM},
            // 仅日期格式 年月日
            {"\\d{4}-((([0][1,3-9]|[1][0-2]|[1-9])-([0-2]\\d|[3][0,1]|[1-9]))|((02|2)-(([1-9])|[0-2]\\d)))",
                    DATE_PATTERN},
            //  带毫秒格式
            {"\\d{4}((([0][1,3-9]|[1][0-2]|[1-9])([0-2]\\d|[3][0,1]|[1-9]))|((02|2)(([1-9])|[0-2]\\d)))([0,1]\\d|[2][0-3])([0-5]\\d|\\d)([0-5]\\d|\\d)\\d{1,3}",
                    DATE_TIME_PATTERN_YYYY_MM_DD_HH_MM_SS_SSS}
    };

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date 日期
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date) {
        return format(date, DATE_PATTERN);
    }

    /**
     * 日期格式化 日期格式为：yyyy-MM-dd
     *
     * @param date    日期
     * @param pattern 格式，如：DateUtils.DATE_TIME_PATTERN
     * @return 返回yyyy-MM-dd格式日期
     */
    public static String format(Date date, String pattern) {
        if (date != null) {
            SimpleDateFormat df = new SimpleDateFormat(pattern);
            return df.format(date);
        }
        return null;
    }
    /**
     *  LocalDateTime --> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 根据升降级周期起始日期生成结束日期
     * 1.按天 2.按自然月  3.按当月剩余天数 4.按连续月份
     */
    public static Date getEndDate(Date startDate,Integer cycleType,Integer cycleValue){
        Date endDate=null;
        //因为前后日期都包含，所以要减1
        if(cycleValue !=null){
            cycleValue = cycleValue-1;
        }
        switch (cycleType){
            case 1:
                endDate = DateUtil.offsetDay(startDate,cycleValue).toSqlDate();
                break;
            case 2:
                endDate = DateUtil.endOfMonth(DateUtil.offsetMonth(startDate,cycleValue)).toSqlDate();
                break;
            case 3:
                endDate = DateUtil.endOfMonth(startDate).toSqlDate();
                break;
            case 4:
                endDate =DateUtil.endOfMonth( DateUtil.offsetMonth(startDate,cycleValue).toSqlDate());//不包含起始日期月
                break;
            default:
                throw new RuntimeException("不支持的周期类型"+cycleType);
        }
        return endDate == null?endDate: DateUtil.parse(DateUtil.format(endDate,
            DatePattern.NORM_DATE_PATTERN));
    }

    /**
     * 是否月底
     */
    public static Boolean isEndOfMonth(Date date) {
        if (date == null) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, (calendar.get(Calendar.DATE) + 1));
        if (calendar.get(Calendar.DAY_OF_MONTH) == 1) {
            return true;
        }else{
            return false;
        }
    }

    /**
     * Date -> LocalDate
     *
     * @param date
     * @return
     */
    public static LocalDate getLocalDateFromDate(Date date) {
        LocalDateTime localDateTime = getLocalDateTimeFormDate(date);
        return localDateTime.toLocalDate();
    }

    /**
     * date -> LocalDateTime
     *
     * @param date
     * @return
     */
    public static LocalDateTime getLocalDateTimeFormDate(Date date) {
        Instant instant = date.toInstant();
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    /**
     * 将LocalDate转换为Date的方法
     * @param localDate 要转换的LocalDate对象
     * @return 转换后的Date对象
     */
    public static Date convertLocalDateToDate(LocalDate localDate) {
        if (localDate == null) {
            return null;
        }
        ZoneId zoneId = ZoneId.systemDefault();
        return Date.from(localDate.atStartOfDay(zoneId).toInstant());
    }

    /**
     * 取两组日期的交集日期
     * 返回日期数组，长度为2，第一个元素为交集的开始日期，第二个元素为交集的结束日期。无则空
     */
    public static Date[] intersectRanges(Date start1, Date end1, Date start2, Date end2) {
        // 确保开始日期不晚于结束日期
        if (start1.after(end1) || start2.after(end2)) {
            throw new IllegalArgumentException("Start date should not be after end date.");
        }

        // 找到两个范围中较晚的开始日期和较早的结束日期
        Date start = start1.after(start2) ? start1 : start2;
        Date end = end1.before(end2) ? end1 : end2;

        // 检查是否有交集
        if (start.before(end) || start.equals(end)) {
            return new Date[]{start, end};
        } else {
            return null;
        }
    }

    public static LocalDate getMonthFirstDay(String monthStr) {
       return LocalDate.parse(StrUtil.format("{}-01", monthStr));
    }
    public static String getDateMonth(LocalDate localDate) {
       return LocalDateTimeUtil.format(localDate, DATE_YYYY_MM);
    }
    public static String strMonthPlus(String monthStr,int monthsToAdd) {
        return LocalDateTimeUtil.format(getMonthFirstDay(monthStr).plusMonths(monthsToAdd), DATE_YYYY_MM);
    }
}
