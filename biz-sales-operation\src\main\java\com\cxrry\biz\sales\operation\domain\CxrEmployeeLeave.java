package com.cxrry.biz.sales.operation.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.logger.api.annotation.LogModel;
import com.cxrry.logger.api.annotation.LogTag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 员工请假管理对象 cxr_employee_leave
 *
 * <AUTHOR>
 * @date 2022-07-20
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cxr_employee_leave")
@LogModel(value = "员工请假管理",tableName = "cxr_employee_leave")
public class CxrEmployeeLeave extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 员工id
     */
    @LogTag(alias = "员工id")
    private Long employeeId;

    /**
     * 请假开始时间
     */
    @LogTag(alias = "请假开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 请假结束时间
     */
    @LogTag(alias = "请假结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @LogTag(alias = "实际请假开始时间")
    @ApiModelProperty(value = "实际请假开始时间")
    private LocalDate actStartTime;

    @LogTag(alias = "实际请假结束时间")
    @ApiModelProperty(value = "实际请假结束时间")
    private LocalDate actEndTime;

    /**
     * 请假类型id
     */
    @LogTag(alias = "请假类型id")
    private Long typeId;

    /**
     * 备注
     */
    @LogTag(alias = "备注")
    private String memo;


    /**
     * 是否考核延期 1是
     */
    @LogTag(alias = "是否考核延期 1是")
    private Boolean delayFlag;

    /**
     * 公司ID
     */
    @LogTag(alias = "公司ID")
    private Long companyId;

    /**
     * 公司名称
     */
    @LogTag(alias = "公司名称")
    private String companyName;

    /**
     * 大区ID
     */
    @LogTag(alias = "大区ID")
    private Long rootRegionId;

    /**
     * 大区名称
     */
    @LogTag(alias = "大区名称")
    private String rootRegionName;

    /**
     * 区域ID
     */
    @LogTag(alias = "区域ID")
    private Long regionId;

    /**
     * 区域名称
     */
    @LogTag(alias = "区域名称")
    private String regionName;

    /**
     * 区域编号
     */
    @LogTag(alias = "区域编号")
    private String regionCode;

    /**
     * 站点名称
     */
    @LogTag(alias = "站点名称")
    private String siteName;

    /**
     * 站点编号
     */
    @LogTag(alias = "站点编号")
    private String siteCode;


    /**
     * 销售代理姓名
     */
    @LogTag(alias = "销售代理姓名")
    private String agentName;

    /**
     * 代理编号
     */
    @LogTag(alias = "代理编号")
    private String agentCode;

    /**
     * 代理等级
     */
    @LogTag(alias = "代理等级")
    private String agentLevel;

    /**
     * 文件 1
     */
    @LogTag(alias = "文件 1")
    private String fileOneUrl;

    /**
     *
     */
    @LogTag(alias = "文件 2 意外险报备")
    private String fileTwoUrl;

    /**
     *
     */
    @LogTag(alias = "文件 3")
    private String fileThreeUrl;

    /**
     *
     */
    @LogTag(alias = "文件 4")
    private String fileFourUrl;

    /**
     *
     */
    @LogTag(alias = "文件 5")
    private String fileFiveUrl;

    /**
     * 申请时间
     */
    @LogTag(alias = "申请时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyTime;

    /**
     * 审批用户id
     */
    private Long approvalUserId;

    /**
     * 审批状态
     */
    private String approvalStatus;

    /**
     * 审核人名称
     */
    @LogTag(alias = "审核人名称")
    private String approvalUserName;

    /**
     * 审核时间
     */
    @LogTag(alias = "审核时间")

    private Date approvalTime;

    /**
     * 请假类型
     */
    @LogTag(alias = "请假类型")
    private Long mode;

    /**
     * 站点id(员工所在的站点的id)
     */
    private Long cxrSiteId;

    /**
     * 销假单绑定的请假单ID
     */
    private Long leaveId;


    /**
     * 审核备注
     */
    private String approvalNotes;


    /**
     * 请假时长
     */
    private Integer days;

    /**
     * 请假时长
     */
    private Integer actDays;


    @ApiModelProperty("销假单绑定的请假单类型ID")
    private Long srcLeaveTypeId;
}
