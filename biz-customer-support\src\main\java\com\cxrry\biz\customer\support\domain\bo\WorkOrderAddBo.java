package com.cxrry.biz.customer.support.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.biz.customer.support.domain.WorkOrderPriorityNode;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderAddBo {
  @ApiModelProperty("工单分类 1.投诉工单 2.退款工单")
  private Integer type;
  @ApiModelProperty("1低 2一般 3紧急 4非常紧急")
  private Integer priority;
  @ApiModelProperty("投诉类型多级名称拼接字符串")
  private String orderMenuRemark;
  @ApiModelProperty("分类id")
  private Long orderMenuId;
  @ApiModelProperty("最新待受理人用户名称，多个逗号分开")
  private String priorityingUserStr;
  @ApiModelProperty("工单描述内容")
  private String describetion;
  @ApiModelProperty("工单附件相对路径url，多个用逗号隔开")
  private List<FileUrlConfig> fileUrl;
  @ApiModelProperty("客户ID")
  private Long customerId;
  @ApiModelProperty("客户主地址手机号")
  private String customerMainPhone;
  @ApiModelProperty("客户登录手机号")
  private String customerLoginPhone;
  @ApiModelProperty("客户名称")
  private String customerName;
  @ApiModelProperty("客户地址")
  private String customerAddress;
  @ApiModelProperty("受理站点ID")
  private Long siteId;
  @ApiModelProperty("受理站点名称")
  private String siteName;
  @ApiModelProperty("受理区域ID")
  private Long regionId;
  @ApiModelProperty("受理区域名称")
  private String regionName;
  @ApiModelProperty("站点主管用户ID")
  private Long siteDirectorUserId;
  @ApiModelProperty("站点主管名称")
  private String siteDirectorUserName;
  @ApiModelProperty("站点编号")
  private String siteMark;
  @ApiModelProperty("销售代理id")
  private Long employeeId;
  @ApiModelProperty("销售代理名称")
  private String employeeName;
  @ApiModelProperty("销售代理编号")
  private String jobNumber;
  @ApiModelProperty(value="公司id")
  private Long currentDeptId;
  @ApiModelProperty(value="省")
  private String province;
  @ApiModelProperty(value="市")
  private String city;
  @ApiModelProperty(value="区")
  private String area;
  @ApiModelProperty(value = "关注人")
  List<WorkOrderFollower> workOrderFollowerList;
  @ApiModelProperty(value = "1.自动分配 2.指定人员")
  private Integer acceptedFlag;
  @ApiModelProperty(value = "受理人选择的是:1.销售代理 2.总部用户")
  private Integer acceptedAccount;
  @ApiModelProperty(value = "最新待受理人用户名称")
  private String acceptorStr;
  @ApiModelProperty(value = "最新待受理人用户编号")
  private String acceptorNumber;
  @ApiModelProperty(value = "最新待受理人用户id")
  private Long acceptorId;

  @ApiModelProperty(value = "投诉对象id")
  private Long complaintObjectId;
  @ApiModelProperty(value = "投诉对象名称")
  private String complaintObjectName;

  //流程id
  private Long flowConfigId;

  //受理人的节点id
  List<WorkOrderPriorityNode>priorityNodeList;

  private String complaintObjectNumber; // 投诉对象的编号

  private String complaintObjectSiteName; // 投诉对象站点名称

  private String complaintObjectSiteMark; // 投诉对象站点编号

  private Long complaintObjectSiteId; // 投诉对象站点id

  private String complaintObjectDeptName; // 投诉对象公司名

  private Long complaintObjectDeptId; // 投诉对象公司id

  private Long complaintObjectRegionId; // 投诉对象区域id

  private String complaintObjectRegionName; // 投诉对象区域名称
  private Long complaintObjectRootRegionId; // 投诉对象大区id

  private String complaintObjectRootRegionName; // 投诉对象大区名称

  private String complaintObjectProvince; // 投诉对象省

  private String complaintObjectCity; // 投诉对象市
  private String complaintObjectArea; // 投诉对象区

  @ApiModelProperty(value = "开单销售代理")
  private List<OperatorMessageConfig> operatorMessage;
  @ApiModelProperty(value = "客户地址id")
  private Long customerAddressId;
}
