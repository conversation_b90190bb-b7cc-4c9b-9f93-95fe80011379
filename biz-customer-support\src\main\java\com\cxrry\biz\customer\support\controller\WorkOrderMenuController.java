package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.cxrry.biz.customer.support.domain.WorkOrderMenu;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderMenuAddBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderMenuUpdateBo;
import com.cxrry.biz.customer.support.service.WorkOrderMenuService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
@Validated
@Api(
    value = "工单列表",
    tags = {"工单列表"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderMenu")
public class WorkOrderMenuController {

  private final WorkOrderMenuService workOrderMenuService;


  @ApiOperation("获取工单菜单栏")
  @GetMapping("/treeselect")
  @SaCheckPermission("support:workOrderMenu:treeselect")
  public R<List<Tree<Long>>> treeselect() {
    List<WorkOrderMenu> WorkOrderMenusList = workOrderMenuService.selectWorkOrderMenusList();
    return R.ok(workOrderMenuService.buildWorkOrderTreeSelect(WorkOrderMenusList));
  }

  @ApiOperation("排序")
  @PostMapping("/sort")
  public R sort(@RequestBody List<WorkOrderMenu>list) {
    return R.ok(workOrderMenuService.sort(list));
  }
  @ApiOperation("新增")
  @SaCheckPermission("support:workOrderMenu:add")
  @PostMapping("/add")
  public R add(@RequestBody WorkOrderMenuAddBo bo) {
    return R.ok(workOrderMenuService.add(bo));
  }

  @ApiOperation("编辑")
  @SaCheckPermission("support:workOrderMenu:update")
  @PostMapping("/update")
  public R update(@RequestBody WorkOrderMenuUpdateBo bo) {
    return R.ok(workOrderMenuService.workOrderMenupdate(bo));
  }

  //删除 考虑删除的逻辑是否在几菜单上面
  @ApiOperation("删除 ")
  @SaCheckPermission("support:workOrderMenu:delete")
  @PostMapping("/delete")
  public R delete(@RequestBody WorkOrderMenuUpdateBo bo) {
    return R.ok(workOrderMenuService.deleteById(bo));
  }

  @ApiOperation("获取工单菜单栏")
  @GetMapping("/treeselectPull")
  public R<List<Tree<Long>>> treeselectPull() {
    List<WorkOrderMenu> WorkOrderMenusList = workOrderMenuService.selectWorkOrderMenusList();
    return R.ok(workOrderMenuService.buildWorkOrderTreePullSelect(WorkOrderMenusList));
  }

  @ApiOperation("获取工单菜单栏二级以下的目录")
  @GetMapping("/MenuLevelByQuery")
  public R<List<Tree<Long>>> MenuLevelByQuery(@RequestParam Integer level) {
    List<WorkOrderMenu> WorkOrderMenusList = workOrderMenuService.selectWorkOrderMenusList(level);
    return R.ok(workOrderMenuService.buildWorkOrderTreeSelect(WorkOrderMenusList));
  }

}
