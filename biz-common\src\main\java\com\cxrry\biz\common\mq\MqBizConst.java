package com.cxrry.biz.common.mq;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @since 2023-12-25
 */
public interface MqBizConst {

  String TOPIC_BIZ_TEST = "BIZ_TEST";

  /**
   * 触发器发送工单公众号消息
   */
  String SENDCUSTOMERSUPPORTCONST_TOPIC =  "sendCustomerSupportConst_topic";

  String DIVIDE_COMPUTE_TOPIC = "divide_compute_topic";

  String DIVIDE_COMPUTE_GROUP = "divide_compute_group";


  /**
   * 计算全部项
   */
  String DIVIDE_COMPUTE_ALL_TAG = "divide_compute_all_tag";

  /**
   * 订单计算
   */
  String DIVIDE_COMPUTE_ORDER_TAG = "divide_compute_order_tag";

  /**
   * 合订单计算
   */
  String DIVIDE_COMPUTE_ORDERS_TAG = "divide_compute_orders_tag";

  /**
   * 借支计算
   */
  String DIVIDE_COMPUTE_PERSONAL_BORROWING_TAG = "divide_compute_personal_borrowing_tag";

  /**
   * 吃住空调计算
   */
  String DIVIDE_COMPUTE_EAT_LIVE_AC_TAG = "divide_compute_eat_live_ac_tag";

  /**
   * 样品申领计算
   */
  String DIVIDE_COMPUTE_APPLY_SAMPLES_TAG = "divide_compute_apply_samples_tag";

  /**
   *新单费计算
   */
  String DIVIDE_COMPUTE_NEW_ORDER_FEE_TAG = "divide_compute_new_order_fee_tag";

  /**
   * 鲜奶拿货价计算
   */
  String DIVIDE_COMPUTE_FM_PURCHASE_UNIT_PRICE_TAG = "divide_compute_fm_purchase_unit_price_tag";

  /**
   * 补贴费计算
   */

  String DIVIDE_COMPUTE_SUBSIDY_FEE_TAG = "divide_compute_subsidy_fee_tag";

  /**
   * 保证金计算
   */
  String DIVIDE_COMPUTE_EARNEST_MONEY_TAG = "divide_compute_earnest_money_tag";

  /**
   * 保证金计算
   */
  String DIVIDE_COMPUTE_EARNEST_MONEY_NEXT_TAG = "divide_compute_earnest_money_next_tag";

  /**
   * 修改规则重新计算
   */
  String DIVIDE_COMPUTE_RULE_UPDATE_TAG = "divide_compute_rule_update_tag";

  /**
   * 重算上个月合作中数据
   */
  String RULE_UPDATE_RECALCULATE_LAST_MONTH_DATA_INCOOP_TAG =  "rule_update_recalculate_last_month_data_incoop_tag";

  /**
   * 修改规则重新计算
   */
  String DIVIDE_COMPUTE_RULE_RANGE_UPDATE_TAG = "divide_compute_rule_range_update_tag";

  /**
   * 站点绑定默认分成收费规则 TOPIC
   */
  String SITE_BIND_DEFAULT_RULE_TOPIC = "site_bind_default_rule_topic";

  /**
   * 站点绑定默认分成收费规则 GROUP
   */
  String SITE_BIND_DEFAULT_RULE_GROUP = "site_bind_default_rule_group";

  /**
   * 绑定默认分成收费规则到所有站点 TAG
   */
  String BIND_DEFAULT_RULE_2_SITE_TAG = "bind_default_rule_2_site_tag";

  /**
   * 新站点绑定所有默认分成收费规则 TAG
   */
  String NEW_SITE_BIND_ALL_DEFAULT_RULE_TAG = "new_site_bind_all_default_rule_tag";

  /**
   * 站点绑定默认分成方案 TOPIC
   */
  String SITE_BIND_DEFAULT_SCHEME_TOPIC = "site_bind_default_scheme_topic";

  /**
   * 站点绑定默认分成方案 GROUP
   */
  String SITE_BIND_DEFAULT_SCHEME_GROUP = "site_bind_default_scheme_group";

  /**
   * 绑定默认分成方案到所有站点 TAG
   */
  String BIND_DEFAULT_SCHEME_2_SITE_TAG = "bind_default_scheme_2_site_tag";

  /**
   * 新站点绑定所有默认分成方案 TAG
   */
  String NEW_SITE_BIND_ALL_DEFAULT_SCHEME_TAG = "new_site_bind_all_default_scheme_tag";

  /**
   * 分成方案应用 TOPIC
   */
  String DIVIDE_RANGE_TOPIC = "divide_range_topic";

  /**
   * 分成方案应用 GROUP
   */
  String DIVIDE_RANGE_GROUP = "divide_range_group";

  /**
   * 分成方案/规则应用延迟生效 TAG
   */
  String DIVIDE_RULE_SCHEME_DELAY_EFFECT_TAG = "divide_rule_scheme_delay_effect_tag";

  /**
   * 销售代理过小承包
   */
  String DIVIDE_RULE_COMPUTE_CONTRACT_TAG = "divide_rule_compute_contract_tag";

  /**
   * 业绩比例分成计算
   */
  String DIVIDE_ACHIEVEMENT_RATIO_COMPUTE_TAG = "divide_achievement_ratio_compute_tag";

  /**
   * 客户订阅牛奶分析表变化
   */
  String CUSTOMER_SUBSCRIPTION_MILK_ANALYSIS_TABLE_CHANGE_TOPIC = "customer_subscription_milk_analysis_table_change_topic";

  /**
   * 客户订阅牛奶分析表变化
   */
  String CUSTOMER_SUBSCRIPTION_MILK_ANALYSIS_TABLE_CHANGE_GROUP =
      "customer_subscription_milk_analysis_table_change_group";

  /**
   * 客户订阅牛奶分析表变化
   */
  String CUSTOMER_SUBSCRIPTION_MILK_ANALYSIS_TABLE_CHANGE_TAG = "customer_subscription_milk_analysis_table_change_tag";
}
