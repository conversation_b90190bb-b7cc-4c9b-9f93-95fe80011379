package com.cxrry.biz.customer.support.domain.bo;

import com.cxrry.biz.customer.support.domain.WorkOrderFollower;
import com.cxrry.common.validate.AddGroup;
import com.cxrry.common.validate.EditGroup;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 工单新增
 * @TableName work_order
 */
@Data
public class WorkOrderAddOrEditBo implements Serializable {

    @ApiModelProperty(value = "主键ID")
    @NotNull(message = "主键ID不能为空",groups = {EditGroup.class})
    private Long id;

    /**
     * 工单分类 1.投诉工单 2.退款工单
     */
    @NotNull(message = "工单分类不能为空",groups = {AddGroup.class, EditGroup.class})
    private Integer type;

    /**
     * 投诉类型ID（分类ID）
     */
    @NotNull(message = "投诉类型ID不能为空",groups = {AddGroup.class, EditGroup.class})
    private Long orderMenuId;

    /**
     * 投诉类型多级名称拼接字符串
     */
    @NotBlank(message = "投诉类型名称不能为空",groups = {AddGroup.class, EditGroup.class})
    private String orderMenuRemark;

    /**
     * 1低 2一般 3紧急 4非常紧急
     */
    @NotNull(message = "优先级不能为空",groups = {AddGroup.class, EditGroup.class})
    private Integer priority;

    /**
     * 受理人用户ID
     */
    @NotNull(message = "受理人用户ID不能为空",groups = {AddGroup.class})
    private Long priorityUserId;

    /**
     * 工单描述内容
     */
    @NotBlank(message = "工单描述内容不能为空",groups = {AddGroup.class, EditGroup.class})
    private String describetion;

    /**
     * 工单附件相对路径url，多个用逗号隔开
     */
    private String fileUrl;

    /**
     * 客户ID
     */
    @NotNull(message = "客户ID不能为空",groups = {AddGroup.class, EditGroup.class})
    private Long customerId;

    /**
     * 客户主地址手机号
     */
    @NotBlank(message = "客户主地址手机号不能为空",groups = {AddGroup.class, EditGroup.class})
    private String customerMainPhone;

    /**
     * 客户登录手机号
     */
    @NotBlank(message = "客户登录手机号不能为空",groups = {AddGroup.class, EditGroup.class})
    private String customerLoginPhone;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空",groups = {AddGroup.class})
    private String customerName;

    /**
     * 客户地址
     */
    @NotBlank(message = "客户地址不能为空",groups = {AddGroup.class})
    private String customerAddress;

    /**
     * 受理站点ID
     */
    @NotNull(message = "受理站点ID不能为空",groups = {AddGroup.class})
    private Long siteId;

    /**
     * 受理站点名称
     */
    @NotBlank(message = "受理站点名称不能为空",groups = {AddGroup.class})
    private String siteName;

    /**
     * 受理区域ID
     */
    @NotNull(message = "受理区域ID不能为空",groups = {AddGroup.class})
    private Long regionId;

    /**
     * 受理区域名称
     */
    @NotBlank(message = "受理区域名称不能为空",groups = {AddGroup.class})
    private String regionName;

    /**
     * 受理大区ID
     */
    @NotNull(message = "受理大区ID不能为空",groups = {AddGroup.class})
    private Long rootRegionId;

    /**
     * 受理大区名称
     */
    @NotBlank(message = "受理大区名称不能为空",groups = {AddGroup.class})
    private String rootRegionName;

    /**
     * 站点主管用户ID
     */
    @NotNull(message = "站点主管用户ID不能为空",groups = {AddGroup.class})
    private Long siteDirectorUserId;

    /**
     * 站点主管名称
     */
    @NotBlank(message = "站点主管名称不能为空",groups = {AddGroup.class})
    private String siteDirectorUserName;

    @ApiModelProperty("工单关注人列表")
    List<WorkOrderFollower> workOrderFollowerList;




}