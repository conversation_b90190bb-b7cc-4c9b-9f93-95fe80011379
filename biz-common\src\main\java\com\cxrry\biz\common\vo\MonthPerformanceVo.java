package com.cxrry.biz.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class MonthPerformanceVo implements Serializable {

    /**
     * 周期ID
     */
    private Long assessCycleRecordId;
    /**
     * 销售代理ID
     */
    private Long cxrEmployeeId;
    /**
     * 月份
     */
    private String month;
    /**
     * 月份类型：noLimit不限制 continuous连续 arbitrarily任意 nature自然月
     */
    private String monthType;
    /**
     * 业绩类型：noLimit不限制；all总业绩;every每月业绩
     */
    private String performanceType;
    /**
     * 当前业绩
     */
    private BigDecimal performance;
    /**
     * 目标业绩
     */
    private BigDecimal targetPerformance;
    /**
     * 描述
     */
    private String desc;
    /**
     * 是否达标
     */
    private Boolean isCompliance;

    private Date createTime;

    /**
     * 实际考核起始日期
     */
    private Date actualAssessmentStartDate;

    /**
     * 实际考核结束日期
     */
    private Date actualAssessmentEndDate;

    /**
     * 变更类型：0无操作，1升级，2降级
     */
    private Integer changeType;

    @ApiModelProperty(value = "达标状态：0未达标，1已达标")
    private Integer achievementStatus;

    @ApiModelProperty(value = "运算符号")
    private String calculateSymbol;

    @ApiModelProperty(value = "当前阶段不能为空")
    private Integer stage;
    @ApiModelProperty(value = "当前等级")
    private Integer currentLevel;

    @ApiModelProperty(value = "达标时间")
    private Date achievementDate;
    /**
     * 是否包含上一阶段达标剩余业绩：true=包含；false=不包含
     */
    private boolean containUpStagePerformance;
}
