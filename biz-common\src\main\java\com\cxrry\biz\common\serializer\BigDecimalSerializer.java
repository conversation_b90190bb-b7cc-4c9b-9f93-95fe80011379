package com.cxrry.biz.common.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.SneakyThrows;

import java.math.BigDecimal;

public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

    @SneakyThrows
    @Override
    public void serialize(BigDecimal bigDecimal, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) {
        if (null != bigDecimal ) {
            String plainString = bigDecimal.stripTrailingZeros().toPlainString();
            jsonGenerator.writeString(plainString);
        } else {
            jsonGenerator.writeString(BigDecimal.ZERO.toPlainString());
        }
    }
}