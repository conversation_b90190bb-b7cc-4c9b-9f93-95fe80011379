package com.cxrry.biz.sales.operation.enums.cycle;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 考核周期调整来源类型枚举
 */
@Getter
@AllArgsConstructor
public enum AdjustSourceTypeEnum {
    /**
     * 手动调整
     */
    MANUAL_ADJUST(0, "手动调整"),
    
    /**
     * 公司放假
     */
    COMPANY_HOLIDAY(1, "公司放假"),
    
    /**
     * 个人请假
     */
    PERSONAL_LEAVE(2, "个人请假");

    private final Integer code;
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 匹配的枚举实例，如果没有找到则返回null
     */
    public static AdjustSourceTypeEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (AdjustSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据描述获取枚举
     *
     * @param description 描述
     * @return 匹配的枚举实例，如果没有找到则返回null
     */
    public static AdjustSourceTypeEnum fromDescription(String description) {
        if (description == null || description.trim().isEmpty()) {
            return null;
        }
        for (AdjustSourceTypeEnum type : values()) {
            if (type.getDescription().equals(description.trim())) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断是否为手动调整
     *
     * @return true if manual adjust
     */
    public boolean isManualAdjust() {
        return this == MANUAL_ADJUST;
    }

    /**
     * 判断是否为自动调整（公司放假或个人请假）
     *
     * @return true if auto adjust
     */
    public boolean isAutoAdjust() {
        return this == COMPANY_HOLIDAY || this == PERSONAL_LEAVE;
    }
}