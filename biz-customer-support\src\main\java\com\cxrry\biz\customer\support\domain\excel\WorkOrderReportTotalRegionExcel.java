package com.cxrry.biz.customer.support.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data

public class WorkOrderReportTotalRegionExcel {
  @ExcelProperty(value = "公司")
  private String priorityerDeptName;


  @ExcelProperty(value = "大区")
  private String priorityerRootRegionName;

  @ExcelProperty(value = "区域")
  private String priorityerRegionName;
  @ExcelProperty(value = "区域编号")
  private String priorityerRegionMark;


  //工单新增数
  @ExcelProperty(value = "新增工单数量")
  private Long addCount=0L;

  //工单处理中数量
  @ExcelProperty(value = "处理中工单数量")
  private Long proccessingCount=0L;

  //工单完结数
  @ExcelProperty(value = "已完成工单数量")
  private Long finishCount=0L;
  //工单超时数
  @ExcelProperty(value = "逾期工单数量")
  private Long overdueCount=0L;
  //工单重启数
  @ExcelProperty(value = "重新打开工单数量")
  private Long restartCount=0L;



}
