package com.cxrry.biz.api.client;

import com.ruoyi.calculate.api.domain.vo.PerformanceVo;

import java.util.Collection;
import java.util.List;

public interface RemoteEmployeePerformanceDayService {

	/**
	 * 查询销售代理业绩
	 */
	List<PerformanceVo> getEmployeeMonthPerformanceBy(Collection<Long> employeeIds, String startDate, String endDate);
	List<PerformanceVo> getUpStagePassPerformanceBy(Long employeeId, Integer stage);
}
