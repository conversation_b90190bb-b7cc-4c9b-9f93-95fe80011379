package com.cxrry.biz.common.vo;

import com.ruoyi.common.core.enums.PostType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 一个人有可以又多个 职务, 所以这个会用list放
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CxrPostId implements Serializable {

    private String value; //职务的枚举

    private String scope; //站点的职务还是区域的职务 0,1,2

    private String name; //职务中文


    public static CxrPostId buildCxrPostId(PostType postType) {
        CxrPostId postId = new CxrPostId();
        postId.setValue(postType.getValue());
        postId.setName(postType.getName());
        postId.setScope(postType.getScope());
        return postId;
    }

//    private Long regionId; //一个人有多个职务 ，虽然现在没有出现跨区职务
//
//    private Long regionName; //保存一下中文名
//
//    private Long siteId; //站点  （如果是区域的职务不用填这个）
//
//    private String siteName; //保存一下中文名
}
