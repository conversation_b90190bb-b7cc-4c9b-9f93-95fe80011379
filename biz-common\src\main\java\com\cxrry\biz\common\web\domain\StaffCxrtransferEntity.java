package com.cxrry.biz.common.web.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.BaseColumn;
import com.ruoyi.common.core.web.domain.base.Entity;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class StaffCxrtransferEntity implements Entity, Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @ApiModelProperty("终端类型  1 后端 2 配送端")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private short terminalType;

    /**
     * 创建人名称
     */
    @ApiModelProperty("创建人名称")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private String createByName;

    @ApiModelProperty("创建人id")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Long createBy;
    /**
     * 创建人类型(详情见字典)
     */
    @ApiModelProperty("创建人类型(详情见字典)")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private String createByType;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Date updateTime;

    @ApiModelProperty("修改人")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Long updateBy;

    /**
     * 更新人名称
     */
    @ApiModelProperty("更新人名称")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    @ApiModelProperty("更新人类型(详情见字典)")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String updateByType;

    /**
     * 删除人id
     */
    @ApiModelProperty("删除人id")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Long deleteBy;

    /**
     * 删除人名称
     */
    @ApiModelProperty("删除人名称")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    @ApiModelProperty("删除人类型(详情见字典)")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private String deleteByType;

    /**
     * 删除时间
     */
    @ApiModelProperty("删除时间")
    @TableField(fill = FieldFill.UPDATE)
    @BaseColumn
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    @ApiModelProperty("删除状态(详情见字典)")
    @TableField(fill = FieldFill.INSERT)
    @BaseColumn
    private String deleteStatus;
}
