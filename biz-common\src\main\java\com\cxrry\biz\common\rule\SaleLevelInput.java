package com.cxrry.biz.common.rule;



import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
 * 销售等级升降输入参数类
 */
@Data
public class SaleLevelInput implements Input {

	@NotNull(message = "销售代理ID不能为空")
	private Long employeeId;

	@NotNull(message = "销售等级不能为空")
	private Integer level;

	@NotNull(message = "当前阶段不能为空")
	private Integer stage;


	@NotNull(message = "当前考核记录不能为空")
	private CxrEmployeeAccessCycleRecordDTO employeeAccessCycleRecord;

	//
	private Date performanceAchievedDate;
}
