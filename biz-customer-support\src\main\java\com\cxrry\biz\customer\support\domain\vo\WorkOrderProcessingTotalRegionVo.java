package com.cxrry.biz.customer.support.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WorkOrderProcessingTotalRegionVo {
  @ApiModelProperty(value = "排序")
  @ExcelProperty(value = "排序", index = 0)
  private Integer RowNum;
  @ApiModelProperty(value = "公司")
  @ExcelProperty(value = "公司", index =1 )
  private String priorityerDeptName;
  @ApiModelProperty(value = "大区")
  @ExcelProperty(value = "大区", index = 2)
  private String priorityerRootRegionName;
  @ApiModelProperty(value = "区域")
  @ExcelProperty(value = "区域", index = 3)
  private String priorityerRegionName;
  @ApiModelProperty(value = "区域编号")
  @ExcelProperty(value = "区域编号", index = 4)
  private Long priorityerRegionMark;
  @ApiModelProperty(value = "数量")
  @ExcelProperty(value = "数量", index = 5)
  private String totalCount;

}
