package com.cxrry.biz.sales.operation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cxrry.biz.sales.operation.domain.SalesAccessLogHistory;
import com.cxrry.biz.sales.operation.mapper.SalesAccessLogHistoryMapper;
import com.cxrry.biz.sales.operation.service.SalesAccessLogHistoryService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【sales_access_log_history】的数据库操作Service实现
* @createDate 2025-07-17 10:15:36
*/
@Service
public class SalesAccessLogHistoryServiceImpl extends ServiceImpl<SalesAccessLogHistoryMapper, SalesAccessLogHistory>
    implements SalesAccessLogHistoryService{

}




