package com.cxrry.biz.common.web.domain;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel("分页响应对象")
public class PageTableDataInfo<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总记录数
     */
    @ApiModelProperty("总记录数")
    private long total;

    /**
     * 列表数据
     */
    @ApiModelProperty("列表数据")
    private List<T> rows;

    /**
     * 统计数据
     */
    @ApiModelProperty("列表数据")
    private T totalData;
    /**
     * 列表数据
     */
    @ApiModelProperty("每页条数")
    private long size;
    /**
     * 列表数据
     */
    @ApiModelProperty("当前页码")
    private long curr;

    /**
     * 常温奶汇总使用
     */
    @ApiModelProperty("已申领的数量")
    private Integer applyNum;
    @ApiModelProperty("过期数量")
    private Integer overdueNum;
    @ApiModelProperty("剩余数量")
    private Integer surplusNum;

    /**
     * 分页
     *
     * @param list  列表数据
     * @param total 总记录数
     */
    public PageTableDataInfo(List<T> list, long total) {
        this.rows = list;
        this.total = total;
    }

    public static <T> PageTableDataInfo<T> build(IPage<T> page) {
        PageTableDataInfo<T> rspData = new PageTableDataInfo<>();
        rspData.setRows(page.getRecords());
        rspData.setTotal(page.getTotal());
        rspData.setSize(page.getSize());
        rspData.setCurr(page.getCurrent());
        return rspData;
    }

    public static <T> PageTableDataInfo<T> build(List<T> list) {
        PageTableDataInfo<T> rspData = new PageTableDataInfo<>();
        rspData.setRows(list);
        rspData.setTotal(list.size());
        return rspData;
    }

//    public static <T> PageTableDataInfo<T> build(PageInfo<T> page) {
//        PageTableDataInfo<T> rspData = new PageTableDataInfo<>();
//        rspData.setRows(page.getList());
//        rspData.setTotal(page.getTotal());
//        return rspData;
//    }

    public static <T> PageTableDataInfo<T> build() {
        PageTableDataInfo<T> rspData = new PageTableDataInfo<>();
        return rspData;
    }

}
