package com.cxrry.biz.customer.support.domain.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
@ApiModel("工单节点受理人集合")
public class WorkOrderAcceptorConfig implements Serializable {
    @ApiModelProperty("1.指定人 2职位")
    private Integer priorityUserType;
    private List<WorkOrderAcceptorNodeConfig> user;



}
