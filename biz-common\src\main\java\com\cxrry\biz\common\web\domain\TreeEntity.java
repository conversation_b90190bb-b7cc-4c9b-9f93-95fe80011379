package com.cxrry.biz.common.web.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.BaseColumn;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Tree基类
 *
 * <AUTHOR> Li
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TreeEntity<T> extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父菜单名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "父菜单名称")
    @BaseColumn
    private String parentName;

    /**
     * 父菜单ID
     */
    @ApiModelProperty(value = "父菜单ID")
    @BaseColumn
    private Long parentId;

    /**
     * 子部门
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子部门")
    @BaseColumn
    private List<T> children = new ArrayList<>();
}
