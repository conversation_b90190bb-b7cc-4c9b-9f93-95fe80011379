package com.cxrry.biz.sales.operation.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cxrry.biz.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class EarnestMoneyExcel implements Serializable {

    @ExcelProperty(value = "月份", index = 0)
    private String bsMonth;

    @ExcelProperty(value = "销售代理", index = 1)
    private String employeeName;

    @ExcelProperty(value = "销售代理编号", index = 2)
    private String employeeNo;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "保证金", index = 3)
    private BigDecimal payableEarnestMoney = BigDecimal.ZERO;

}
