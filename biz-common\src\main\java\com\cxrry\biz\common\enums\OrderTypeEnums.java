package com.cxrry.biz.common.enums;

import com.ruoyi.common.core.exception.ServiceException;

/**
 * Description: 订单类型
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/5 10:12
 */
public enum OrderTypeEnums {

    /**
     *
     */
    NEW_ORDER((short) 0, "新订单", "order:lock:new_order"),
    CONTINUE_ORDER((short) 1, "续订单", "order:lock:continue_order"),
    RETURN_ORDER((short) 2, "退订单", "order:lock:return_order"),
    INCREASE_ORDER((short) 3, "增订单", "order:lock:increase_order"),
    CHANGE_ORDER((short) 4, "转单", "order:lock:change_order"),
    EXCHANGE_ORDER((short) 5, "换单", "order:lock:exchange_order"),
    GIVE_ORDER((short) 6, "赠送单", "order:lock:give_order"),
    CONTRACT_ORDER((short) 7, "合订单", "order:lock:contract_order"),
    CHANGE_LONG_MILK_ORDER((short) 8, "转单", "order:lock:change_order");

    private short value;
    private String desc;
    private String lockName;

    OrderTypeEnums(short value, String desc, String lockName) {
        this.value = value;
        this.desc = desc;
        this.lockName = lockName;
    }

    public static OrderTypeEnums getType(short orderType) {
        for (OrderTypeEnums orderTypeEnums : values()) {
            if (orderTypeEnums.value == orderType) {
                return orderTypeEnums;
            }
        }
        throw new ServiceException("订单类型不存在");
    }

    public static short getType(String orderType) {
        for (OrderTypeEnums orderTypeEnums : values()) {
            if (orderTypeEnums.desc.equals(orderType)) {
                return orderTypeEnums.value;
            }
        }
        throw new ServiceException("订单类型不存在");
    }

    public short getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public String getLockName() {
        return lockName;
    }
}
