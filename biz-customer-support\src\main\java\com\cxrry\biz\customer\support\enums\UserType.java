package com.cxrry.biz.customer.support.enums;

import com.ruoyi.common.core.utils.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备类型 针对多套 用户体系
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum UserType {

    /**
     * pc端
     */
    SYS_USER("sys_user"),

    /**
     * 员工端
     */
    CXR_STAFF("cxr_staff"),

    /**
     * 客户端
     */
    CXR_CUSTOMER("cxr_customer"),

    /**
     * app端
     */
    APP_USER("app_user"),
    ADMIN("admin");
    public static final String cxr_customer = "cxr_customer";
    public static final String cxr_staff = "cxr_staff";
    public static final String sys_user = "login";
    public static final String login_type = "loginType";
    private final String userType;

    public static UserType getUserType(String str) {
        for (UserType value : values()) {
            if (StringUtils.contains(str, value.getUserType())) {
                return value;
            }
        }
        throw new RuntimeException("'UserType' not found By " + str);
    }
}
