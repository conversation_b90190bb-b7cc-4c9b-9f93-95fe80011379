package com.cxrry.biz.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

public interface OrderEnums {

    String TIKTOK_PROMOTION_STR = "{}-{}";

    @Getter
    @AllArgsConstructor
    enum Channel {
        OFFLINE(0, "线下"),
        WX(1, "微信"),
        TIKTOK(2, "抖音");

        private int value;

        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum PromotionCommission {
        COMMISSION(1, "有"),
        NO_COMMISSION(0, "无");

        private int value;

        private String desc;
    }

    @Getter
    @AllArgsConstructor
    enum AccountingType {

        NORMAL_ACCOUNTING(1, "正常核算"),

        TIKTOK_PROMOTION(2, "抖音推广");

        private int value;

        private String desc;
    }

    static String format(Integer channel,Integer accountingType){
        return StrUtil.format(TIKTOK_PROMOTION_STR,channel,accountingType);
    }
}
