package com.cxrry.biz.customer.support.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 工单列表查询BO
 */
@Data
public class WorkOrderQueryBo extends PageQuery implements Serializable {

	private Long workOrderOperationId;

	private Long workOrderId;
	/**
	 * 当前登录用户ID
	 */
	private Long userId;

	//站点名称
	private String siteName;

	//区域名称
	private String regionName;

	//客户手机号
	private String customerPhone;

	//受理状态
	private Integer processStatus;

	/**
	 * 查询起始日期
	 */
	@NotNull(message = "查询起始日期不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date startDate;

	/**
	 * 查询结束日期
	 */
	@NotNull(message = "查询结束日期不能为空")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endDate;

	/**
	 * 受理人名称查询
	 */
	private String priorityerName;

	/**
	 * 受理人编号查询
	 */
	private String priorityerJobNumber;
	//当前受理中的节点ID
	private Long currentAcceptNodeId;

	@ApiModelProperty(value = "站点ID集合")
	private List<Long> siteIds; ;

	@ApiModelProperty(value = "区域ID集合")
	private List<Long> regionIds; ;

	private Long postId;

	//其他地方调用不需要权限
	@ApiModelProperty(value = "是否需要权限 0：需要权限 1：不需要权限")
	private Integer noPermission;

	@ApiModelProperty(value = "是否统计，用来区分统计页跳转过来，使用不同的日期条件")
	private Integer isReportSearch;


	@ApiModelProperty(value = "投诉对象id")
	private Long complaintObjectId;


}
