//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.cxrry.biz.common.enums;

import cn.hutool.core.util.StrUtil;

public enum PostType {
  COMMON_EMPLOYEE("80", "0", "销售代理"),
  EXPAND_STRIKER("101", "0", "拓展前锋"),
  DIRECTOR("106", "1", "主管"),
  FINANCIAL("102", "1", "财务"),
  GENERATION_DIRECTOR("104", "1", "代主管"),
  DEVELOPMENT_DIRECTOR("105", "1", "拓展主管"),
  DEVELOPMENT_MANAGER("107", "2", "拓展经理"),
  RESERVE_DIRECTOR("103", "1", "储备主管"),
  COMMISSIONER("300", "2", "总监"),
  BIG_DISTRICT_MANAGER("200", "2", "大区经理"),
  REGION_MANAGER("108", "2", "小区经理"),
  CELLULAR_MANAGER("108", "2", "小区经理"),
  REGIONAL_MANAGER("200", "2", "大区经理"),
  MAJORDOMO("300", "2", "总监"),
  GROUP_LEADER("81", "0", "组长"),
  ASSISTANT_MANAGER("205", "1", "助理经理"),
  BIZ_BP("109", "2", "业务BP"),

  CHAIR_MAN("110", "4", "董事长");

  private final String value;
  private final String scope;
  private final String name;

  public static PostType getEnumByValue(String value) {
    if (value == null) {
      return null;
    } else {
      PostType[] postTypeArray = values();

      for(int i = 0; i < postTypeArray.length; ++i) {
        PostType postType = postTypeArray[i];
        if (StrUtil.equals(postType.getValue(), value)) {
          return postType;
        }
      }

      return null;
    }
  }

  public int compare(String another) {
    Integer value = Integer.valueOf(this.getValue());
    Integer anotherValue = Integer.valueOf(another);
    return value - anotherValue;
  }

  public String getValue() {
    return this.value;
  }

  public String getScope() {
    return this.scope;
  }

  public String getName() {
    return this.name;
  }

  private PostType(String value, String scope, String name) {
    this.value = value;
    this.scope = scope;
    this.name = name;
  }
}
