package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 工单受理记录表
 * @TableName work_order_operation
 */
@TableName(value ="work_order_operation",autoResultMap = true)
@Data
public class WorkOrderOperation implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 工单ID
     */
    private Long workOrderId;

    /**
     * 流转节点ID
     */
    private Long workOrderPriorityNodeId;

    /**
     * 转交后的节点ID（发生转交时）
     */
    private Long nextOperationId;

    /**
     * 转交后受理人名称
     */
    private String nextOperator;

    /**
     * 受理状态 0.待处理 1.受理中 2.已完成 3已超时
     */
    private Integer priorityStatus;

    /**
     * 操作类型 1完结 2回复 3转交 4回退 5逾期 6重启
     */
    private Integer operateType;

    /**
     * 节点等级 1 2 3 4 5 6
     */
    private Integer nodeLevel;

    /**
     * 受理人ID类型
     */
    private Integer priorityUserIdType;

    /**
     * 受理人配置的用户ID
     */
    private Long priorityUserId;

    /**
     * 受理人配置的职位ID
     */
    private Long priorityPostId;

    /**
     * 受理人配置的角色ID
     */
    private Long priorityRoleId;

    /**
     * 受理人ID类型对应的名称
     */
    private String priorityIdName;

    /**
     * 受理人ID
     */
    private Long priorityerId;

    /**
     * 受理人名称
     */
    private String priorityerName;

    /**
     * 受理人编号
     */
    private String priorityerNumber;

    /**
     * 受理人站点名称
     */
    private String priorityerSiteName;
    /**
     * 受理人站点编号
     */
    private String priorityerSiteMark;
    /**
     * 受理人站点id
     */
    private Long priorityerSiteId;
    /**
     * 受理人公司名
     */
    private String priorityerDeptName;
    /**
     * 受理人公司id
     */
    private Long priorityerDeptId;
    /**
     * 受理人区域id
     */
    private Long priorityerRegionId;
    /**
     * 受理人区域名
     */
    private String priorityerRegionName;
    /**
     * 受理人区域编号
     */
    private String priorityerRegionMark;
    /**
     * 受理人大区id
     */
    private Long priorityerRootRegionId;
    /**
     * 受理人大区名
     */
    private String priorityerRootRegionName;
    /**
     * 受理人省
     */
    private String priorityerProvince;
    /**
     * 受理人市
     */
    private String priorityerCity;
    /**
     * 受理人区
     */
    private String priorityerArea;







    /**
     * 受理人账号类型：1销售代理 2总部用户
     */
    private Integer acceptedAccount;

    /**
     * 操作人账号类型：1销售代理 2总部用户
     */
    private Integer operateAccount;


    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作时间
     */
    private Date operateTime;

    /**
     * 操作人名称
     */
    private String operatorName;

    /**
     * 操作人在当前站点的最高职位名称（冗余）
     */
    private String operatorMaxJobName;


    /**
     * 回复内容
     */
    private String relyNotes;

    /**
     * Url相对路径，逗号隔开
     */
    @TableField(typeHandler = JsonTypeHandlerConstant.FileUrlConfigHandler.class)
    private List<FileUrlConfig> fileUrl;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人类型(详情见字典)
     */
    private String createByType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    private String updateByType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除人id
     */
    private Long deleteBy;

    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    private String deleteByType;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 流程类型 1真实节点 2虚拟节点
     */
    private Integer processType;


}