package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.customer.support.domain.WorkOrderFlowConfig;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderFlowConfigAddBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderFlowConfigPageBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderFlowConfigUpdateBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderFlowConfigDetilVo;
import com.cxrry.biz.customer.support.service.WorkOrderFlowConfigService;
import com.ruoyi.common.core.domain.R;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
    value = "工单流程配置",
    tags = {"工单流程配置"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderFlowConfig")
public class WorkOrderFlowConfigController {

  private final WorkOrderFlowConfigService workOrderFlowConfigService;

  @ApiOperation("工单流程配置分页")
  @SaCheckPermission("support:workOrderFlowConfig:workOrderFlowConfigPage")
  @PostMapping("/workOrderFlowConfigPage")
  public R<PageTableDataInfo<WorkOrderFlowConfig>> workOrderFlowConfigPage(@RequestBody WorkOrderFlowConfigPageBo bo) {
    return R.ok(workOrderFlowConfigService.workOrderFlowConfigPage(bo));
  }

  @ApiOperation("删除")
  @SaCheckPermission("support:workOrderFlowConfig:delete")
  @GetMapping("/delete")
  public R delete(@RequestParam Long id) {
    return R.ok(workOrderFlowConfigService.delete(id));
  }

  @ApiOperation("状态的起动")
  @SaCheckPermission("support:workOrderFlowConfig:operation")
  @GetMapping("/operation")
  public R operation(@RequestParam Long id,@RequestParam Integer status ) {
    return R.ok(workOrderFlowConfigService.operation(id,status));
  }

  @ApiOperation("新增")
  @SaCheckPermission("support:workOrderFlowConfig:add")
  @PostMapping("/add")
  public R add(@RequestBody WorkOrderFlowConfigAddBo bo) {
    return R.ok(workOrderFlowConfigService.add(bo));
  }


  @ApiOperation("编辑")
  @SaCheckPermission("support:workOrderFlowConfig:update")
  @PostMapping("/update")
  public R update(@RequestBody WorkOrderFlowConfigUpdateBo bo) {
    return R.ok(workOrderFlowConfigService.update(bo));
  }

  @ApiOperation("详情")
  @SaCheckPermission("support:workOrderFlowConfig:queryById")
  @GetMapping("/queryById")
  public R<WorkOrderFlowConfigDetilVo> queryById(@RequestParam Long id) {
    return R.ok(workOrderFlowConfigService.queryById(id));
  }

  @ApiOperation("返回已经选择中id")
  @GetMapping("/queryByCheckedId")
  public R<WorkOrderFlowConfig> queryByCheckedId() {
    return R.ok(workOrderFlowConfigService.queryByCheckedId());
  }



}
