package com.cxrry.biz.sales.operation.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

/**
 * 业绩比例分成考核记录
 * @TableName cxr_divide_ratio_evaluation_record
 */
@TableName(value ="cxr_divide_ratio_evaluation_record")
@Data
public class CxrDivideRatioEvaluationRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * L1晋升L2考核周期开始时间
     */
    private LocalDate oneStageStartTime;

    /**
     * L1晋升L2考核周期结束时间
     */
    private LocalDate oneStageEndTime;

    /**
     * L2考核开始日期
     */
    private LocalDate twoStageStartTime;

    /**
     * L2考核30天到期日期
     */
    private LocalDate twoStageEndTime;

    /**
     * 过小承包30天后开始时间
     */
    private LocalDate passAfterStartTime;

    /**
     * 过小承包30天后结束时间
     */
    private LocalDate passAfterEndTime;

    /**
     * 没有通过小承包30天后开始时间
     */
    private LocalDate notPassAfterStartTime;

    /**
     * 没有通过小承包30天后结束时间
     */
    private LocalDate notPassAfterEndTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}