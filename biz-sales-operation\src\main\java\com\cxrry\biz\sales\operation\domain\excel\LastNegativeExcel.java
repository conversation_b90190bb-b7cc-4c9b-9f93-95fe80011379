package com.cxrry.biz.sales.operation.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cxrry.biz.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class LastNegativeExcel implements Serializable {

    @ExcelProperty(value = "月份")
    private String bsMonth;

    @ExcelProperty(value = "销售代理")
    private String employeeName;

    @ExcelProperty(value = "销售代理编号")
    private String employeeNo;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "上月负分成")
    private BigDecimal lastMonthNegativeActualDivide = BigDecimal.ZERO;

    @ExcelProperty(value = "上月负分成月份")
    private String lastMonth;
}
