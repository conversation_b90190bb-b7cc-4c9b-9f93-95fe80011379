package com.cxrry.biz.sales.operation.enums.leave;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 请假审批状态
 *
 * <AUTHOR>
 * @date 2022/7/12 11:51
 */
@Getter
@AllArgsConstructor
public enum LeaveApprovalStatus {
    /**
     * 已审核
     */
    APPROV("0"),

    /**
     * 待审核
     */
    CHECK_PEND("1"),

    /**
     * 已拒绝
     */
    DENI("2");
    private final String value;

    public static LeaveApprovalStatus getByValue(String value) {
        for (LeaveApprovalStatus item : values()) {
            if (item.value.equals(value)) {
                return item;
            }
        }
        return null;
    }
}
