package com.cxrry.biz.common.enums;

public enum ExecuteStatusEnums {
    /**
     * 0未执行
     */
    exe_status_0(0, "未执行"),
    /**
     * 1已执行
     */
    exe_status_1(1, "已执行");


    private Integer code;
    private String message;

    ExecuteStatusEnums(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer code() {
        return this.code;
    }

    public String message() {
        return this.message;
    }


}