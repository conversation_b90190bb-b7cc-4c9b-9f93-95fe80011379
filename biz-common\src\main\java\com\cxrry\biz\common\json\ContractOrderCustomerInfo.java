package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>Description: 合订单用户信息</p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/22 15:22
 **/
@ApiModel("销售代理")
@Data
public class ContractOrderCustomerInfo implements Serializable {


    /**
     * 订购数量
     */
    @ApiModelProperty(value = "订购数量", required = true)
    private Integer orderQuantity = 0;

    /**
     * 订单总金额
     */
    @ApiModelProperty(value = "订单金额", required = true)
    private BigDecimal amount = BigDecimal.ZERO;

    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty(value = "鲜奶赠送数量")
    private Integer freshMilkGiveQuantity = 0;


    /**
     * 常温奶赠送数量
     */
    @ApiModelProperty(value = "常温奶赠送数量")
    private Integer longMilkGiveQuantity = 0;

    /**
     * 已送数量
     */
    @ApiModelProperty(value = "已送数量")
    private Integer freshMilkSentQuantity = 0;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名", required = true)
    private String name;

    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", required = true)
    private String phone;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "配送地址", required = true)
    private long sysAreaId;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", required = true)
    private String provice;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", required = true)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", required = true)
    private String area;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
    private Long residentialQuartersId;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String residentialQuartersName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    private String adress;

    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id", required = true)
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称", required = true)
    private String siteName;

    /**
     * 配送员id
     */
    @ApiModelProperty(value = "配送员id", required = true)
    private Long distributionId;

    /**
     * 配送员名称
     */
    @ApiModelProperty(value = "配送员名称", required = true)
    private String distributionName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


}
