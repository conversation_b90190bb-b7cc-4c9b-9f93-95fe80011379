package com.cxrry.biz.customer.support.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.json.WorkOrderOrderMenuConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderTotalDurationConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderFlowConfigDetilVo implements Serializable {
  private Long id;
  @ApiModelProperty("流程名称")
  private String processName;

  @ApiModelProperty("流程说明")
  private String processDescription;

  @ApiModelProperty("工单分类id")
  private Long orderMenuId;

  @ApiModelProperty("投诉类型多级名称拼接字符串")
  private String orderMenuRemark;

  @ApiModelProperty("分类")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderOrderMenuConfigHandler.class)
  private List<WorkOrderOrderMenuConfig>orderMenuConfig;
  @ApiModelProperty("总时长的配置")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderTotalDurationConfigHandler.class)
  private List<WorkOrderTotalDurationConfig> totalDurationConfig;

  private List<WorkOrderFlowNodeConfigVo>workOrderButtonConfigVoList;
}
