package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderAddBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderHandleStatusBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderPageBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderUpdateBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderDetailVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderEditDetailVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderPageVo;
import com.cxrry.biz.customer.support.service.WorkOrderService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.redis.annotation.RedisDistributedLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Validated
@Api(
        value = "工单相关",
        tags = {"工单相关"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrder")
public class WorkOrderController {

    private final WorkOrderService workflowOrderService;


    @RedisDistributedLock(value = "'support:workOrder:workOrderReply' + #bo.workNodeId")
    @ApiOperation("工单 回复")
    @SaCheckPermission("support:workOrder:workOrderReply")
    @PostMapping("/workOrderReply")
    public R workOrderReply(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.workOrderReply(bo,2));
    }

    @ApiOperation("重新开启")
    @SaCheckPermission("support:workOrder:restartWorkOrder")
    @GetMapping("/restartWorkOrder")
    public R restartWorkOrder(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workflowOrderService.restartWorkOrder(id));
    }
    @RedisDistributedLock(value = "'support:workOrder:finishedWorkOrder' + #bo.workNodeId")
    @ApiOperation("完结工单")
    @SaCheckPermission("support:workOrder:finishedWorkOrder")
    @PostMapping("/finishedWorkOrder")
    public R finishedWorkOrder(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.finishedWorkOrder(bo,2));
    }

    @RedisDistributedLock(value = "'support:workOrder:overdueCompletion' + #bo.workOrderId")
    @ApiOperation("逾期完结工单")
    @SaCheckPermission("support:workOrder:overdueCompletion")
    @PostMapping("/overdueCompletion")
    public R overdueCompletion(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.overdueCompletion(bo));
    }

    @RedisDistributedLock(value = "'support:workOrder:stafffinishedWorkOrder' + #bo.workNodeId")
    @ApiOperation("配送端 完结工单")
    @PostMapping("/stafffinishedWorkOrder")
    public R stafffinishedWorkOrder(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.finishedWorkOrder(bo,1));
    }

    @RedisDistributedLock(value = "'support:workOrder:staffWorkOrderReply' + #bo.workNodeId")
    @ApiOperation("配送端 工单 回复")
    @PostMapping("/staffWorkOrderReply")
    public R staffWorkOrderReply(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.workOrderReply(bo,1));
    }


    @ApiOperation("配送端工单转交")
//    @SaCheckPermission("support:workOrder:staffTransferWorkOrder")
    @PostMapping("/staffTransferWorkOrder")
    public R staffTransferWorkOrder(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.staffTransferWorkOrder(bo));
    }

    @ApiOperation("工单转交")
    @SaCheckPermission("support:workOrder:transferWorkOrder")
    @PostMapping("/transferWorkOrder")
    public R transferWorkOrder(@RequestBody WorkOrderHandleStatusBo bo) {
        return R.ok(workflowOrderService.transferWorkOrder(bo,2));
    }
    @ApiOperation("工单删除")
    @SaCheckPermission("support:workOrder:deleteWorkOrder")
    @GetMapping("/deleteWorkOrder")
    public R deleteWorkOrder(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workflowOrderService.deleteWorkOrder(id));
    }

    @ApiOperation("工单新增")
    @SaCheckPermission("support:workOrder:workOrderAdd")
    @PostMapping("/workOrderAdd")
    public R workOrderAdd(@RequestBody WorkOrderAddBo bo) {
        return R.ok(workflowOrderService.workOrderAdd(bo));
    }

    @ApiOperation("工单编辑")
    @SaCheckPermission("support:workOrder:workOrderUpdate")
    @PostMapping("/workOrderUpdate")
    public R workOrderUpdate(@RequestBody WorkOrderUpdateBo bo) {
        return R.ok(workflowOrderService.workOrderUpdate(bo));
    }

    @ApiOperation("工单分页")
    @SaCheckPermission("support:workOrder:workOrderPage")
    @PostMapping("/workOrderPage")
    public R<PageTableDataInfo<WorkOrderPageVo>> workOrderPage(@RequestBody WorkOrderPageBo bo) {
        return R.ok(workflowOrderService.workOrderPage(bo));
    }

    @ApiOperation("工单分页导出")
    @PostMapping("/export")
    public void workOrderPage(@RequestBody WorkOrderPageBo bo, HttpServletResponse response) throws IOException {
        this.workflowOrderService.export(bo,response);
    }

    @ApiOperation("工单展示详情")
    @SaCheckPermission("support:workOrder:queryById")
    @GetMapping("/queryById")
    public R<WorkOrderDetailVo> queryById(
        @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workflowOrderService.queryById(id));
    }

    @ApiOperation("工单去编辑的详情")
    @SaCheckPermission("support:workOrder:getWorkOrderEditDetails")
    @GetMapping("/getWorkOrderEditDetails")
    public R<WorkOrderEditDetailVo> getWorkOrderEditDetails(
        @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workflowOrderService.getWorkOrderEditDetails(id));
    }
}
