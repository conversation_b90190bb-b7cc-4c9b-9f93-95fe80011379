<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cxrry.biz.sales.operation.mapper.CxrDivideRatioEvaluationRecordMapper">

    <resultMap id="BaseResultMap" type="com.cxrry.biz.sales.operation.domain.CxrDivideRatioEvaluationRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="oneStageStartTime" column="one_stage_start_time" jdbcType="DATE"/>
            <result property="oneStageEndTime" column="one_stage_end_time" jdbcType="DATE"/>
            <result property="twoStageStartTime" column="two_stage_start_time" jdbcType="DATE"/>
            <result property="twoStageEndTime" column="two_stage_end_time" jdbcType="DATE"/>
            <result property="passAfterStartTime" column="pass_after_start_time" jdbcType="DATE"/>
            <result property="passAfterEndTime" column="pass_after_end_time" jdbcType="DATE"/>
            <result property="notPassAfterStartTime" column="not_pass_after_start_time" jdbcType="DATE"/>
            <result property="notPassAfterEndTime" column="not_pass_after_end_time" jdbcType="DATE"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,one_stage_start_time,one_stage_end_time,
        two_stage_start_time,two_stage_end_time,pass_after_start_time,
        pass_after_end_time,not_pass_after_start_time,not_pass_after_end_time
    </sql>
</mapper>
