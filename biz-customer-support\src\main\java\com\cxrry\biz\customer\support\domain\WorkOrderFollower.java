package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 工单关注人
 * @TableName work_order_trigger_follower
 */
@TableName(value ="work_order_follower")
@Data
public class WorkOrderFollower implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 工单id
     */
    private Long workOrderId;

    /**
     * 对应类型userid
     */
    private Long followerUserId;
    /**
     * 对应类型职位id
     */
    private Long followerPostId;
    /**
     * 对应类型角色id
     */
    private Long followerRoleId;

    /**
     * 对应类型名称
     */
    private String followerName;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人类型(详情见字典)
     */
    private String createByType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    private String updateByType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除人id
     */
    private Long deleteBy;

    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    private String deleteByType;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}