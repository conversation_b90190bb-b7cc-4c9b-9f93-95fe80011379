package com.cxrry.biz.sales.operation.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cxrry.biz.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class MarketingSubsidyExcel implements Serializable {

    @ExcelProperty(value = "月份")
    private String bsMonth;

    @ExcelProperty(value = "销售代理")
    private String employeeName;

    @ExcelProperty(value = "销售代理编号")
    private String employeeNo;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "杂费补贴")
    private BigDecimal extrasSubsidyFee = BigDecimal.ZERO;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "总补贴费用(元)")
    private BigDecimal subsidyFee = BigDecimal.ZERO;


}
