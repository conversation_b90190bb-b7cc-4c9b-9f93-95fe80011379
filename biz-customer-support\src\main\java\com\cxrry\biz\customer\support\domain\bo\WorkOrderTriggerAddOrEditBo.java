package com.cxrry.biz.customer.support.domain.bo;

import com.cxrry.biz.customer.support.domain.dto.TriggerActiveDto;
import com.cxrry.biz.customer.support.domain.dto.TriggerConditionDto;
import com.ruoyi.common.core.validate.EditGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class WorkOrderTriggerAddOrEditBo implements Serializable {
    @NotNull(
            message = "主键不能为空",
            groups = {EditGroup.class})
    private Long id;

    private String triggerEvent;

    /**
     * 判断类型1工单创建2工单修改
     */
    private Integer judgmentType;
    // 前置条件
    private List<TriggerConditionDto> triggerScopeCondition;
    // 触发条件
    private List<TriggerConditionDto> triggerConditions;

    private List<TriggerActiveDto> triggerActiveDtos;


}
