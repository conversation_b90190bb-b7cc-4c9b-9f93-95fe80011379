package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.WorkOrderTrigger;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderMenuAddBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderMenuUpdateBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderTriggerAddOrEditBo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderTriggerPageBo;
import com.cxrry.biz.customer.support.service.WorkOrderTriggerService;
import com.cxrry.common.validate.AddGroup;
import com.cxrry.common.validate.EditGroup;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderTrigger")
public class WorkOrderTriggerController {


    private final WorkOrderTriggerService workOrderTriggerService;



    @ApiOperation("工单列表")
    @SaCheckPermission("support:workOrderTrigger:page")
    @PostMapping("/page")
    public R<PageTableDataInfo<WorkOrderTrigger>> page(
            @RequestBody WorkOrderTriggerPageBo bo) {
        return R.ok(workOrderTriggerService.workOrderTriggerPage(bo));
    }

    @ApiOperation("工单详情")
    @SaCheckPermission("support:workOrderTrigger:detail")
    @GetMapping("/detail")
    public R detail(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.detail(id));
    }


    @ApiOperation("工单启用")
    @SaCheckPermission("support:workOrderTrigger:activate")
    @GetMapping("/activate")
    public R activate(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.triggerActivate(id));
    }

    @ApiOperation("工单停用")
    @SaCheckPermission("support:workOrderTrigger:blockUp")
    @GetMapping("/blockUp")
    public R blockUp(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.triggerBlockUp(id));
    }
    @ApiOperation("工单删除")
    @SaCheckPermission("support:workOrderTrigger:deleteWorkOrderTrigger")
    @GetMapping("/deleteWorkOrderTrigger")
    public R deleteWorkOrderTrigger(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.deleteWorkOrderTrigger(id));
    }


    @ApiOperation("工单条件删除")
    @GetMapping("/deleteTriggerCondition")
    public R deleteTriggerCondition(
            @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.deleteTriggerCondition(id));
    }


    @ApiOperation("工单条件删除")
    @GetMapping("/deleteTriggerActive")
    public R deleteTriggerAction(
        @ApiParam(value = "主键", required = true) @NotNull(message = "主键不能为空") @RequestParam Long id) {
        return R.ok(workOrderTriggerService.deleteTriggerActive(id));
    }
    @ApiOperation("新增")
    @SaCheckPermission("support:workOrderTrigger:add")
    @PostMapping("/add")
    public R add(@Validated(AddGroup.class) @RequestBody WorkOrderTriggerAddOrEditBo bo) {
        return R.ok(workOrderTriggerService.addTrigger(bo));
    }

    @ApiOperation("修改")
    @SaCheckPermission("support:workOrderTrigger:update")
    @PostMapping("/update")
    public R update( @Validated(EditGroup.class) @RequestBody WorkOrderTriggerAddOrEditBo bo) {
        return R.ok(workOrderTriggerService.updateTrigger(bo));
    }

}
