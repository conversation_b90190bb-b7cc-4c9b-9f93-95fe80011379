package com.cxrry.biz.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 客户在小程序的记录类型 ENUM
 */
@Getter
@AllArgsConstructor
public enum CustomerXcxRecordTypeEnums {

    /**
     * 1-小程序浏览记录
     */
    MALL_FOOTPRINT(1, "小程序浏览记录"),
    /**
     * 2-小程序收藏记录
     */
    MALL_COLLECT(2, "小程序收藏记录"),
    /**
     * 3-小程序当前加入购物车商品
     */
    MALL_CART(3, "小程序当前加入购物车商品"),
    ;


    private final Integer code;
    private final String message;


    public static CustomerXcxRecordTypeEnums getByCode(Integer code){
        if(code != null){
            for (CustomerXcxRecordTypeEnums xcxRecordType : CustomerXcxRecordTypeEnums.values()){
                if(xcxRecordType.getCode().equals(code)){
                    return xcxRecordType;
                }
            }for (CustomerXcxRecordTypeEnums xcxRecordType : CustomerXcxRecordTypeEnums.values()){
                if(xcxRecordType.getCode().equals(code)){
                    return xcxRecordType;
                }
            }
        }
        return null;
    }

}
