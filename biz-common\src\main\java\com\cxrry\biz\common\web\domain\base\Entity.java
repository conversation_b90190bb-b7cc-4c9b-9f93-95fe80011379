package com.cxrry.biz.common.web.domain.base;

import com.ruoyi.common.core.annotation.BaseColumn;
import com.ruoyi.common.core.exception.ServiceException;
import java.io.Serializable;
import java.lang.reflect.Field;

/**
 * 基类的接口
 */
public interface Entity extends Serializable {

    /**
     * 清空当前对象的所有基础字段的值
     *
     * @param classes 基础字段所在的类字节码
     * @throws IllegalAccessException
     */
    default void clearAllBaseColumns(Class<? extends Entity> classes) {
        try {
            Field[] declaredFields = classes.getDeclaredFields();
            for (int i = 0; i < declaredFields.length; i++) {
                Field field = declaredFields[i];
                BaseColumn baseColumn = field.getAnnotation(BaseColumn.class);
                if (null != baseColumn && baseColumn.isBaseColumn()) {
                    field.setAccessible(true);
                    field.set(this, null);
                }
            }
        } catch (Exception e) {
            throw new ServiceException("系统内部错误，请联系管理员：" + e.getMessage());
        }
    }
}
