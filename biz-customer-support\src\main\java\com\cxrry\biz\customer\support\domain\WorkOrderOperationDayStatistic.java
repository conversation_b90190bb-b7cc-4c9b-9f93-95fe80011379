package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName work_order_operation_day_statistic
 */
@TableName(value ="work_order_operation_day_statistic")
@Data
public class WorkOrderOperationDayStatistic implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 统计时间
     */
    private LocalDate distributionDate;

    /**
     * 销售代理id
     */
    private Long employeeId;

    /**
     * 销售代理名称
     */
    private String employeeName;

    /**
     * 销售代理工号
     */
    private String jobNumber;

    /**
     * 站点id
     */
    private Long siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点编号
     */
    private String siteMark;

    /**
     * 区域id
     */
    private Long cxrRegionId;

    /**
     * 区域名称
     */
    private String cxrRegionName;

    /**
     * 下单大区id（下单区域的父级节点id）
     */
    private Long cxrParentRegionId;

    /**
     * 下单大区名称（下单区域的父级节点名称）
     */
    private String cxrParentRegionName;

    /**
     * 新增工单数量
     */
    private Integer addWorkOrderSum;

    /**
     * 处理中工单数量
     */
    private Integer processingWorkOrderSum;

    /**
     * 已完成工单数量
     */
    private Integer completedWorkOrderSum;

    /**
     * 超时工单数量
     */
    private Integer overtimeWorkOrderSum;

    /**
     * 删除状态<详情见字典>
     */
    private String deleteStatus;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}