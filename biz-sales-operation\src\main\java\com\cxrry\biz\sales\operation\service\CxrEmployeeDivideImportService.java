package com.cxrry.biz.sales.operation.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

public interface CxrEmployeeDivideImportService{
    String importNegativeExpense(String month, MultipartFile file) throws IOException;

    String importAccommodationExpense(String month, MultipartFile file) throws IOException;

    String importPlatformExpense(String month, MultipartFile file) throws IOException;

    String importEarnestMoneyExpense(String month, MultipartFile file,Integer earnestMoneyType) throws IOException;

    String importRefundExpense(String month, MultipartFile file) throws IOException;

    String importSubsidyExpense(String month, MultipartFile file) throws IOException;

    String importMarketingExpense(String month, MultipartFile file) throws IOException;
}
