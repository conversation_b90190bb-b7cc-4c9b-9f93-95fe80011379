package com.cxrry.biz.sales.operation.domain.dto;

import cn.hutool.core.collection.ListUtil;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 延期计算结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DelayCalculationResult {
    
    /**
     * 延期天数
     */
    private Integer delayDays;
    
    /**
     * 新的结束日期
     */
    private Date newEndDate;
    
    /**
     * 计算原因说明
     */
    private String calculationReason;
    
    /**
     * 详细的延期过程描述
     */
    private String delayProcessDescription;
    
    /**
     * 使用的记录ID列表
     */
    private List<Long> usedRecordIds;
    
    /**
     * 是否有延期
     */
    private Boolean hasDelay;
    
    /**
     * 个人请假在周期内的天数
     */
    private Integer personalLeaveDaysInCycle;
    
    /**
     * 公司放假在周期内的天数
     */
    private Integer companyHolidayDaysInCycle;
    
    /**
     * 是否存在时间重叠
     */
    private Boolean hasOverlap;
    
    /**
     * 具体的延期日期集合（用于日期去重）
     */
    private Set<LocalDate> delayDates;
    
    /**
     * 创建无延期的结果
     */
    public static DelayCalculationResult noDelay() {
        return DelayCalculationResult.builder()
                .delayDays(0)
                .hasDelay(false)
                .calculationReason("无需延期")
                .delayProcessDescription("经检查，该考核周期无有效的延期记录，无需延期处理")
                .delayDates(new HashSet<>())
                .build();
    }
    
    /**
     * 创建手动调整的结果
     */
    public static DelayCalculationResult manualAdjust(Integer delayDays, Date newEndDate, Long recordId) {
        return DelayCalculationResult.builder()
                .delayDays(delayDays)
                .newEndDate(newEndDate)
                .hasDelay(delayDays > 0)
                .calculationReason("手动调整延期")
                .delayProcessDescription(String.format("检测到手动调整记录(ID:%d)，按手动调整优先原则，延期%d天", recordId, delayDays))
                .delayDates(new HashSet<>()) // 手动调整暂时不记录具体日期
                .usedRecordIds(ListUtil.of(recordId))
                .build();
    }
}
