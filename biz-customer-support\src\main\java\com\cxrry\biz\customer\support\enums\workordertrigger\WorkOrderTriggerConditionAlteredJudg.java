package com.cxrry.biz.customer.support.enums.workordertrigger;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum WorkOrderTriggerConditionAlteredJudg {
		ALREADY_CHANGED(1, "已变更"),
		CHANGED_TO(2, "更改为"),
		CHANGED_FROM(3, "更改自"),
		NOT_CHANGED(4, "未更改为"),
		NOT_CHANGED_FROM(5, "未更改自");

		private final Integer code;
		private final String description;
}