package com.cxrry.biz.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class CommonFieldDoWithUtils {

    public static void fieldDoWithToNull(Object object,Map<String, Object> FieldsValueMap){
        if(Objects.isNull(object) || Objects.isNull(FieldsValueMap)) return;
        FieldsValueMap.forEach((privateFieldName,v)->{
            Field field = ReflectionUtils.findField(object.getClass(), privateFieldName);
            if(Objects.nonNull(field)){
                Class<?> fieldType = field.getType();
                String typeName = fieldType.getTypeName();
                if("java.math.BigDecimal".equals(typeName)){
                    // 设置字段可访问
                    ReflectionUtils.makeAccessible(field);
                    Object value = ReflectionUtils.getField(field, object);
                    // 修改字段值
                    if(Objects.nonNull(value) && new BigDecimal(value.toString()).compareTo(BigDecimal.ZERO)==0){
                        ReflectionUtils.setField(field, object, null);
                    }
                }
            }
        });
    }
    public static Map<String, Object> exportFieldDoWithToStr(Object object,Map<String, Object> FieldsValueMap){
        if(Objects.isNull(object) || Objects.isNull(FieldsValueMap)) return FieldsValueMap;
        FieldsValueMap.forEach((privateFieldName,v)->{
            Field field = ReflectionUtils.findField(object.getClass(), privateFieldName);
            if(Objects.nonNull(field)){
                ReflectionUtils.makeAccessible(field);
                Object value = ReflectionUtils.getField(field, object);
                if(Objects.isNull(value) || StringUtils.isBlank(value.toString())){
                    FieldsValueMap.put(privateFieldName,"");
                }else{
                    if(value instanceof Number && new BigDecimal(value.toString()).compareTo(BigDecimal.ZERO)==0){
                        FieldsValueMap.put(privateFieldName,"");
                    }
                }
            }
        });
        return FieldsValueMap;
    }
}
