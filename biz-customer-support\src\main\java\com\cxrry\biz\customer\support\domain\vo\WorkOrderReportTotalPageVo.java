package com.cxrry.biz.customer.support.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WorkOrderReportTotalPageVo {
  /**
   * 受理人ID
   */
  private Long priorityerId;

  /**
   * 受理人名称
   */
  @ExcelProperty(value = "销售代理", index = 9)
  private String priorityerName;

  /**
   * 受理人编号
   */
  @ExcelProperty(value = "代理编号", index = 10)
  private String priorityerNumber;

  /**
   * 受理人站点名称
   */
  @ExcelProperty(value = "站点", index = 7)
  private String priorityerSiteName;
  /**
   * 受理人站点编号
   */
  @ExcelProperty(value = "站点编号", index = 8)
  private String priorityerSiteMark;
  /**
   * 受理人站点id
   */
  private Long priorityerSiteId;
  /**
   * 受理人公司名
   */
  @ExcelProperty(value = "公司", index = 0)
  private String priorityerDeptName;
  /**
   * 受理人公司id
   */
  private Long priorityerDeptId;
  /**
   * 受理人区域id
   */
  private Long priorityerRegionId;
  /**
   * 受理人区域名
   */
  @ExcelProperty(value = "区域", index = 2)
  private String priorityerRegionName;
  @ExcelProperty(value = "区域编号", index = 3)
  private String priorityerRegionMark;
  /**
   * 受理人大区id
   */
  private Long priorityerRootRegionId;
  /**
   * 受理人大区名
   */
  @ExcelProperty(value = "大区", index = 1)
  private String priorityerRootRegionName;
  /**
   * 受理人省
   */
  @ExcelProperty(value = "省", index = 4)
  private String priorityerProvince;
  /**
   * 受理人市
   */
  @ExcelProperty(value = "市", index = 5)
  private String priorityerCity;
  /**
   * 受理人区
   */
  @ExcelProperty(value = "区", index = 6)
  private String priorityerArea;

  //工单新增数
  @ExcelProperty(value = "新增工单数量", index = 12)
  private Long addCount=0L;

  //工单完结数
  @ExcelProperty(value = "已完成工单数量", index = 14)
  private Long finishCount=0L;
  //工单超时数
  @ExcelProperty(value = "超时工单数", index = 15)
  private Long overTimeCount=0L;
  //工单逾期数
  @ExcelProperty(value = "逾期工单数量", index = 16)
  private Long overdueCount=0L;
  //工单重启数
  @ExcelProperty(value = "重新打开工单数量", index = 17)
  private Long restartCount=0L;
  //工单处理中数量
  @ExcelProperty(value = "处理中工单数量", index = 13)
  private Long proccessingCount=0L;
  //关联组合key
  private String groupKey;


  @ExcelProperty(value = "状态", index = 11)
  @ApiModelProperty(value = "合作状态")
  private String occupationStatus;

}
