package com.cxrry.biz.api.client;


import com.cxrry.biz.common.vo.RuleConditionVo;

import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface RemoteRuleConditionEditTaskService {

	/**
	 * 添加考核周期
	 */
	List<RuleConditionVo> getNewOrOldRuleConditionBy(Collection<Long> oldRuleSetIds, String ruleSetCode,
													 List<Long> ruleGroupIds, List<Integer> stageIdList, Date accessStartDate, Date runDate);
}
