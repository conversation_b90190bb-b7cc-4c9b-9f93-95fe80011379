
# 销售代理考核周期调整逻辑梳理 (简化版)

## 1. 核心调整规则

### 1.1 调整类型分类
```
调整类型
├── 个人请假 (PERSONAL_LEAVE)
├── 公司放假 (COMPANY_HOLIDAY)  
└── 手动调整 (MANUAL_ADJUST)
```

### 1.2 简化处理规则

```
延期记录处理决策树
├── 查询该周期所有有效延期记录
├── 按类型分组，每组取最新记录
│   ├── 个人请假组 → 取最新一条
│   ├── 公司放假组 → 取最新一条
│   └── 手动调整组 → 取最新一条
└── 最终延期计算
    ├── 如果存在手动调整
    │   └── 只取手动调整，忽略其他
    └── 如果不存在手动调整
        ├── 只有个人请假 → 直接使用
        ├── 只有公司放假 → 直接使用
        └── 个人+公司都存在 → 取最大交集延期
```

## 2. 最大交集延期计算逻辑

### 2.1 交集计算规则
```
最大交集延期计算
├── 输入参数
│   ├── 个人请假延期记录 (开始日期A, 结束日期A)
│   ├── 公司放假延期记录 (开始日期B, 结束日期B)
│   └── 考核周期起始日期 (cycleStartDate)
│   └── 考核周期结束日期 (cycleEndDate)
├── 计算步骤
│   ├── 1. 找出在考核周期内的实际无法工作时间
│   │   ├── 个人请假在周期内的天数 = 计算请假与周期的交集天数
│   │   ├── 公司放假在周期内的天数 = 计算放假与周期的交集天数
│   │   └── 重叠部分只算一次
│   ├── 2. 计算重叠交集
│   │   ├── 交集开始 = max(开始日期A, 开始日期B, cycleStartDate)
│   │   ├── 交集结束 = min(结束日期A, 结束日期B, cycleEndDate)
│   │   └── if (交集开始 <= 交集结束) 则有重叠
│   └── 3. 计算总延期天数
│       ├── 如果有重叠: 延期天数 = max(个人请假在周期内天数, 公司放假在周期内天数)
│       └── 如果无重叠: 延期天数 = 个人请假在周期内天数 + 公司放假在周期内天数
└── 输出结果
    └── 最终延期天数
```

### 2.2 交集场景示例
```
场景1: 完全重叠
考核周期: 2024-01-01 ~ 2024-01-31
个人请假: 2024-01-10 ~ 2024-01-15 (6天)
公司放假: 2024-01-12 ~ 2024-01-18 (7天)
→ 个人请假在周期内: 6天 (2024-01-10 ~ 2024-01-15)
→ 公司放假在周期内: 7天 (2024-01-12 ~ 2024-01-18)
→ 重叠部分: 4天 (2024-01-12 ~ 2024-01-15)
→ 最终延期: max(6, 7) = 7天 ← 取最大值

场景2: 部分重叠
考核周期: 2024-01-01 ~ 2024-01-31
个人请假: 2024-01-10 ~ 2024-01-15 (6天)
公司放假: 2024-01-13 ~ 2024-01-20 (8天)
→ 个人请假在周期内: 6天 (2024-01-10 ~ 2024-01-15)
→ 公司放假在周期内: 8天 (2024-01-13 ~ 2024-01-20)
→ 重叠部分: 3天 (2024-01-13 ~ 2024-01-15)
→ 最终延期: max(6, 8) = 8天 ← 取最大值

场景3: 无重叠
考核周期: 2024-01-01 ~ 2024-01-31
个人请假: 2024-01-10 ~ 2024-01-12 (3天)
公司放假: 2024-01-15 ~ 2024-01-18 (4天)
→ 个人请假在周期内: 3天
→ 公司放假在周期内: 4天
→ 重叠部分: 0天
→ 最终延期: 3 + 4 = 7天 ← 累加

场景4: 跨越考核周期边界
考核周期: 2024-01-10 ~ 2024-02-10
个人请假: 2024-01-05 ~ 2024-01-15 (11天，但只有6天在周期内)
公司放假: 2024-01-12 ~ 2024-02-15 (35天，但只有30天在周期内)
→ 个人请假在周期内: 6天 (2024-01-10 ~ 2024-01-15)
→ 公司放假在周期内: 30天 (2024-01-12 ~ 2024-02-10)
→ 重叠部分: 4天 (2024-01-12 ~ 2024-01-15)
→ 最终延期: max(6, 30) = 30天 ← 取最大值

场景5: 完全在周期外
考核周期: 2024-01-10 ~ 2024-02-10
个人请假: 2024-01-01 ~ 2024-01-05 (5天，但0天在周期内)
→ 个人请假在周期内: 0天
→ 最终延期: 0天 ← 不延期
```

## 3. 处理流程图

### 3.1 主流程
```mermaid
graph TD
    A[查询周期延期记录] --> B[按类型分组]
    B --> C[各组取最新记录]
    C --> D{是否存在手动调整?}
    D -->|是| E[只取手动调整]
    D -->|否| F{个人+公司都存在?}
    F -->|是| G[计算最大交集延期]
    F -->|否| H[直接使用单一类型]
    E --> I[应用延期]
    G --> I
    H --> I
    I --> J[更新周期记录]
```

### 3.2 最大交集计算子流程
```mermaid
graph TD
    A[个人请假记录] --> C[计算与考核周期的交集天数]
    B[公司放假记录] --> D[计算与考核周期的交集天数]
    C --> E[个人请假在周期内天数]
    D --> F[公司放假在周期内天数]
    E --> G{是否有时间重叠?}
    F --> G
    G -->|是| H[取最大天数]
    G -->|否| I[累加天数]
    H --> J[返回延期结果]
    I --> J
```

## 4. 数据结构设计

### 4.1 延期记录查询结果
```java
// 查询结果分组结构
Map<AdjustSourceType, CxrEmployeeAccessCycleRecordLog> latestRecords = {
    PERSONAL_LEAVE: 最新个人请假记录,
    COMPANY_HOLIDAY: 最新公司放假记录,
    MANUAL_ADJUST: 最新手动调整记录
}
```

### 4.2 延期计算结果
```java
public class DelayCalculationResult {
    private Integer delayDays;           // 延期天数
    private Date newEndDate;             // 新的结束日期
    private String calculationReason;    // 计算原因说明
    private List<Long> usedRecordIds;    // 使用的记录ID列表
}
```

## 5. 核心算法伪代码

### 5.1 主处理逻辑
```java
public DelayCalculationResult calculateDelay(Long employeeId, Long cycleId) {
    // 1. 查询所有有效延期记录
    List<CxrEmployeeAccessCycleRecordLog> allRecords = queryActiveRecords(employeeId, cycleId);
    
    // 2. 按类型分组，取最新
    Map<AdjustSourceType, CxrEmployeeAccessCycleRecordLog> latestByType = 
        groupByTypeAndGetLatest(allRecords);
    
    // 3. 应用简化规则
    if (latestByType.containsKey(MANUAL_ADJUST)) {
        // 只取手动调整
        return useManualAdjustOnly(latestByType.get(MANUAL_ADJUST));
    }
    
    boolean hasPersonal = latestByType.containsKey(PERSONAL_LEAVE);
    boolean hasCompany = latestByType.containsKey(COMPANY_HOLIDAY);
    
    if (hasPersonal && hasCompany) {
        // 计算最大交集
        return calculateMaxIntersection(
            latestByType.get(PERSONAL_LEAVE),
            latestByType.get(COMPANY_HOLIDAY),
            cycleStartDate,
            cycleEndDate
        );
    } else if (hasPersonal) {
        return usePersonalLeaveOnly(latestByType.get(PERSONAL_LEAVE));
    } else if (hasCompany) {
        return useCompanyHolidayOnly(latestByType.get(COMPANY_HOLIDAY));
    }
    
    return noDelayResult();
}
```

### 5.2 最大交集计算
```java
private DelayCalculationResult calculateMaxIntersection(
    CxrEmployeeAccessCycleRecordLog personalRecord,
    CxrEmployeeAccessCycleRecordLog companyRecord,
    Date cycleStartDate,
    Date cycleEndDate) {
    
    // 计算个人请假在考核周期内的天数
    int personalDaysInCycle = calculateDaysInCycle(
        personalRecord.getOriginalAssessmentStartDate(),
        personalRecord.getOriginalAssessmentEndDate(),
        cycleStartDate,
        cycleEndDate
    );
    
    // 计算公司放假在考核周期内的天数
    int companyDaysInCycle = calculateDaysInCycle(
        companyRecord.getOriginalAssessmentStartDate(),
        companyRecord.getOriginalAssessmentEndDate(),
        cycleStartDate,
        cycleEndDate
    );
    
    // 检查是否有重叠
    boolean hasOverlap = checkOverlap(
        personalRecord.getOriginalAssessmentStartDate(),
        personalRecord.getOriginalAssessmentEndDate(),
        companyRecord.getOriginalAssessmentStartDate(),
        companyRecord.getOriginalAssessmentEndDate(),
        cycleStartDate,
        cycleEndDate
    );
    
    int finalDelayDays;
    String reason;
    
    if (hasOverlap) {
        // 有重叠，取最大值
        finalDelayDays = Math.max(personalDaysInCycle, companyDaysInCycle);
        reason = "个人请假与公司放假有重叠，取最大延期天数";
    } else {
        // 无重叠，累加
        finalDelayDays = personalDaysInCycle + companyDaysInCycle;
        reason = "个人请假与公司放假无重叠，累加延期天数";
    }
    
    Date newEndDate = DateUtils.addDays(cycleEndDate, finalDelayDays);
    
    return DelayCalculationResult.builder()
        .delayDays(finalDelayDays)
        .newEndDate(newEndDate)
        .calculationReason(reason)
        .usedRecordIds(Arrays.asList(personalRecord.getId(), companyRecord.getId()))
        .build();
}

/**
 * 计算某个时间段在考核周期内的天数
 */
private int calculateDaysInCycle(Date startDate, Date endDate, Date cycleStart, Date cycleEnd) {
    Date effectiveStart = Collections.max(Arrays.asList(startDate, cycleStart));
    Date effectiveEnd = Collections.min(Arrays.asList(endDate, cycleEnd));
    
    if (effectiveStart.compareTo(effectiveEnd) <= 0) {
        return DateUtils.daysBetween(effectiveStart, effectiveEnd) + 1;
    }
    return 0;
}
```

## 6. 优势分析

### 6.1 规则简化优势
```
简化后的优势
├── 逻辑清晰易懂
├── 减少复杂的优先级计算
├── 手动调整具有绝对优先权
├── 个人+公司的交集处理明确
└── 便于测试和维护
```

### 6.2 实现复杂度降低
```
复杂度对比
├── 原方案: O(n²) 多重嵌套判断
└── 新方案: O(n) 线性处理 + 简单分组
```

## 7. 测试用例设计

### 7.1 基础场景测试
```
测试场景覆盖
├── 只有个人请假
├── 只有公司放假
├── 只有手动调整
├── 个人+公司 (有交集)
├── 个人+公司 (无交集)
├── 个人+公司+手动 (手动优先)
├── 多条同类型记录 (取最新)
└── 考虑考核起始日期的边界情况
```

### 7.2 边界条件测试
```
边界条件
├── 延期记录为空
├── 延期日期早于考核起始日期
├── 延期日期等于考核起始日期
├── 同一天的多条记录
└── 跨月的延期计算
```
