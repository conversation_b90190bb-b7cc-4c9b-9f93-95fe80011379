package com.cxrry.biz.customer.support.controller.staff;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderQueryBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderStaffDetailVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderStaffListVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderTransferSelectListVo;
import com.cxrry.biz.customer.support.service.WorkOrderStaffService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
        value = "工单相关",
        tags = {"工单相关"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/staff/workOrder")
public class StaffWorkOrderController {

    private final WorkOrderStaffService workOrderStaffService;


    @ApiOperation("配送端-我受理的")
    @PostMapping("/myAccept")
    public R<Page<WorkOrderStaffListVo>> myAcceptWorkOrder(@RequestBody WorkOrderQueryBo bo) {
        return R.ok(workOrderStaffService.myAcceptWorkOrder(bo));
    }

    @ApiOperation("配送端-我关注的")
    @PostMapping("/myFollower")
    public R<Page<WorkOrderStaffListVo>> myFollowerWorkOrder(@RequestBody WorkOrderQueryBo bo) {
        return R.ok(workOrderStaffService.myFollowerWorkOrder(bo));
    }

    @ApiOperation("配送端-我管辖的")
    @PostMapping("/myManage")
    public R<Page<WorkOrderStaffListVo>> myManageWorkOrder(@RequestBody WorkOrderQueryBo bo) {
        return R.ok(workOrderStaffService.myManageWorkOrder(bo));
    }

    @ApiOperation("配送端-工单详情")
    @PostMapping("/detail")
    public R<WorkOrderStaffDetailVo> detail(@RequestBody WorkOrderQueryBo bo) {
        return R.ok(workOrderStaffService.detail(bo));
    }

    @ApiOperation("配送端-转交选择用户接口")
    @PostMapping("/transferSelectList")
    public R<Page<WorkOrderTransferSelectListVo>> transferSelectList(@RequestBody WorkOrderQueryBo bo) {
        return R.ok(workOrderStaffService.transferSelectList(bo));
    }
}
