package com.cxrry.biz.customer.support.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ExcelIgnoreUnannotated
public class WorkOrderProcessingTotalSiteVo {
  @ApiModelProperty(value = "排序")
  @ExcelProperty(value = "排序", index = 0)
  private Integer RowNum;
  @ApiModelProperty(value = "公司")
  @ExcelProperty(value = "公司", index =1 )
  private String priorityerDeptName;
  @ApiModelProperty(value = "大区")
  @ExcelProperty(value = "大区", index = 2)
  private String priorityerRootRegionName;
  @ApiModelProperty(value = "区域")
  @ExcelProperty(value = "区域", index = 3)
  private String priorityerRegionName;
  @ApiModelProperty(value = "省")
  @ExcelProperty(value = "省", index = 4)
  private String priorityerProvince;
  @ApiModelProperty(value = "市")
  @ExcelProperty(value = "市", index =5 )
  private String priorityerCity;
  @ApiModelProperty(value = "区")
  @ExcelProperty(value = "区", index = 6)
  private String priorityerArea;
  @ApiModelProperty(value = "站点")
  @ExcelProperty(value = "站点", index =7 )
  private String priorityerSiteName;
  @ApiModelProperty(value = "站点编号")
  @ExcelProperty(value = "站点编号", index =8 )
  private String priorityerSiteMark;
  @ApiModelProperty(value = "数量")
  @ExcelProperty(value = "数量", index = 9)
  private String totalCount;
}
