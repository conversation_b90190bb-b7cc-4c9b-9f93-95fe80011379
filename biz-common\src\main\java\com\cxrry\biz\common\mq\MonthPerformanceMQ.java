package com.cxrry.biz.common.mq;

import com.cxrry.common.domain.dto.BasicMqMessageDTO;
import com.ruoyi.calculate.api.domain.vo.PerformanceVo;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @since 2024.09.06
 */
@Getter
@Setter
public class MonthPerformanceMQ extends BasicMqMessageDTO {
    /**
     * 销售代理考核周期表关联ID
     */
    private Long cxrEmployeeAccessCycleRecordId;
    /**
     * 业绩类型：noLimit=不限制 every=每月 all=总业绩
     */
    private String performanceType;
    /**
     * 周期类型 1.按天 2.按自然月  3.按当月剩余天数 4.按连续月份
     */
    private Integer cycleType;
    /**
     * 比较符号：> < >= <=
     */
    private String calculateSymbol;
    /**
     * 设置目标业绩
     */
    private BigDecimal targetPerformance;
    private List<PerformanceVo> employeePerformances;

    private Date runDate;

    public MonthPerformanceMQ() {
    }

    public MonthPerformanceMQ(Long cxrEmployeeAccessCycleRecordId, String performanceType, Integer cycleType, BigDecimal targetPerformance,String calculateSymbol,
        List<PerformanceVo> employeePerformances,Date runDate) {
        this.cxrEmployeeAccessCycleRecordId = cxrEmployeeAccessCycleRecordId;
        this.performanceType = performanceType;
        this.cycleType = cycleType;
        this.targetPerformance = targetPerformance;
        this.calculateSymbol = calculateSymbol;
        this.employeePerformances = employeePerformances;
        this.runDate = runDate;
    }
}
