package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 员工表
 * @TableName cxr_employee
 */
@TableName(value ="cxr_employee")
@Data
public class CxrEmployee implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 员工姓名
     */
    private String name;

    /**
     * 员工真实姓名
     */
    private String realName;

    /**
     * 员工工号
     */
    private String jobNumber;

    /**
     * 员工性别(详情见字典)
     */
    private String genderType;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 学历类型(详情见字典)
     */
    private String educationType;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 员工级别类型(详情见字典)
     */
    private String employeeLevelType;

    /**
     * 是否绑定微信
     */
    private String isBindWx;

    /**
     * 微信openid
     */
    private String wxOpenid;

    /**
     * 微信unionid
     */
    private String wxUnionid;

    /**
     * 微信头像
     */
    private String wxHeadPortrait;

    /**
     * 微信昵称
     */
    private String wxNickname;

    /**
     * 入职时间
     */
    private Date inductionTime;

    /**
     * 离职时间
     */
    private Date quitTime;

    /**
     * 紧急联系人名称
     */
    private String emergencyContactName;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 紧急联系人关系类型(详情见字典)
     */
    private String emergencyContactRelationshipType;

    /**
     * 应聘渠道类型(详情见字典)
     */
    private String applicationChannelsType;

    /**
     * 是否有保险
     */
    private String isHaveInsurance;

    /**
     * 是否包吃
     */
    private String isCanEat;

    /**
     * 是否包住
     */
    private String isCanLive;

    /**
     * 是否有空调
     */
    private String isHaveAirConditioner;

    /**
     * 开户银行名称
     */
    private String depositBankName;

    /**
     * 开户银行卡姓名
     */
    private String depositBankCardName;

    /**
     * 开户银行卡电话
     */
    private String depositBankCardPhone;

    /**
     * 开户银行卡身份证
     */
    private String depositBankCardIdCard;

    /**
     * 开户银行卡卡号
     */
    private String depositBankCardNumber;

    /**
     * 家属开户银行名称
     */
    private String familyDepositBankName;

    /**
     * 家属开户银行卡姓名
     */
    private String familyDepositBankCardName;

    /**
     * 家属开户银行卡电话
     */
    private String familyDepositBankCardPhone;

    /**
     * 家属开户银行卡身份证
     */
    private String familyDepositBankCardIdCard;

    /**
     * 家属开户银行卡卡号
     */
    private String familyDepositBankCardNumber;

    /**
     * 家属关系类型(详情见字典)
     */
    private String familyRelationshipType;

    /**
     * 身份证正面照图片url
     */
    private String idCardPositivePictureUrl;

    /**
     * 身份证反面照图片url
     */
    private String idCardBackPictureUrl;

    /**
     * 销售代表登记表文件url
     */
    private String salesRepresentativeRegisterFileUrl;

    /**
     * 等级代理协议文件url
     */
    private String gradeProxyAgreementFileUrl;

    /**
     * 代理合同文件url
     */
    private String proxyContractFileUrl;

    /**
     * 银行卡正面照图片url
     */
    private String depositBankCardPositivePictureUrl;

    /**
     * 工作次数(目前是第几次工作)
     */
    private Integer workFrequency;

    /**
     * 职业状态(详情见字典)
     */
    private String occupationStatus;

    /**
     * 入职申请审核状态(详情见字典)
     */
    private String inductionApplyAuditStatus;

    /**
     * 离职申请审核状态(详情见字典)
     */
    private String quitApplyAuditStatus;

    /**
     * 学分
     */
    private BigDecimal credit;

    /**
     * 小组表id(员工所在小组的id)
     */
    private Long cxrGroupId;

    /**
     * 站点id(员工所在的站点的id)
     */
    private Long cxrSiteId;

    /**
     * 乐观锁
     */
    private Long revision;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建人类型(详情见字典)
     */
    private String createByType;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人id
     */
    private Long updateBy;

    /**
     * 更新人名称
     */
    private String updateByName;

    /**
     * 更新人类型(详情见字典)
     */
    private String updateByType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除人id
     */
    private Long deleteBy;

    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除人类型(详情见字典)
     */
    private String deleteByType;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    /**
     * 排序
     */
    private Long sortNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 部门id(公司id)
     */
    private Long sysDeptId;

    /**
     * 备用id
     */
    private Long spareId;

    /**
     * 
     */
    private String siteName;

    /**
     * 健康证url
     */
    private String healthCertificateUrl;

    /**
     * 销售代理终止手续办理须知
     */
    private String proxyEndNoticeUrl;

    /**
     * 销售代理终止申请表
     */
    private String proxyEndApplyUrl;

    /**
     * 介绍人id
     */
    private Long introducerId;

    /**
     * 介绍人姓名
     */
    private String introducerName;

    /**
     * 师傅id
     */
    private Long masterId;

    /**
     * 师傅姓名
     */
    private String masterName;

    /**
     * 组长id
     */
    private Long groupLeaderId;

    /**
     * 组长姓名
     */
    private String groupLeaderName;

    /**
     * [{startDate:"",endDate:""},{startDate:"",endDate:""}]   记录当前用户每次入职时间和离职时间
     */
    private Object historyDate;

    /**
     * 销售代理类型 json 数组[],存代理类型字典值
     */
    private Object proxyType;

    /**
     * 入职申请失败审核原因
     */
    private String inductionFailAuditReason;

    /**
     * 原组长名称
     */
    private String sourceGroupLeaderName;

    /**
     * 保证金
     */
    private BigDecimal earnestMoney;

    /**
     * 是否保持组长职位 不保留null  调岗：1 ； 晋升：2
     */
    private Integer keepGroupLeader;

    /**
     * 招聘专员
     */
    private String recruitCommName;

    /**
     * 大奶箱库存
     */
    private Integer lmbStock;

    /**
     * 大奶箱申领标识 0未申领，1已经申领
     */
    private Integer lmbApplyFlag;

    /**
     * 大奶箱押金
     */
    private BigDecimal lmbCashPledge;

    /**
     * 图片访问地址
     */
    private Integer imageLocation;

    /**
     * 图片移动记录
     */
    private Object moveData;

    /**
     * 认证时间
     */
    private Date authTime;

    /**
     * 认证结束时间
     */
    private Date authEndTime;

    /**
     * 认证状态 0未认证，1认证
     */
    private Integer authStatus;

    /**
     * 认证信息
     */
    private String authInfo;

    /**
     * 成为分销员时间
     */
    private Date toBeDistributorTime;

    /**
     * 法大大签署任务ID
     */
    private String authTaskId;

    /**
     * 销售代理的身份证地址
     */
    private String employeeAddress;

    /**
     * 身份证 类型 1 中国  2.香港
     */
    private Integer idCartType;

    /**
     * 1 是否覆盖入职日期 1覆盖
     */
    private Integer authOfInduction;

    /**
     * 提交时间
     */
    private Date submitTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}