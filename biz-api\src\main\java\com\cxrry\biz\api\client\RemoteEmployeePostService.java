package com.cxrry.biz.api.client;

import com.cxrry.biz.common.vo.EmployeePostVo;
import com.cxrry.biz.common.vo.SysUserPostVo;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface RemoteEmployeePostService {

	/**
	 * 查询销售代理职务
	 */
	List<EmployeePostVo> getEmployeePostBy(Collection<Long> employeeIds, Date runDate);

	/**
	 * 查询销售代理职务
	 */
	List<EmployeePostVo> getEmployeePostBy(Collection<Long> employeeIds, Date startDate, Date endDate);
	/**
	 * 查询系统用户岗位职务
	 */
	List<SysUserPostVo> getSysUserPostBy(Collection<Long> employeeIds);
}
