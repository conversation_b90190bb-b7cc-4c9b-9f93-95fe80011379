package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cxrry.biz.customer.support.domain.json.WorkOrderOrderMenuConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderTotalDurationConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import java.util.List;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
*
* @TableName work_order_flow_config
*/
@TableName(value = "work_order_flow_config",autoResultMap = true)
@Data
public class WorkOrderFlowConfig implements Serializable {

    /**
    * id
    */
    @NotNull(message="[id]不能为空")
    @ApiModelProperty("id")
    private Long id;
    /**
    * 流程名称、流程说明
    */
    @ApiModelProperty("流程名称")
    private String processName;
    @ApiModelProperty("流程说明")
    private String processDescription;
    @ApiModelProperty("分类")
    @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderOrderMenuConfigHandler.class)
    private List<WorkOrderOrderMenuConfig>orderMenuConfig;
    /**
    * 0.停用 1.启用
    */
    @ApiModelProperty("0.停用 1.启用")
    private Integer status;
    /**
    * 总时长的配置
    */
    @ApiModelProperty("总时长的配置")
    @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderTotalDurationConfigHandler.class)
    private List<WorkOrderTotalDurationConfig> totalDurationConfig;
    /**
    * 创建人id
    */
    @ApiModelProperty("创建人id")
    private Long createBy;
    /**
    * 创建人名称
    */
    @ApiModelProperty("创建人名称")
    private String createByName;
    /**
    * 创建人类型(详情见字典)
    */
    @ApiModelProperty("创建人类型(详情见字典)")
    private String createByType;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
    * 更新人id
    */
    @ApiModelProperty("更新人id")
    private Long updateBy;
    /**
    * 更新人名称
    */
    @ApiModelProperty("更新人名称")
    private String updateByName;
    /**
    * 更新人类型(详情见字典)
    */
    @ApiModelProperty("更新人类型(详情见字典)")
    private String updateByType;
    /**
    * 更新时间
    */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
    * 删除人id
    */
    @ApiModelProperty("删除人id")
    private Long deleteBy;
    @ApiModelProperty("删除人名称")
    private String deleteByName;
    /**
    * 删除人类型(详情见字典)
    */
    @ApiModelProperty("删除人类型(详情见字典)")
    private String deleteByType;
    /**
    * 删除时间
    */
    @ApiModelProperty("删除时间")
    private Date deleteTime;
    @ApiModelProperty("删除状态")
    private String deleteStatus;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    @ApiModelProperty(value = "流程id")
    private Long flowConfigId;

    @TableField(exist = false)
    @ApiModelProperty(value = "分类工单ids")
    private List<Long> orderMenuIds;
}
