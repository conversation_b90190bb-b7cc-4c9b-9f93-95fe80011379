package com.cxrry.biz.customer.support.controller.staff;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.StaffWorkOrderComplaintTotalBo;
import com.cxrry.biz.customer.support.domain.vo.StaffWorkOrderComplaintTotalVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderTotalNumberVo;
import com.cxrry.biz.customer.support.service.WorkOrderComplaintTotalService;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.UserType;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
    value = "配送端-投诉工单",
    tags = {"配送端-投诉工单"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/staff/workOrderComplaintTotal")
public class StaffWorkOrderComplaintTotalController {

  private final WorkOrderComplaintTotalService workOrderComplaintTotalService;

  @ApiOperation("工单排名-投诉统计")
  @SaCheckPermission(value = "support:workOrder:staffWorkOrderComplaintTotal", type = UserType.cxr_staff)
  @PostMapping("/staffPage")
  public R<PageTableDataInfo<StaffWorkOrderComplaintTotalVo>> workOrderComplaintTotalPage(
      @RequestBody StaffWorkOrderComplaintTotalBo bo) {
    return R.ok(workOrderComplaintTotalService.staffPage(bo));
  }

  @ApiOperation("工单排名-投诉统计底部合计")
  @PostMapping("/staffTotal")
  public R<WorkOrderTotalNumberVo> staffTotal(@RequestBody StaffWorkOrderComplaintTotalBo bo) {
    return R.ok(workOrderComplaintTotalService.staffTotal(bo));
  }


}
