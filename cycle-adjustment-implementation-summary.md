# 销售代理考核周期调整逻辑实现总结

## 1. 需求背景

根据"销售代理考核周期调整逻辑梳理"文档，我们需要实现一个更加合理的考核周期延期计算逻辑，主要处理三种调整类型：
- 个人请假 (PERSONAL_LEAVE)
- 公司放假 (COMPANY_HOLIDAY)
- 手动调整 (MANUAL_ADJUST)

## 2. 实现进度

### 2.1 已完成部分

1. **延期计算核心逻辑**
   - 实现了 `CycleDelayManagerImpl` 类，包含延期计算的核心算法
   - 支持三种调整类型的优先级处理
   - 实现了个人请假和公司放假的交集计算

2. **延期计算结果模型**
   - 设计了 `DelayCalculationResult` 类，包含延期天数、新结束日期、计算原因等信息
   - 支持详细的延期处理描述，便于追踪和调试

3. **支持当前调整记录**
   - 修改了 `autoProcessDelayWithResult` 方法，支持传入当前调整记录
   - 实现了 `getActiveDelayRecordsWithCurrent` 方法，将当前调整与历史记录合并计算

4. **修复了合并计算逻辑**
   - 修正了个人请假和公司放假有重叠时的计算逻辑
   - 实现了 `calculateMergedDaysInCycle` 方法，正确计算合并后的总天数

### 2.2 待完成部分

1. **集成到 `adjustCycleByDays` 方法**
   - 需要在 `CxrEmployeeAccessCycleRecordServiceImpl` 类中修改 `adjustCycleByDays` 方法
   - 将新的延期计算逻辑应用到周期调整流程中

2. **单元测试**
   - 编写单元测试验证各种场景下的延期计算结果
   - 测试边界条件和特殊情况

3. **文档更新**
   - 更新技术文档，说明新的延期计算逻辑
   - 提供使用示例和注意事项

## 3. 核心算法说明

### 3.1 延期计算流程

```
延期计算流程
├── 查询周期所有有效延期记录
├── 按类型分组，每组取最新记录
│   ├── 个人请假组 → 取最新一条
│   ├── 公司放假组 → 取最新一条
│   └── 手动调整组 → 取最新一条
└── 最终延期计算
    ├── 如果存在手动调整
    │   └── 只取手动调整，忽略其他
    └── 如果不存在手动调整
        ├── 只有个人请假 → 直接使用
        ├── 只有公司放假 → 直接使用
        └── 个人+公司都存在 → 计算合并延期
```

### 3.2 合并延期计算

当个人请假和公司放假都存在时：

1. **有重叠**：计算合并后的总天数（从最早开始到最晚结束）
   ```
   例如：
   个人请假：2025-6-18 到 2025-6-24
   公司放假：2025-6-23 到 2025-6-29
   合并后：2025-6-18 到 2025-6-29 = 12天延期
   ```

2. **无重叠**：累加两个时间段的天数
   ```
   例如：
   个人请假：2025-6-10 到 2025-6-15
   公司放假：2025-6-20 到 2025-6-25
   累加：6天 + 6天 = 12天延期
   ```
