package com.cxrry.biz.customer.support.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderReportTotalPageBo extends PageQuery implements Serializable {

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "合作状态 1:合作中 2:终止合作")
    private String occupationStatus;

    /**
     * 受理人ID
     */
    private Long priorityerId;

    /**
     * 受理人名称
     */
    private String priorityerName;

    /**
     * 受理人编号
     */
    private String priorityerNumber;

    /**
     * 受理人站点名称
     */
    private String priorityerSiteName;
    /**
     * 受理人站点编号
     */
    private String priorityerSiteMark;
    /**
     * 受理人站点id
     */
    private Long priorityerSiteId;
    /**
     * 受理人公司名
     */
    private String priorityerDeptName;
    /**
     * 受理人公司id
     */
    private Long priorityerDeptId;
    /**
     * 受理人区域id
     */
    private Long priorityerRegionId;
    /**
     * 受理人区域名
     */
    private String priorityerRegionName;
    /**
     * 受理人大区id
     */
    private Long priorityerRootRegionId;
    /**
     * 受理人大区名
     */
    private String priorityerRootRegionName;
    /**
     * 受理人省
     */
    private String priorityerProvince;
    /**
     * 受理人市
     */
    private String priorityerCity;
    /**
     * 受理人区
     */
    private String priorityerArea;

    //分组类型  1销售代理 2站点 3区域
    private Integer groupType;

    //销售代理ID集合
    private List<Long> empIdList;

    //销售代理站点ID集合
    private List<Long> siteIdList;

    //销售代理区域ID集合
    private List<Long> regionIdList;

    //用于查询站点 、区域的ID集合
    private List<Long> ids;

}
