package com.cxrry.biz.customer.support.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@RefreshScope
@Data
@Component
@ConfigurationProperties(prefix = "wx.wptmp")
public class WxMpTmpProperties {
  /**
   * 投诉工单模板ID
   */
  private String complaintTemplateId;

  /**
   * 退款工单模板ID
   */
  private String refundTemplateId;



}