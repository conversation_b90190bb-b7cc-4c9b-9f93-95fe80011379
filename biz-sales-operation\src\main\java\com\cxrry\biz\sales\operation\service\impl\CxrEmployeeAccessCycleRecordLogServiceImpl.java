package com.cxrry.biz.sales.operation.service.impl;


import static java.util.stream.Collectors.toList;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cxrry.biz.common.utils.DateUtils;
import com.cxrry.biz.common.utils.SmsBizSendUtil;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.sales.operation.domain.CxrEmployee;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecord;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordLog;
import com.cxrry.biz.sales.operation.domain.CxrStopMilk;
import com.cxrry.biz.sales.operation.domain.bo.AccessEditPageBo;
import com.cxrry.biz.sales.operation.domain.dto.StopMilkDTO;
import com.cxrry.biz.sales.operation.domain.vo.AccessEditPageExcel;
import com.cxrry.biz.sales.operation.domain.vo.AccessEditPageVo;
import com.cxrry.biz.sales.operation.enums.cycle.AdjustSourceTypeEnum;
import com.cxrry.biz.sales.operation.enums.cycle.EmployeeStageEnum;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeAccessCycleRecordLogMapper;
import com.cxrry.biz.sales.operation.mapper.CxrStopMilkMapper;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordLogService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeAccessCycleRecordService;
import com.cxrry.biz.sales.operation.service.CxrEmployeeService;
import com.ruoyi.common.core.domain.ExcelReportDTO;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.core.enums.OccupationStatus;
import com.ruoyi.common.excel.utils.ExcelExportObj;
import com.ruoyi.common.excel.utils.ExcelMqUtil;
import com.ruoyi.common.mybatis.core.page.PageQuery;
import com.ruoyi.common.satoken.utils.helper.LoginHelper;
import com.ruoyi.system.api.model.LoginUser;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cxr_employee_access_cycle_record_log(修改考核周期记录表)】的数据库操作Service实现
* @createDate 2024-08-22 15:19:07
*/
@RequiredArgsConstructor
@Slf4j
@Service
public class CxrEmployeeAccessCycleRecordLogServiceImpl extends ServiceImpl<CxrEmployeeAccessCycleRecordLogMapper, CxrEmployeeAccessCycleRecordLog>
implements CxrEmployeeAccessCycleRecordLogService{

  private final CxrStopMilkMapper stopMilkMapper;
  @Lazy
  @Autowired
  private  CxrEmployeeAccessCycleRecordService employeeAccessCycleRecordService;
  @Autowired
  private  CxrEmployeeAccessCycleRecordLogService employeeAccessCycleRecordLogService;
  private final CxrEmployeeService employeeService;
  private final SmsBizSendUtil smsBizSendUtil;

  @Override
  public PageTableDataInfo<AccessEditPageVo> accessEditPage(AccessEditPageBo bo) {
    PageQuery pageQuery = BeanUtil.copyProperties(bo, PageQuery.class);

    IPage<AccessEditPageVo> editPageVoIPage= this.baseMapper.accessEditPage(bo,pageQuery.build());
    List<AccessEditPageVo> records = editPageVoIPage.getRecords();
    if (CollUtil.isNotEmpty(records)){
      for (AccessEditPageVo record : records) {
        if (ObjectUtil.isNotEmpty(record.getStage())){
          record.setStageStr(EmployeeStageEnum.fromSequence(record.getStage()).getDescription());
        }
        record.setEditLastStageStr(EmployeeStageEnum.fromSequence(record.getEditLastStage()).getDescription());
      }
    }
    return PageTableDataInfo.build(editPageVoIPage);
  }


  @Override
  public void export(AccessEditPageBo bo, HttpServletResponse response) {
    int pageSize = 1000;
    long pageCount = 1;
    LoginUser loginUser = LoginHelper.getLoginUser();
    PageQuery pageQuerys = BeanUtil.copyProperties(bo, PageQuery.class);
    pageQuerys.setPageNum(1);
    pageQuerys.setPageSize(1);
    IPage<AccessEditPageVo> editPageVoIPage= this.baseMapper.accessEditPage(bo,pageQuerys.build());
    List<AccessEditPageVo> records = new ArrayList<>();
    Long splitTotal = editPageVoIPage.getTotal();
    ExcelReportDTO excelReportDTO = ExcelMqUtil.excelSendRecord(bo.getHtmlName(), splitTotal, loginUser.getUserId(),
        loginUser.getUserType(), loginUser.getUserName());
    for (int current = 1; current <= pageCount; current++) {
      PageQuery pageQuery =new PageQuery();
      pageQuery.setPageNum(current);
      pageQuery.setPageSize(pageSize);
      IPage<AccessEditPageVo> pageVoIPage = this.baseMapper.accessEditPage(bo, pageQuery.build());
      long total = pageVoIPage.getTotal();
      pageCount = total % pageSize > 0 ? (total / pageSize + 1) : total / pageSize;
      records.addAll(pageVoIPage.getRecords());
      ExcelMqUtil.excelSendIncrementData(excelReportDTO.getUniqueMark(),
          Long.valueOf(pageVoIPage.getSize()), null);
    }
    excelExportSetData(records, response, excelReportDTO);
  }

  private void excelExportSetData(List<AccessEditPageVo> records, HttpServletResponse response, ExcelReportDTO excelReportDTO) {
    List<List> listList = records.stream().map(x -> {
      List<Object> list = new ArrayList<>();
      list.add(x.getCompanyName());
      list.add(x.getRegionName());
      list.add(x.getArea());
      list.add(x.getProvince());
      list.add(x.getCity());
      list.add(x.getDistrict());
      list.add(x.getSiteName());
      list.add(x.getEmployeeName());
      list.add(x.getEmployeeNumber());
      list.add("L"+x.getCurrentLevel());

      if (ObjectUtil.isNotEmpty(x.getStage())){
        list.add(EmployeeStageEnum.fromSequence(x.getStage()).getDescription());
      }else{
        list.add("-");
      }

      list.add(EmployeeStageEnum.fromSequence(x.getEditLastStage()).getDescription());
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      list.add(
          ObjectUtil.isEmpty(x.getOriginalAssessmentStartDate())?null:
          StrUtil.format("{} 至 {}",sdf.format(x.getOriginalAssessmentStartDate()),
          ObjectUtil.isNotEmpty(x.getOriginalAssessmentEndDate())? sdf.format(x.getOriginalAssessmentEndDate()):"-"));

      list.add(
          ObjectUtil.isEmpty(x.getEditLastAssessmentStartDate())?null:
          StrUtil.format("{} 至 {}",sdf.format(x.getEditLastAssessmentStartDate()),
          ObjectUtil.isNotEmpty(x.getEditLastAssessmentEndDate())? sdf.format(x.getEditLastAssessmentEndDate()):"-"));

      list.add(x.getUpdateCycleReason());
      list.add(
          ObjectUtil.isEmpty(x.getAdjustStartDate())?null:
              StrUtil.format("{} 至 {}",x.getAdjustStartDate(),
                  ObjectUtil.isNotEmpty(x.getAdjustEndDate())? x.getAdjustEndDate():"-")
          );

      list.add(x.getUpdateByName());
      list.add(x.getUpdateTime());
      list.add(x.getRemark());
      return list;
    }).collect(toList());
    ExcelExportObj excelExportObj = new ExcelExportObj(excelReportDTO.getFileDir(), "代理考核周期修改.xlsx",
        AccessEditPageExcel.class);
    excelExportObj.writeManySheet(listList);
    excelExportObj.pathClose();
  }

  /**
   * 获取指定日期内的调整记录
   */
  @Override
  public List<CxrEmployeeAccessCycleRecordLog> getAdjustLogList(Long cxrEmployeeId, Date startDate, Date endDate,
      Integer stage) {
    //查询周期起始开始有修改记录的日志  (等确认取值逻辑),先只对有调整日期的做处理.
    //调整起始日期小于结束日期&调整结束日期大于起始日期
    List<CxrEmployeeAccessCycleRecordLog> logList = this.list(
        new LambdaQueryWrapper<CxrEmployeeAccessCycleRecordLog>()
            .eq(CxrEmployeeAccessCycleRecordLog::getEmployeeId,cxrEmployeeId)
            .ge(CxrEmployeeAccessCycleRecordLog::getAdjustEndDate, startDate)//>=
            .le(CxrEmployeeAccessCycleRecordLog::getAdjustStartDate, endDate)//<=
//            .eq(CxrEmployeeAccessCycleRecordLog::getRerunFlag, 1)
//            .eq(CxrEmployeeAccessCycleRecordLog::getHandCycleRecordId,0)
//            .eq(CxrEmployeeAccessCycleRecordLog::getStage,stage)
    );
    if(CollectionUtil.isEmpty(logList)) ListUtil.toList();
    return this.filterLogs(logList);
  }

  /**
   * 过滤logList中的交叉日期记录，保留id最大的一条
   * @param logList 查询得到的日志列表
   * @return 过滤后的日志列表
   */
  public  List<CxrEmployeeAccessCycleRecordLog> filterLogs(List<CxrEmployeeAccessCycleRecordLog> logList) {
    logList.sort(Comparator
        .comparing(CxrEmployeeAccessCycleRecordLog::getAdjustStartDate)
        .thenComparing(CxrEmployeeAccessCycleRecordLog::getAdjustEndDate));
    List<CxrEmployeeAccessCycleRecordLog> filteredList = new ArrayList<>();

    for (int i = 0; i < logList.size(); i++) {
      CxrEmployeeAccessCycleRecordLog currentLog = logList.get(i);
      boolean hasIntersection = false;

      for (int j = 0; j < filteredList.size(); j++) {
        CxrEmployeeAccessCycleRecordLog filteredLog = filteredList.get(j);

        Date[] intersection =
            DateUtils.intersectRanges(
                DateUtils.convertLocalDateToDate(currentLog.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(currentLog.getAdjustEndDate()),
                DateUtils.convertLocalDateToDate(filteredLog.getAdjustStartDate()),
                DateUtils.convertLocalDateToDate(filteredLog.getAdjustEndDate()));

        if (intersection != null && intersection.length > 0) {
          hasIntersection = true;
          if (currentLog.getId() > filteredLog.getId()) {
            filteredList.set(j, currentLog);
          }
          break;
        }
      }
      if (!hasIntersection) {
        filteredList.add(currentLog);
      }
    }
    return filteredList;
  }



  /**
   * 延期-公司统一停奶
   * 把运行中的考核周期延期相差天数
   * @param dto
   */
  @DSTransactional
  @Override
  public void accessDelayByStopMilk(StopMilkDTO dto) {
    CxrStopMilk cxrStopMilk = stopMilkMapper.selectById(dto.getCxrStopMilkId());
    if (ObjectUtil.isNull(cxrStopMilk)){
      throw new IllegalArgumentException("停奶记录不存在");
    }
    if(!ObjectUtil.equal(cxrStopMilk.getCompanyRecess(),2)){
      log.info("未选择公司统一放假，不处理");
      return;
    }
    //查找符号条件的周期进行延期
    LambdaQueryWrapper<CxrEmployee> cxrEmployeeLambdaQueryWrapper = new LambdaQueryWrapper<>();
    cxrEmployeeLambdaQueryWrapper.select(CxrEmployee::getId)
    .eq(CxrEmployee::getOccupationStatus, OccupationStatus.INDUCTION.getValue())
    .eq(CxrEmployee::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue());
    if(cxrStopMilk.getCxrSiteId()!=null){
      cxrEmployeeLambdaQueryWrapper.eq(CxrEmployee::getCxrSiteId,cxrStopMilk.getCxrSiteId());
    }else if(cxrStopMilk.getCxrEmployeeId()!=null||dto.getCxrEmployeeId()!=null){
      cxrEmployeeLambdaQueryWrapper.eq(CxrEmployee::getId,cxrStopMilk.getCxrEmployeeId());
    }
    if(dto.getCxrEmployeeId()!=null){
      cxrEmployeeLambdaQueryWrapper.eq(CxrEmployee::getId,dto.getCxrEmployeeId());
    }
    List<CxrEmployee> cxrEmployeeList = employeeService.list(cxrEmployeeLambdaQueryWrapper);
    if (ObjectUtil.isEmpty(cxrEmployeeList)){
      log.info("未找到符合条件的员工");
      return;
    }
    employeeAccessCycleRecordLogService.remove(new LambdaUpdateWrapper<CxrEmployeeAccessCycleRecordLog>()
        .eq(CxrEmployeeAccessCycleRecordLog::getSourceId, dto.getCxrStopMilkId()));
    int isError = 0;
    for (CxrEmployee cxrEmployee : cxrEmployeeList) {
      try{
        List<CxrEmployeeAccessCycleRecord> inProgressList = employeeAccessCycleRecordService.getInProgress(
            cxrEmployee.getId());
        if (ObjectUtil.isEmpty(inProgressList)){
          continue;
        }
        if(cxrStopMilk.getDeleteStatus().equals(DeleteStatus.NOT_DELETED.getValue())){
          employeeAccessCycleRecordService.adjustCycleByDays(AdjustSourceTypeEnum.COMPANY_HOLIDAY,dto.getCxrStopMilkId(), inProgressList,
              cxrStopMilk.getStartStopTime(),
              cxrStopMilk.getEndStopTime(),"公司统一放假延期:"+cxrStopMilk.getRemark()+";按月的周期不变动");
        }else{
          employeeAccessCycleRecordService.adjustCycleByDays(AdjustSourceTypeEnum.COMPANY_HOLIDAY,dto.getCxrStopMilkId(), inProgressList,
              cxrStopMilk.getStartStopTime(),
              DateUtil.offsetDay(cxrStopMilk.getStartStopTime(),-1).toJdkDate(),"公司统一放假延期删除:"+cxrStopMilk.getRemark()+
                  ";恢复周期");
        }


      }catch (Exception e){
        log.error("员工：{} 延期失败",cxrEmployee.getId(),e);
        isError ++;
      }
    }
    if (isError>0){
      smsBizSendUtil.sendInternalUserSms(DateUtil.now()+StrUtil.format("统一放假发生延期失败,失败数{}",isError));
    }
  }
}
