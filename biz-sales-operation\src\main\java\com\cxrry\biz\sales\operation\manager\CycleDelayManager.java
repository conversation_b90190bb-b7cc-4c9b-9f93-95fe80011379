package com.cxrry.biz.sales.operation.manager;

import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecord;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeAccessCycleRecordLog;
import com.cxrry.biz.sales.operation.domain.dto.DelayCalculationResult;
import java.util.Date;
import java.util.List;

/**
 * 考核周期延期处理管理器
 * 专门处理考核周期的延期逻辑
 */
public interface CycleDelayManager {

    /**
     * 计算考核周期的延期天数
     * 根据简化规则处理个人请假、公司放假、手动调整的延期逻辑
     *
     * @param employeeId 员工ID
     * @param cycleId 考核周期ID
     * @return 延期计算结果
     */
    DelayCalculationResult calculateDelay(CxrEmployeeAccessCycleRecord cycleRecord);

    /**
     * 应用延期到考核周期
     * 根据计算结果更新考核周期的实际结束日期
     *
     * @param cycleRecord 考核周期记录
     * @param delayResult 延期计算结果
     * @return 是否成功应用延期
     */
    Boolean applyDelay(CxrEmployeeAccessCycleRecord cycleRecord, DelayCalculationResult delayResult);

    /**
     * 自动处理考核周期延期
     * 查询指定周期的延期记录并自动计算应用延期
     *
     * @param cycleRecord 考核周期记录
     * @return 延期计算结果，包含延期天数和说明
     */
    DelayCalculationResult autoProcessDelayWithResult(CxrEmployeeAccessCycleRecord cycleRecord);



    /**
     * 自动处理考核周期延期
     * 查询指定周期的延期记录并自动计算应用延期
     *
     * @param cycleRecord 考核周期记录
     * @param currentAdjust 当前调整记录
     * @return 延期计算结果，包含延期天数和说明
     */
    DelayCalculationResult autoProcessDelayWithResult(CxrEmployeeAccessCycleRecord cycleRecord,CxrEmployeeAccessCycleRecordLog currentAdjust);

    /**
     * 自动处理考核周期延期（兼容性方法）
     * 查询指定周期的延期记录并自动计算应用延期
     *
     * @param cycleRecord 考核周期记录
     * @return 延期天数，0表示无延期
     */
    Integer autoProcessDelay(CxrEmployeeAccessCycleRecord cycleRecord);

    /**
     * 查询考核周期的有效延期记录
     * 按类型分组并返回每种类型的最新记录
     *
     * @param employeeId 员工ID
     * @param cycleId 考核周期ID
     * @return 有效的延期记录列表
     */
    List<CxrEmployeeAccessCycleRecordLog> getActiveDelayRecords(CxrEmployeeAccessCycleRecord cycleRecord);

    /**
     * 验证延期记录的有效性
     * 检查延期记录是否在考核周期范围内
     *
     * @param delayRecord 延期记录
     * @param cycleStartDate 考核周期开始日期
     * @param cycleEndDate 考核周期结束日期
     * @return 是否有效
     */
    Boolean validateDelayRecord(CxrEmployeeAccessCycleRecordLog delayRecord, Date cycleStartDate, Date cycleEndDate);
}
