package com.cxrry.biz.customer.support.controller;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderFlowNodeConfigVBo;
import com.cxrry.biz.customer.support.service.WorkOrderFlowNodeConfigService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
    value = "工单流程配置",
    tags = {"工单流程配置"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderFlowNodeConfig")
public class WorkOrderFlowNodeConfigController {
  private final WorkOrderFlowNodeConfigService workflowFlowNodeConfigService;
  @ApiOperation("工单节点配置的编辑")
  @SaCheckPermission("support:workOrderFlowNodeConfig:update")
  @PostMapping("/update")
  public R update(@RequestBody WorkOrderFlowNodeConfigVBo bo) {
    return R.ok(workflowFlowNodeConfigService.update(bo));
  }
}
