package com.cxrry.biz.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD,ElementType.TYPE})
public @interface BizCheckPermission {

    /**
     * 需要校验的权限码
     * @return 需要校验的权限码
     */
    String value();

}

