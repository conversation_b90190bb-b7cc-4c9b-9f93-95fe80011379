package com.cxrry.biz.sales.operation.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cxrry.biz.sales.operation.domain.CxrEmployeeDivideMonthLastRecord;
import com.cxrry.biz.sales.operation.service.CxrEmployeeDivideMonthLastRecordService;
import com.cxrry.biz.sales.operation.mapper.CxrEmployeeDivideMonthLastRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【cxr_employee_divide_month_last_record(月底实发分成记录)】的数据库操作Service实现
* @createDate 2025-07-01 15:38:53
*/
@Service
public class CxrEmployeeDivideMonthLastRecordServiceImpl extends ServiceImpl<CxrEmployeeDivideMonthLastRecordMapper, CxrEmployeeDivideMonthLastRecord>
implements CxrEmployeeDivideMonthLastRecordService{

}
