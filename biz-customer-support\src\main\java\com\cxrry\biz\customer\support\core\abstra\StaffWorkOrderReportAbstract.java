package com.cxrry.biz.customer.support.core.abstra;

import static java.util.stream.Collectors.toList;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cxrry.biz.customer.support.domain.CxrEmployeePost;
import com.cxrry.biz.customer.support.domain.SysUserPost;
import com.cxrry.biz.customer.support.enums.base.PostType;
import com.cxrry.biz.customer.support.mapper.erp.CxrEmployeePostMapper;
import com.cxrry.biz.customer.support.mapper.erp.SysUserPostMapper;
import com.ruoyi.common.core.enums.ChargeStatus;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.satoken.utils.LoginUtil;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;

/*
工单配送端报表抽象类
 */
public  abstract class StaffWorkOrderReportAbstract {

	@Autowired
	private CxrEmployeePostMapper cxrEmployeePostMapper;
	@Autowired
	private SysUserPostMapper sysUserPostMapper;

	/**
	 * 查询权限
	 * @param type 1.销售代理 2.站点 3.区域
	 */
	protected List<Long> getUserPermissionIds(Integer type) {
		List<Long> ids = new ArrayList<>();
		Long userId = LoginUtil.getLoginUser().getUserId();
		//董事长大区 小区 总监
		List<SysUserPost> sysUserPostList = sysUserPostMapper.selectList(new LambdaQueryWrapper<SysUserPost>()
				.eq(SysUserPost::getUserId,userId)
				.in(SysUserPost::getPostId,
						PostType.CHAIR_MAN.getValue(),
						PostType.REGION_MANAGER.getValue(),
						PostType.COMMISSIONER.getValue(),
						PostType.CELLULAR_MANAGER.getValue())
				.eq(SysUserPost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
		);
		//判断是否存在董事长职位
		if(sysUserPostList.stream().anyMatch(n -> n.getPostId().toString().equals(PostType.CHAIR_MAN.getValue())))return null;
		ids.addAll(sysUserPostList.stream().map(SysUserPost::getDeptId).collect(toList()));

		//主管 站点维度 业务BP 助理经理 区域维度
		List<CxrEmployeePost> cxrEmployeePost = cxrEmployeePostMapper.selectList(new LambdaQueryWrapper<CxrEmployeePost>()
				.eq(CxrEmployeePost::getCxrEmployeeId, userId)
				.eq(CxrEmployeePost::getChargeStatus, ChargeStatus.IN_PROGRESS.getValue())
				.eq(CxrEmployeePost::getDeleteStatus, DeleteStatus.NOT_DELETED.getValue())
				.apply("JSON_OVERLAPS(JSON_EXTRACT(cxr_post_id,'$.value'),{0})",
						JSONUtil.toJsonStr(Arrays.asList(
								type!=3?PostType.DIRECTOR.getValue():"0",
								type!=3?PostType.ASSISTANT_MANAGER.getValue():"0",
								PostType.BIZ_BP.getValue())))
		);
		List<Long> cxrSiteIds =
				cxrEmployeePost.stream().filter(n -> n.getCxrSiteId()!=null).map(CxrEmployeePost::getCxrSiteId).collect(toList());
		ids.addAll(cxrSiteIds);
		List<Long> cxrRegionIds =
				cxrEmployeePost.stream().filter(n -> n.getCxrRegionId()!=null).map(CxrEmployeePost::getCxrRegionId).collect(toList());
		ids.addAll(cxrRegionIds);
		if (ids.isEmpty())ids.add(0L);
		return ids.stream().distinct().collect(toList());
	}
}
