package com.cxrry.biz.sales.operation.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cxrry.biz.common.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ElaFeeExcel implements Serializable {

    @ExcelProperty(value = "月份")
    private String bsMonth;

    @ExcelProperty(value = "销售代理")
    private String employeeName;

    @ExcelProperty(value = "销售代理编号")
    private String employeeNo;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "吃(元)")
    private BigDecimal eat;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "住(元)")
    private BigDecimal reside;

    @JsonSerialize(using = BigDecimalSerializer.class)
    @ExcelProperty(value = "空调(元)")
    private BigDecimal conditioner;
}
