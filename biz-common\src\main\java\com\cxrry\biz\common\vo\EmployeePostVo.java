package com.cxrry.biz.common.vo;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class EmployeePostVo implements Serializable {
    /**
     * 销售代理ID
     */
    @ApiModelProperty(value = "销售代理ID")
    private Long employeeId;
    /**
     * 职务ID
     */
    @ApiModelProperty(value = "职务ID")
    private Long postId;
    /**
     * 职务名称
     */
    @ApiModelProperty(value = "职务名称")
    private String postName;

    private String postInfoStr;

    public CxrPostId getPostInfo(){
        if(StringUtils.isBlank(postInfoStr)) return null;
        CxrPostId cxrPostId = JSONObject.parseObject(postInfoStr, CxrPostId.class);
        return cxrPostId;
    }
    public EmployeePostVo() {
    }

    public EmployeePostVo(Long employeeId, Long postId, String postName, String postInfoStr) {
        this.employeeId = employeeId;
        this.postId = postId;
        this.postName = postName;
        this.postInfoStr = postInfoStr;
    }
}
