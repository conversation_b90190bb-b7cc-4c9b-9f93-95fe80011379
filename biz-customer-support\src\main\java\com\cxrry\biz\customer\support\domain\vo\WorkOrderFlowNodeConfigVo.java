package com.cxrry.biz.customer.support.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.cxrry.biz.customer.support.domain.json.WorkOrderAcceptorConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderButtonConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderDurationConfig;
import com.cxrry.biz.customer.support.jsontypehandler.JsonTypeHandlerConstant;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class WorkOrderFlowNodeConfigVo {
  private Long id;
  @ApiModelProperty("节点等级")
  private Integer nodeGrade;
  @ApiModelProperty("时长的配置")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderDurationConfigTypeHandler.class)
  private List<WorkOrderDurationConfig> durationConfig;
  @ApiModelProperty("受理人")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderAcceptorConfigHandler.class)
  private List<WorkOrderAcceptorConfig> acceptorConfig;
  @ApiModelProperty("按钮")
  @TableField(typeHandler = JsonTypeHandlerConstant.WorkOrderButtonConfigHandler.class)
  private List<WorkOrderButtonConfig> buttonConfig;
}
