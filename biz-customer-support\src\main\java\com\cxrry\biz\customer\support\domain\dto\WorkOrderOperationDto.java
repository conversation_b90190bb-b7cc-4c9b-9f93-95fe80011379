package com.cxrry.biz.customer.support.domain.dto;

import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 工单受理记录（服务记录）DTO
 */
@Data
public class WorkOrderOperationDto {
	@ApiModelProperty(value = "操作人名称")
	private String operatorName;

	@ApiModelProperty(value = "操作类型：1完结 2回复 3转交 4回退 5逾期 6重启")
	private Integer operateType;

	@ApiModelProperty(value = "回复内容")
	private String relyNotes;

	@ApiModelProperty(value = "Url相对路径，逗号隔开")
	private List<FileUrlConfig> fileUrl;

	@ApiModelProperty(value = "转交后受理人名称")
	private String nextOperator;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "受理时间")
	private Date operateTime;

}
