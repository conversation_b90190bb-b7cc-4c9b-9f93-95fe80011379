package com.cxrry.biz.common.json;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/4 20:21
 **/
@ApiModel("销售代理")
@Data
public class BusinessAgent implements Serializable {


    /**
     * 销售代理id  cxr_employee_id
     */
    @ApiModelProperty(value = "销售代理id", required = true)
    private Long proxyId;

    /**
     * 销售代理名称
     */
    @ApiModelProperty(value = "销售代理名称", required = true)
    private String proxyName;

    /**
     * 销售代理编号
     */
    @ApiModelProperty(value = "销售代理编号", required = true)
    private String proxyNo;

    /**
     * 销售代理编号
     */
    @ApiModelProperty(value = "销售代理等级", required = true)
    private Integer level;

    /**
     * 销售代理编号
     */
    @ApiModelProperty(value = "站点名称", required = true)
    private String siteName;

    /**
     * 销售代理合作状态
     */
    @ApiModelProperty(value = "销售代理合作状态")
    private Boolean occupationStatus;
}
