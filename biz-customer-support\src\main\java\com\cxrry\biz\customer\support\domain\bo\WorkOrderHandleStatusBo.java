package com.cxrry.biz.customer.support.domain.bo;

import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import java.util.Date;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WorkOrderHandleStatusBo implements Serializable{

    private Long workOrderId;

    private Long workNodeId;

    private Long currentOperationId;

    // 回复内容
    private String relyNotes;

    // 附件
    private List<FileUrlConfig> fileUrl;

    private Long workOrderPriorityNodeId;
    //受理人  前者
    private Long acceptId;

    private String acceptName;

    //转交人后者
    private Long operatorId;
    private String operatorName;


    /**
     * 转交后的节点ID（发生转交时）
     */
    private Long nextOperationId;

    /**
     * 转交后受理人名称
     */
    private String nextOperator;

    /**
     * 受理状态 0.待处理 1.受理中 2.已完成
     */
    private Integer priorityStatus;

    /**
     * 操作类型 1完结 2回复 3转交 4回退 5逾期 6重启
     */
    private Integer operateType;

    /**
     * 节点等级 1 2 3 4 5 6
     */
    private Integer nodeLevel;

    /**
     * 受理人ID类型
     */
    private Integer priorityUserIdType;

    /**
     * 受理人配置的用户ID
     */
    private Long priorityUserId;

    /**
     * 受理人配置的职位ID
     */
    private Long priorityPostId;

    /**
     * 受理人配置的角色ID
     */
    private Long priorityRoleId;

    /**
     * 受理人ID类型对应的名称
     */
    private String priorityIdName;

    /**
     * 受理人ID
     */
    private Long priorityerId;

    /**
     * 受理人名称
     */
    private String priorityerName;

    /**
     * 受理人编号
     */
    private String priorityerNumber;

    /**
     * 受理人在当前站点的最高职位名称（冗余）
     */
    private String operatorMaxJobName;

    /**
     * 受理人账号类型：1销售代理 2总部用户
     */
    private Integer acceptedAccount;

    private Long siteId;

    /**
     * 操作时间
     */
    private Date operateTime;



}
