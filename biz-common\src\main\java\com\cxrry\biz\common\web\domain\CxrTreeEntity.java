package com.cxrry.biz.common.web.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ruoyi.common.core.annotation.BaseColumn;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2022/6/29 16:24
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CxrTreeEntity<T> extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 父菜单名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "父菜单名称")
    @BaseColumn
    private String parentName;

    /**
     * 父菜单ID
     */
    @ApiModelProperty(value = "父菜单ID")
    @BaseColumn
    private Long parentId;

    /**
     * 所有父级
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "所有父级")
    @BaseColumn
    private String allParent;

    /**
     * 所有子级
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "所有子级")
    @BaseColumn
    private String allChildren;

    /**
     * 子部门
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子部门")
    private List<T> children = new ArrayList<>();
}
