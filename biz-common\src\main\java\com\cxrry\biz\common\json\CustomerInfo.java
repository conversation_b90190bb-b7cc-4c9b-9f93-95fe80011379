package com.cxrry.biz.common.json;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>Description: </p>
 *
 * <AUTHOR>
 * @version v1.0.0
 * @since 2022/8/4 20:38
 **/
@ApiModel("用户信息")
@Data
public class CustomerInfo implements Serializable {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名", required = true)
    private String name;

    /**
     * 客户电话
     */
    @ApiModelProperty(value = "客户电话", required = true)
    private String phone;

    /**
     * 区域id
     */
    @ApiModelProperty(value = "配送地址", required = true)
    private Long sysAreaId;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", required = true)
    private String provice;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", required = true)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", required = true)
    private String area;

    /**
     * 小区id
     */
    @ApiModelProperty(value = "小区id")
    private Long residentialQuartersId;

    /**
     * 小区名称
     */
    @ApiModelProperty(value = "小区名称")
    private String residentialQuartersName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址", required = true)
    private String adress;


    @ApiModelProperty(value = "详细地址", required = true)
    private String address;
    /**
     * 站点id
     */
    @ApiModelProperty(value = "站点id", required = true)
    private Long siteId;

    /**
     * 站点名称
     */
    @ApiModelProperty(value = "站点名称", required = true)
    private String siteName;

    /**
     * 配送员id
     */
    @ApiModelProperty(value = "配送员id", required = true)
    private Long distributionId;

    /**
     * 配送员名称
     */
    @ApiModelProperty(value = "配送员名称", required = true)
    private String distributionName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty(value = "上午起送开启状态")
    public Boolean amEnable = Boolean.FALSE;
    @ApiModelProperty(value = "上午起送时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public LocalDate amSendDate;
    @ApiModelProperty(value = "上午配送鲜奶详情")
    public List<MilkDistributionDTO> amMilkDistributionList;
    @ApiModelProperty(value = "下午起送开启状态")
    public Boolean pmEnable = Boolean.FALSE;
    @ApiModelProperty(value = "下午起送时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    public LocalDate pmSendDate;
    @ApiModelProperty(value = "下午配送鲜奶详情")
    public List<MilkDistributionDTO> pmMilkDistributionList;
    @ApiModelProperty(value = "客户名称")
    private String customerName;
    @ApiModelProperty(value = "客户手机号")
    private String customerPhone;

    @ApiModelProperty(value = "上午停奶开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate amStopStartDate;
    @ApiModelProperty(value = "上午停奶结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate amStopEndDate;
    @ApiModelProperty(value = "下午停奶开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate pmStopStartDate;

    @ApiModelProperty(value = "下午停奶结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate pmStopEndDate;

    @ApiModelProperty("省")
    private String province;

    /**
     * 鲜奶数量
     */
    @ApiModelProperty(name = "鲜奶数量", notes = "")
    private Integer freshMilkQuantity;
    /**
     * 鲜奶订购数量
     */
    @ApiModelProperty(name = "鲜奶订购数量", notes = "")
    private Integer orderQuantity;
    /**
     * 鲜奶赠送数量
     */
    @ApiModelProperty(name = "鲜奶赠送数量", notes = "")
    private Integer freshMilkGiveQuantity;
    /**
     * 鲜奶已送数量
     */
    @ApiModelProperty(name = "鲜奶已送数量", notes = "")
    private Integer freshMilkSentQuantity;

    @ApiModelProperty("常温奶赠送数量")
    private Integer longMilkGiveQuantity;

    private BigDecimal amount;


    private List<CustomerDistributioninfoDTO.CheckboxList> checkboxList;

    private Long addressId;

    @ApiModelProperty("修改历史")
    private AlterRecord alterRecord;
    @ApiModelProperty("上午排奶修改历史")
    private List<MilkDistributionDTO> alterAmTaste;
    @ApiModelProperty("下午派奶修改历史")
    private List<MilkDistributionDTO> alterPmTaste;

    private CustomerDistributioninfoDTO customerDistributioninfoDTO;


    @Data
    public static class CheckboxList implements Serializable {

        private String name;
        private Boolean checked;
        private Boolean disabled;
    }


    @ApiModelProperty("订单id")
    private Long orderId;
}
