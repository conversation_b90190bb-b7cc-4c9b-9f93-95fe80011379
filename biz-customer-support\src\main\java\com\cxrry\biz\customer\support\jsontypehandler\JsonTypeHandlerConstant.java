package com.cxrry.biz.customer.support.jsontypehandler;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.handlers.AbstractJsonTypeHandler;
import com.cxrry.biz.customer.support.domain.json.FileUrlConfig;
import com.cxrry.biz.customer.support.domain.json.OperatorMessageConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderAcceptorConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderButtonConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderDurationConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderOrderMenuConfig;
import com.cxrry.biz.customer.support.domain.json.WorkOrderTotalDurationConfig;
import java.util.List;
import org.apache.ibatis.type.TypeHandler;

public class JsonTypeHandlerConstant {

	public static class WorkOrderDurationConfigTypeHandler extends AbstractJsonTypeHandler<List<WorkOrderDurationConfig>> {

		@Override
		protected List<WorkOrderDurationConfig> parse(String json) {
			return JSONUtil.toList(json, WorkOrderDurationConfig.class);
		}

		@Override
		protected String toJson(List<WorkOrderDurationConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

	public static class WorkOrderAcceptorConfigHandler extends AbstractJsonTypeHandler<List<WorkOrderAcceptorConfig>> {

		@Override
		protected List<WorkOrderAcceptorConfig> parse(String json) {
			return JSONUtil.toList(json, WorkOrderAcceptorConfig.class);
		}

		@Override
		protected String toJson(List<WorkOrderAcceptorConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

	public static class WorkOrderButtonConfigHandler extends AbstractJsonTypeHandler<List<WorkOrderButtonConfig>>  {

		@Override
		protected List<WorkOrderButtonConfig> parse(String json) {
			return JSONUtil.toList(json, WorkOrderButtonConfig.class);
		}

		@Override
		protected String toJson(List<WorkOrderButtonConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

	public static class WorkOrderTotalDurationConfigHandler extends AbstractJsonTypeHandler<List<WorkOrderTotalDurationConfig>> {

		@Override
		protected List<WorkOrderTotalDurationConfig> parse(String json) {
			return JSONUtil.toList(json, WorkOrderTotalDurationConfig.class);
		}

		@Override
		protected String toJson(List<WorkOrderTotalDurationConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

  public static class   WorkOrderOrderMenuConfigHandler extends AbstractJsonTypeHandler<List<WorkOrderOrderMenuConfig>> {

		@Override
		protected List<WorkOrderOrderMenuConfig> parse(String json) {
			return JSONUtil.toList(json, WorkOrderOrderMenuConfig.class);
		}

		@Override
		protected String toJson(List<WorkOrderOrderMenuConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

  public static class FileUrlConfigHandler  extends AbstractJsonTypeHandler<List<FileUrlConfig>>{

		@Override
		protected List<FileUrlConfig> parse(String json) {
			return JSONUtil.toList(json, FileUrlConfig.class);
		}

		@Override
		protected String toJson(List<FileUrlConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}

  public static class OperatorMessageHandler extends AbstractJsonTypeHandler<List<OperatorMessageConfig>> {

		@Override
		protected List<OperatorMessageConfig> parse(String json) {
			return JSONUtil.toList(json, OperatorMessageConfig.class);
		}

		@Override
		protected String toJson(List<OperatorMessageConfig> obj) {
			return JSONUtil.toJsonStr(obj);
		}
	}
}
