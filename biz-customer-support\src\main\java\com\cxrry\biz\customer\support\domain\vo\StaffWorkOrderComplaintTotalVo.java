package com.cxrry.biz.customer.support.domain.vo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class StaffWorkOrderComplaintTotalVo {
  @ApiModelProperty(value = "排序")
  private Integer RowNum;
  @ApiModelProperty(value = "区域")
  private String complaintObjectRegionName;
  @ApiModelProperty(value = "站点")
  private String complaintObjectSiteName;
  @ApiModelProperty(value = "投诉对象")
  private String complaintObjectName;
  @ApiModelProperty(value = "数量")
  private String totalCount;
  @ApiModelProperty(value = "投诉对象id")
  private Long complaintObjectId;
}
