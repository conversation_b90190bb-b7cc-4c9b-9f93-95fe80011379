package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderReportTotalPageBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderReportTotalPageVo;
import com.cxrry.biz.customer.support.service.WorkOrderReportTotalService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
        value = "工单统计报表相关",
        tags = {"工单统计报表相关"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderReportTotal")
public class WorkOrderReportTotalController {

    private final WorkOrderReportTotalService workOrderReportTotalService;



    @ApiOperation("分页")
    @SaCheckPermission("support:workOrderReportTotal:groupPage")
    @PostMapping("/groupPage")
    public R<PageTableDataInfo<WorkOrderReportTotalPageVo>> groupPage(@RequestBody WorkOrderReportTotalPageBo bo) {
        return R.ok(workOrderReportTotalService.groupPage(bo));
    }

    @ApiOperation("统计")
    @SaCheckPermission("support:workOrderReportTotal:groupTotal")
    @PostMapping("/groupTotal")
    public R<WorkOrderReportTotalPageVo> groupTotal(@RequestBody WorkOrderReportTotalPageBo bo) {
        return R.ok(workOrderReportTotalService.groupTotal(bo));
    }

    @ApiOperation("导出")
    @SaCheckPermission("support:workOrderReportTotal:export")
    @PostMapping("/export")
    public void workOrderPage(@RequestBody WorkOrderReportTotalPageBo bo, HttpServletResponse response) throws IOException {
        workOrderReportTotalService.export(bo,response);
    }


}
