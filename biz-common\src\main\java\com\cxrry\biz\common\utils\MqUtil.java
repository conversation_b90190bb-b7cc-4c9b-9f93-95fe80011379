package com.cxrry.biz.common.utils;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2022/7/8 16:03
 */
@Slf4j
@Component
public class MqUtil {

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    private static List<Integer> defaultLevel;

    static {
        defaultLevel = new ArrayList<>();
        defaultLevel.add(1);
        defaultLevel.add(5);
        defaultLevel.add(10);
        defaultLevel.add(30);
        defaultLevel.add(60);
        defaultLevel.add(120);
        defaultLevel.add(180);
        defaultLevel.add(240);
        defaultLevel.add(300);
        defaultLevel.add(360);
        defaultLevel.add(420);
        defaultLevel.add(480);
        defaultLevel.add(540);
        defaultLevel.add(600);
        defaultLevel.add(1200);
        defaultLevel.add(1800);
        defaultLevel.add(3600);
        defaultLevel.add(7200);
    }

    public static Integer calculateDefault(long second) {
        Integer level = null;
        for (int i = defaultLevel.size() - 1; i >= 0; i--) {
            int l = (int) second / defaultLevel.get(i);
            if (l > 0 && level == null) {
                level = i;
            }
            if (level == null) {
                continue;
            }
            if (level < i) {
                break;
            }
        }
        if (level == null) {
            return 1;
        } else {
            return level + 1;
        }

    }

    public SendResult sendDelayMessage(String topic, String tag, String content, int second) {
        return sendDelayMessage(topic, tag, content, DateUtil.offsetMinute(new Date(), 1));
    }

    public SendResult sendDelayMessage(String topic, String tag, String content, Duration duration) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTags(tag);
        message.setBody(content.getBytes(StandardCharsets.UTF_8));
        return sendDelayMessage(message, new Date(System.currentTimeMillis() + duration.getSeconds() * 1000L));
    }

    /**
     * 发送延迟消息
     *
     * @param topic
     * @param tag
     * @param content
     * @param startSendTime 触发时间节点
     * @return
     */
    public SendResult sendDelayMessage(String topic, String tag, String content, Date startSendTime) {
        Message message = new Message();
        message.setTopic(topic);
        message.setTags(tag);
        message.setBody(content.getBytes(StandardCharsets.UTF_8));
        return sendDelayMessage(message, startSendTime);
    }

    /**
     * @param msg
     * @param startSendTime
     * @return
     */
    public SendResult sendDelayMessage(Message msg, Date startSendTime) {
        // 延迟时间为空 则不延迟，直接发送
        if (null == startSendTime) {
            return this.sendSyncMessage(msg);
        }
        long l = Duration.between(Instant.now(), startSendTime.toInstant()).getSeconds();
        Integer level = this.calculateDefault(l);

        log.info("===========================l={},level={}", l, level);

        if (l <=  2 * 60 * 60L) {
            msg.setDelayTimeLevel(level);
            return this.sendDelaySyncMessage(msg);

        } else {
//            fillMessage(msg, level, startSendTime);
//            return this.sendDelaySyncMessage(msg);
//            RocketmqDelayMessage rocketmqDelayMessage = new RocketmqDelayMessage();
//            rocketmqDelayMessage.setTopic(msg.getTopic());
//            rocketmqDelayMessage.setTag(msg.getTags());
//            rocketmqDelayMessage.setMessageStatus(RocketMessageStatus.DID_NOT_DELIVER.getValue());
//            rocketmqDelayMessage.setContent(new String(msg.getBody()));
//            rocketmqDelayMessage.setDelayTime(startSendTime.getTime());
//            asyncMessageFailureService.saveRocketmqDelayMessage(rocketmqDelayMessage);
            return new SendResult(SendStatus.SEND_OK, null, null, null, 0);
        }

    }

    /**
     * 发送扩展同步消息 消息失败后记录到 rocketmq_message_failure 表中，由定时任务重新加入到rocketmq中
     *
     * @return SendResult
     */
    public SendResult sendDelaySyncMessage(Message msg) {
        try {
            DefaultMQProducer producer = rocketMQTemplate.getProducer();
            SendResult sendResult = producer.send(msg, producer.getSendMsgTimeout());
            // 同步发送消息，只要不抛异常就是成功
            if (sendResult != null) {
                log.info("发送延迟MQ消息成功 -- mes = {}", msg);
            }
            return sendResult;
        } catch (Exception e) {
            log.error("发送延迟MQ消息失败 -- mes = {}", msg);
            log.error("Exception -- : ", e);
            // 消息发送失败，需要进行重试处理，可重新发送这条消息或持久化这条数据进行补偿处理
//            RocketmqDelayMessage rocketmqDelayMessage = new RocketmqDelayMessage();
//            rocketmqDelayMessage.setTopic(msg.getTopic());
//            rocketmqDelayMessage.setTag(msg.getTags());
//            rocketmqDelayMessage.setMessageStatus(RocketMessageStatus.DID_NOT_DELIVER.getValue());
//            rocketmqDelayMessage.setContent(new String(msg.getBody()));
//            rocketmqDelayMessage.setExceptionMessage(getErrorMessage(e));
//
//            asyncMessageFailureService.saveRocketmqDelayMessage(rocketmqDelayMessage);
        }
        return null;
    }


    /**
     * 发送同步消息
     *
     * @return
     */
    public SendResult sendSyncMessage(String topic, String message) {
        return this.sendSyncMessage(topic, null, message);
    }

    /**
     * 发送同步消息
     *
     * @return
     */
    public SendResult sendSyncMessage(Message msg) {
        return this.sendSyncMessage(msg.getTopic(), msg.getTags(), new String(msg.getBody()));
    }

    public SendResult sendSyncMessage(String topic, String tag, Object obj) {
       return sendSyncMessage(topic,tag, JSONObject.toJSONString(obj));
    }



    /**
     * 发送同步消息 消息失败后记录到 rocketmq_message_failure 表中，由定时任务重新加入到rocketmq中
     *
     * @return SendResult
     */
    public SendResult sendSyncMessage(String topic, String tag, String content) {

        try {
            String destination = "";
            if (StrUtil.isNotBlank(tag)) {
                destination = StrUtil.format("{}:{}", topic, tag);
            } else {
                destination = StrUtil.format("{}", topic);
            }

            SendResult sendResult = rocketMQTemplate.syncSend(destination, content);
            // 同步发送消息，只要不抛异常就是成功
            if (sendResult != null) {
                log.info("发送MQ消息成功 -- Topic:{},msgId:{},tag:{},body:{}", topic, sendResult.getMsgId(), tag,
                    content);
            }
            return sendResult;
        } catch (Exception e) {
            log.error("发送MQ消息失败 -- Topic:{},tag:{},body:{}", topic, tag, content);
            log.error("Exception -- : ", e);
            // 消息发送失败，需要进行重试处理，可重新发送这条消息或持久化这条数据进行补偿处理
//            RocketmqMessageFailure rocketmqMessageFailure = new RocketmqMessageFailure();
//            rocketmqMessageFailure.setTopic(topic);
//            rocketmqMessageFailure.setTag(tag);
//            rocketmqMessageFailure.setContent(content);
//            rocketmqMessageFailure.setMessageStatus(RocketMessageStatus.DID_NOT_DELIVER.getValue());
//            rocketmqMessageFailure.setExceptionMessage(getErrorMessage(e));
//            asyncMessageFailureService.saveRocketmqMessageFailure(rocketmqMessageFailure);
        }
        return null;
    }


    private String getErrorMessage(Exception ex) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw, true);
        ex.printStackTrace(pw);
        pw.flush();
        sw.flush();
        return sw.toString();
    }


}
