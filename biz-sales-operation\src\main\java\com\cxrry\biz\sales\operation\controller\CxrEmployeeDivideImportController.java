package com.cxrry.biz.sales.operation.controller;

import com.cxrry.biz.sales.operation.service.CxrEmployeeDivideImportService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDate;

@Slf4j
@Api(tags = {"销售代理分成导入模块"})
@RefreshScope
@RequiredArgsConstructor
@RestController()
@RequestMapping("/employeeDivideImport")
public class CxrEmployeeDivideImportController {

    private final CxrEmployeeDivideImportService employeeDivideImportService;
    @ApiOperation("上月负分成 导入")
    @PostMapping("/importNegativeExpense")
    public R importNegativeExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importNegativeExpense(month,file));
    }

    @ApiOperation("吃住空调 导入")
    @PostMapping("/importAccommodationExpense")
    public R importAccommodationExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importAccommodationExpense(month,file));
    }

    @ApiOperation("平台维护费 导入")
    @PostMapping("/importPlatformExpense")
    public R importPlatformExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importPlatformExpense(month,file));
    }

    @ApiOperation("保证金 导入")
    @PostMapping("/importEarnestMoneyExpense")
    public R importEarnestMoneyExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("earnestMoneyType") Integer earnestMoneyType,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importEarnestMoneyExpense(month,file,earnestMoneyType));
    }

    @ApiOperation("合作中退款提成 导入")
    @PostMapping("/importRefundExpense")
    public R importRefundExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importRefundExpense(month,file));
    }

    @ApiOperation("补贴修改 导入")
    @PostMapping("/importSubsidyExpense")
    public R importSubsidyExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importSubsidyExpense(month,file));
    }

    @ApiOperation("活动影响费用 导入")
    @PostMapping("/importMarketingExpense")
    public R importMarketingExpense(
            @NotNull(message = "月份不能为空")
            @RequestParam("month") String month,
            @RequestParam("file") MultipartFile file) throws IOException {
        if (file.getSize() == 0) {
            return R.fail("上传的文件为空!");
        }
        return R.ok(employeeDivideImportService.importMarketingExpense(month,file));
    }

}
