package com.cxrry.biz.common.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cxrry.biz.common.config.LianLuSmsProperties;
import com.ruoyi.business.base.api.domain.CxrMobileVerificationCode;
import com.ruoyi.business.base.api.dubbo.RemoteMobileCodeService;
import com.ruoyi.common.core.enums.DeleteStatus;
import com.ruoyi.common.sms.config.properties.WarnUserConfig;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SmsBizSendUtil {

  @Resource
  private LianLuSmsProperties lianLuSmsProperties;
  @Resource
  private WarnUserConfig warnUserConfig;


  @Autowired
  @Qualifier("futureThreadPool")
  private ThreadPoolTaskExecutor threadPoolTaskExecutor;

  @DubboReference
  private RemoteMobileCodeService  remoteMobileCodeService;

  /**
   * 发送内部人员信息
   */
  public void sendInternalUserSms(String smsContent) {
    if(warnUserConfig.getOpenValue()!=null&&warnUserConfig.getOpenValue()==1){
      asyncSendListPhoneMsg(warnUserConfig.getPhones(), smsContent);
    }
  }

  /**
   *
   * @param phone
   * @param smsContent
   * @return
   */

  public Map<String, Object> getSendSmsParams(String phone, String smsContent) {
    String mchId = lianLuSmsProperties.getMchId();
    String appId = lianLuSmsProperties.getAppId();
    String appKey = lianLuSmsProperties.getAppKey();
    String signName = lianLuSmsProperties.getSignName();
    String version = lianLuSmsProperties.getVersion();
    String signType = lianLuSmsProperties.getSignType();
    String type = lianLuSmsProperties.getType();
    Map<String, Object> params = new HashMap<>();
    params.put("Type", type);
    List<String> list = new ArrayList<>();
    list.add(phone);
    params.put("PhoneNumberSet", list);
    params.put("AppId", appId);
    params.put("Version", version);
    params.put("MchId", mchId);
    params.put("Signature", "xxxxxxx");
    params.put("SessionContext", smsContent);
    params.put("SignType", signType);
    params.put("TimeStamp", System.currentTimeMillis());
    params.put("SignName", signName);
    String sign = null;
    try {
      sign = this.sign(params, appKey);
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
    }
    if (Objects.nonNull(sign)) {
      params.put("Signature", sign);
    }
    return params;
  }

  public String sign(Map<String, Object> params, String appKey) throws NoSuchAlgorithmException {
    // 请求中所有参数名排序，参数过滤后按顺序拼接
    String str = params.entrySet().stream()
        .filter(e -> (!e.getKey().equals("Signature")
            && !e.getKey().equals("SessionContext")
            && !e.getKey().equals("PhoneNumberSet")
            && !e.getKey().equals("SessionContextSet")
            && !e.getKey().equals("ContextParamSet")
            && !e.getKey().equals("TemplateParamSet")
            && !e.getKey().equals("PhoneList")
            && !e.getKey().equals("phoneSet")))
        .sorted(Map.Entry.comparingByKey())
        .map(e -> e.getKey() + "=" + e.getValue())
        // 结尾拼接上appkey
        .collect(Collectors.joining("&")) + "&key=" + appKey;
    // 拼接后字符串MD5加密
    MessageDigest md = MessageDigest.getInstance("MD5");
    md.update(str.getBytes());
    byte[] digest = md.digest();
    StringBuilder sb = new StringBuilder();
    for (byte b : digest) {
      sb.append(String.format("%02x", b & 0xff));
    }
    // 加密后大写
    return sb.toString().toUpperCase();
  }

  public JSONObject sendPost(Map<String, Object> sendSmsParams, String reqURL) {
    String paramsStr = JSONUtil.toJsonStr(sendSmsParams);
    String responseMsg = HttpUtil.post(reqURL, paramsStr);
    JSONObject jsonObject = JSONUtil.parseObj(responseMsg);
    log.info("\n短信请求URL:{}\n请求参数：{}\n请求结果：{}", reqURL, sendSmsParams, jsonObject);
    String status = jsonObject.getStr("status");
    Integer sendFlag = status.equals("00")?0:1;
    CxrMobileVerificationCode cxrMobileVerificationCode = new CxrMobileVerificationCode();
    cxrMobileVerificationCode.setPhone((String) ((List)sendSmsParams.get("PhoneNumberSet")).get(0));
    cxrMobileVerificationCode.setVerificationCode(sendSmsParams.get("SessionContext").toString());
//        cxrMobileVerificationCode.setRequestIp(RequestServlet.getRem.getRemoteAddr());
//        log.info("验证码{}", getrandom);
    // 把用户登录的验证存到数据库中
    cxrMobileVerificationCode.setRevision(1L);
    cxrMobileVerificationCode.setSortNum(1L);
    cxrMobileVerificationCode.setCreateTime(new Date());
    //
    // cxrMobileVerificationCode.setCreateBy(CustomerLoginHelper.getEmployeeId());
    //                cxrMobileVerificationCode.setSysDeptId(CustomerLoginHelper.getDeptId());
//        cxrMobileVerificationCode.setCreateByType(T);
    //
    // cxrMobileVerificationCode.setCreateByName(CustomerLoginHelper.getUsername());
    cxrMobileVerificationCode.setStatus(sendFlag);
    cxrMobileVerificationCode.setDeleteStatus(DeleteStatus.NOT_DELETED.getValue());
    remoteMobileCodeService.addCode(cxrMobileVerificationCode);
    return jsonObject;
  }

  public void asyncSendPhoneMsg(String phone, String msg) {
    CompletableFuture.runAsync(() -> {
      String sendUrl = lianLuSmsProperties.getSendUrl();
      Map<String, Object> sendSmsParams = this.getSendSmsParams(phone, msg);
      this.sendPost(sendSmsParams, sendUrl);
    }, threadPoolTaskExecutor);
  }

  public void asyncSendListPhoneMsg(List<String> phones, String msg) {
    CompletableFuture.runAsync(() -> {
      for (String phone : phones) {
        String sendUrl = lianLuSmsProperties.getSendUrl();
        Map<String, Object> sendSmsParams = this.getSendSmsParams(phone, msg);
        this.sendPost(sendSmsParams, sendUrl);
      }
    }, threadPoolTaskExecutor);
  }

}