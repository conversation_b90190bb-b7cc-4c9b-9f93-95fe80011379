package com.cxrry.biz.customer.support.domain.dto;


import com.baomidou.mybatisplus.annotation.TableId;
import com.cxrry.biz.common.json.JudgmentValue;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TriggerActiveDto implements Serializable{

    private Long id;

    private Long triggerId;

    /**
     * 执行动作类型 1.给受理方发送短信消息 2.给受理方发送公众号消息 3.更改工单受理人 4.添加关注人
     */
    private Integer activeType;

    /**
     *
     */
    private List<JudgmentValue> targetIdList;

    /**
     * 公众号模板类型  1.投诉 2.退款
     */
    private Integer wxMpTmpType;
    /**
     * 类型 1用户ID、2职位ID、3角色ID
     */
    private Integer followerType;

    private List<String> username;



}
