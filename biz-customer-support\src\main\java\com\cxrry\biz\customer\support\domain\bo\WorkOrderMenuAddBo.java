package com.cxrry.biz.customer.support.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;

@Data
public class WorkOrderMenuAddBo implements Serializable {
  @ApiModelProperty("父级id")
  private Long parentId;

  @ApiModelProperty("组级列表")
  private String ancestors;
  /**
   * 菜单名
   */
  @ApiModelProperty("菜单名")
  private String menuName;
  /**
   * 菜单顺序
   */
  @ApiModelProperty("菜单级别")
  private Integer menuNum;

  @ApiModelProperty("菜单排序")
  private Integer sort;


}
