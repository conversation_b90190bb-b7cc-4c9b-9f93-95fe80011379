package com.cxrry.biz.customer.support.mapper.erp;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.cxrry.biz.customer.support.constants.IConst;
import com.cxrry.biz.customer.support.domain.SysUserPost;
import com.cxrry.biz.customer.support.domain.vo.DeptVos;
import com.cxrry.biz.customer.support.domain.vo.SiteDirectorUserInfo;
import com.cxrry.biz.customer.support.domain.vo.SysUserVosPlus;
import com.cxrry.biz.customer.support.domain.vo.UserInfoServiceVo;
import com.ruoyi.common.mybatis.core.mapper.BaseMapperPlus;
import com.ruoyi.system.api.domain.SysDept;
import com.ruoyi.system.api.domain.SysUserVos;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 部门管理 数据层
 *
 * <AUTHOR> Li
 */
@DS(value = IConst.DS_ERP)
@Mapper
public interface SysDeptMapper extends BaseMapperPlus<SysDeptMapper, SysDept, SysDept> {

  SysDept queryById(Long id);

  List<SysUserPost> getUserPosts(Long operatorId);

  SiteDirectorUserInfo getSiteDirectorUserInfo(Long siteId);

  List<Long> getReginDirectorUserInfo(Long acceptId);

  UserInfoServiceVo selectSysUser(Long value);

  List<SysUserVosPlus> getUserListVoList(@Param("userIds") List<Long> userIds);

  SysDept getMaxRootDept(Long cxrSiteId);

  DeptVos queryByIdDeptVos(@Param("id") Long id);

  SysUserVosPlus getUserByIdSysUserVos(@Param("userId") Long userId);
}
