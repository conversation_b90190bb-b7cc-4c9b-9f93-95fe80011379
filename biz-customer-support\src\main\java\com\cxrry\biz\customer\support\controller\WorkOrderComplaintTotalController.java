package com.cxrry.biz.customer.support.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cxrry.biz.common.web.domain.PageTableDataInfo;
import com.cxrry.biz.customer.support.domain.bo.WorkOrderComplaintTotalBo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderComplaintTotalVo;
import com.cxrry.biz.customer.support.domain.vo.WorkOrderTotalNumberVo;
import com.cxrry.biz.customer.support.service.WorkOrderComplaintTotalService;
import com.ruoyi.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@Api(
    value = "工单排名-投诉统计",
    tags = {"工单排名-投诉统计"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/workOrderComplaintTotal")
public class WorkOrderComplaintTotalController {

  private final WorkOrderComplaintTotalService workOrderComplaintTotalService;

  @ApiOperation("工单排名-投诉统计")
  @SaCheckPermission("support:workOrder:workOrderComplaintTotalPage")
  @PostMapping("/page")
  public R<PageTableDataInfo<WorkOrderComplaintTotalVo>> workOrderComplaintTotalPage(@RequestBody WorkOrderComplaintTotalBo bo) {
    return R.ok(workOrderComplaintTotalService.workOrderComplaintTotalPage(bo));
  }
  @ApiOperation("工单排名-投诉统计底部合计")
  @PostMapping("/total")
  public R<WorkOrderTotalNumberVo> workOrderComplaintTotal(@RequestBody WorkOrderComplaintTotalBo bo) {
    return R.ok(workOrderComplaintTotalService.workOrderComplaintTotal(bo));
  }

  @ApiOperation("工单排名-投诉统计导出")
  @PostMapping("/export")
  public void  export(@RequestBody WorkOrderComplaintTotalBo bo,HttpServletResponse response) throws IOException {
    this.workOrderComplaintTotalService.export(bo,response);
  }




}
