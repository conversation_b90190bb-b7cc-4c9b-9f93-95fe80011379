package com.cxrry.biz.customer.support.domain.bo;

import com.ruoyi.common.mybatis.core.page.PageQuery;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

@Data
public class WorkOrderPageBo extends PageQuery implements Serializable {

  @ApiModelProperty(value = "公司id")
  private Long currentDeptId;
  @ApiModelProperty(value ="工单分类 1.投诉工单 2.退款工单")
  private Integer type;
  @ApiModelProperty(value ="工单编号")
  private String code;
  @ApiModelProperty(value ="大区名称")
  private String rootRegionName;
  @ApiModelProperty(value ="区域名称")
  private String regionName;
  @ApiModelProperty(value ="站点名称")
  private String siteName;
  @ApiModelProperty(value ="最新待受理人用户名称，多个逗号分开")
  private String acceptorStr;
  @ApiModelProperty(value ="最新待受理人用户编号，多个逗号分开")
  private String acceptorNumber;
  @ApiModelProperty(value ="客户登录手机号")
  private String customerLoginPhone;
  @ApiModelProperty(value ="客户主地址手机号")
  private String customerMainPhone;
  @ApiModelProperty(value ="创建人名称")
  private String createByName;
  @ApiModelProperty(value ="关注人")
  private String followerName;
  @ApiModelProperty(value ="1低 2一般 3紧急 4非常紧急")
  private Integer priority;
  @ApiModelProperty(value = "逾期次数 0否 1是")
  private Integer overdueNum;
  @ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
  private Integer processStatus;
  @ApiModelProperty(value = "本单实际完成开始时间")
  private LocalDate actStartFinishTime;
  @ApiModelProperty(value = "本单实际完成结束时间")
  private LocalDate actEndFinishTime;
  @ApiModelProperty(value = "创建开始时间")
  private Date createStartTime;
  @ApiModelProperty(value = "创建结束时间")
  private Date createEndTime;
  @ApiModelProperty(value = "类型 全部传空 1.我关注的 2.我创建的")
  private Integer category;
  @ApiModelProperty(value = "排序 1.高->底 2.底->高")
  private Integer sort;

  private Long userId;

  @ApiModelProperty(value = "操作开始时间")
  private LocalDate operateTimeStartTime;
  @ApiModelProperty(value = "操作结束时间")
  private LocalDate operateTimeEndTime;




}
