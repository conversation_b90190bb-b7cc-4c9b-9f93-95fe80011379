package com.cxrry.biz.common.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.domain.CxrBaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 手机验证码对象 cxr_mobile_verification_code
 *
 * <AUTHOR>
 * @date 2022-07-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cxr_mobile_verification_code")
public class CxrMobileVerificationCode extends CxrBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 手机验证码
     */
    private String verificationCode;

    /**
     * 手机号码
     */
    private String phone;

    /**
     * 有效状态(详情见字典)
     */
    private String validStatus;

    /**
     * 请求ip地址
     */
    private String requestIp;

    /**
     * 发送时间
     */
    private Long date;

    private String thirdId;
    // 0 发送中 1发送失败 2发送成功
    private Integer status;


}
