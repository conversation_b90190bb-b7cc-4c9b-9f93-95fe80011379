package com.cxrry.biz.customer.support.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 工单触发器
 * @TableName work_order_trigger
 */
@TableName(value ="work_order_trigger")
@Data
public class WorkOrderTrigger implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 触发事件
     */
    private String triggerEvent;
    /**
     * 判断类型1工单创建2工单修改
     */
    private Integer judgmentType;
    /**
     * 创建人名称
     */
    private String createByName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人名称
     */
    private String updateByName;
    /**
     * 删除人名称
     */
    private String deleteByName;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态(详情见字典)
     */
    private String deleteStatus;

    /**
     * 启用状态（0正常 1停用）

     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableField(exist = false)
    List<WorkOrderTriggerCondition> triggerConditions = new ArrayList<WorkOrderTriggerCondition>();
    @TableField(exist = false)
    List<WorkOrderTriggerActive> triggerActives = new ArrayList<WorkOrderTriggerActive>();
}