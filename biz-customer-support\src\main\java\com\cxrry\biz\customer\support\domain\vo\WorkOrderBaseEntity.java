package com.cxrry.biz.customer.support.domain.vo;

import com.cxrry.biz.customer.support.enums.workorder.ButtonTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class WorkOrderBaseEntity {
	/**
	 * 回复按钮
	 */
	Boolean replyButton = false;

	/**
	 * 转交按钮
	 */
	Boolean forwardButton = false;

	/**
	 * 完结按钮
	 */
	Boolean completeButton = false;

	//当前受理中的节点ID
	private Long currentAcceptNodeId;

	//工单ID
	private Long workOrderId;


	@ApiModelProperty(value = "受理状态 0待受理 1受理中 2已完结 3.已逾期")
	private Integer processStatus;


	public void enableButtonByType(Integer buttonType){
		if(ButtonTypeEnums.REPLY.getCode().equals(buttonType)){
			this.setReplyButton(true);
		}else if(ButtonTypeEnums.FORWARD.getCode().equals(buttonType)){
			this.setForwardButton(true);
		}else if(ButtonTypeEnums.COMPLETE.getCode().equals(buttonType)){
			this.setCompleteButton(true);
		}
	}
}
